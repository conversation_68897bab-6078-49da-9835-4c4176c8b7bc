# 数据库字段加密 - 生命周期密码管理技术方案

## 🎯 项目概述

### 背景
当前数据库字段加密系统存在用户体验问题：缓存过期时需要用户重新输入密码，打断正常应用流程。


### 核心场景重新定义
1. **应用启动流程**：保持现有逻辑（检查是否设置密码等），需要时才进入锁屏
2. **密码解锁时机**：用户在锁屏界面输入密码解锁，此时缓存密码
3. **异常场景**：后端无法获取缓存密码时，显示密码输入对话框（很少见）

### 目标
- **保持现有流程**：不改变现有的应用启动逻辑和用户引导
- **主流程优化**：基于现有锁屏界面，在解锁时缓存密码
- **优化解锁体验**：在锁屏解锁时缓存密码，减少后续输入
- **异常处理**：密码失效时通过事件驱动的对话框处理
- **最小化改动**：基于现有代码结构，优先复用现有组件



## 🏗️ 架构设计

### 整体架构图

```mermaid
graph TB
    subgraph "应用启动流程 - 保持现有逻辑"
        A[App启动]
        B[检查密码设置状态]
        C{是否设置过密码?}
        D[引导设置密码]
        E{应用是否被锁定?}
        F[进入主界面]
        G[进入锁屏界面]
    end
    
    subgraph "密码解锁流程 - 缓存密码"
        H[用户输入密码]
        I[验证密码]
        J[缓存密码到生命周期管理器]
        K[解锁成功]
    end
    
    subgraph "异常处理流程 - 密码失效"
        L[数据库操作]
        M[密码失效]
        N[事件通知]
        O[显示密码输入对话框]
        P[重新输入密码]
        Q[更新缓存]
    end
    
    A --> B
    B --> C
    C -->|否| D
    C -->|是| E
    E -->|否| F
    E -->|是| G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> F
    
    L --> M
    M --> N
    N --> O
    O --> P
    P --> Q
```

### 核心数据流

```mermaid
sequenceDiagram
    participant U as User
    participant App as App
    participant PM as PasswordManager
    participant LS as LockScreen
    participant LPM as LifecyclePasswordManager
    participant EM as EventManager
    participant DB as Database
    participant PD as PasswordDialog
    
    Note over U,PD: 应用启动流程 - 保持现有逻辑
    U->>App: 启动应用
    App->>PM: hasAppPassword()
    PM->>App: 返回密码设置状态
    
    alt 未设置密码
        App->>U: 显示密码设置引导
        U->>App: 设置密码
        App->>PM: setAppPassword(password)
        PM->>LPM: setLifecyclePassword(password)
        App->>U: 进入主界面
    else 已设置密码且应用被锁定
        App->>LS: 进入锁屏界面
        LS->>U: 显示密码输入
        U->>LS: 输入密码
        LS->>PM: unlockApplication(password)
        PM->>LPM: setLifecyclePassword(password)
        LPM->>EM: emit("password_updated")
        LS->>U: 进入主界面
    else 已设置密码且应用未锁定
        App->>U: 直接进入主界面
    end
    
    Note over U,PD: 正常使用
    U->>DB: 查询加密数据
    DB->>LPM: getPassword()
    LPM->>DB: 返回缓存密码
    DB->>U: 返回解密数据
    
    Note over U,PD: 异常流程：密码失效
    U->>DB: 查询加密数据
    DB->>LPM: getPassword()
    LPM->>LPM: 发现密码失效
    LPM->>EM: emit("password_invalid")
    EM->>PD: 显示密码输入对话框
    PD->>U: 请求重新输入密码
    U->>PD: 输入密码
    PD->>PM: unlockApplication(password)
    PM->>LPM: setLifecyclePassword(password)
    LPM->>EM: emit("password_updated")
    EM->>PD: 隐藏对话框
    PD->>U: 继续之前的操作
```

## 🎨 用户体验流程重新设计

### 1. 应用启动流程（保持现有逻辑）
```
用户启动应用
    ↓
检查密码设置状态 (hasAppPassword())
    ↓
┌─────────────────────────────────────────────┐
│  未设置密码                                    │
│      ↓                                      │
│  显示密码设置引导/对话框                        │
│      ↓                                      │
│  用户设置密码                                │
│      ↓                                      │
│  自动缓存密码到 LifecyclePasswordManager      │
│      ↓                                      │
│  进入主界面                                  │
└─────────────────────────────────────────────┘
    ↓
┌─────────────────────────────────────────────┐
│  已设置密码                                    │
│      ↓                                      │
│  检查应用锁定状态                              │
│      ↓                                      │
│  ┌─────────────────┐  ┌─────────────────┐    │
│  │  应用被锁定        │  │  应用未锁定        │    │
│  │      ↓           │  │      ↓           │    │
│  │  进入锁屏界面      │  │  直接进入主界面    │    │
│  │      ↓           │  │                 │    │
│  │  用户输入密码      │  │                 │    │
│  │      ↓           │  │                 │    │
│  │  缓存密码          │  │                 │    │
│  │      ↓           │  │                 │    │
│  │  进入主界面        │  │                 │    │
│  └─────────────────┘  └─────────────────┘    │
└─────────────────────────────────────────────┘
```

### 2. 异常流程：密码失效
```
用户正常使用应用
    ↓
后台数据库操作需要密码
    ↓
LifecyclePasswordManager 发现密码失效
    ↓
发送 password_invalid 事件
    ↓
前端显示 password-input-dialog
    ↓
用户重新输入密码
    ↓
调用 unlockApplication(password) 更新缓存
    ↓
隐藏对话框，继续之前的操作
```

### 3. 应用锁定流程
```
用户手动锁定应用 / 自动锁定触发
    ↓
调用 lockApplication()
    ↓
清除 LifecyclePasswordManager 缓存
    ↓
发送 password_cleared 事件
    ↓
前端导航到锁屏界面
```

## 🔧 核心组件设计

### 1. 后端组件设计

#### PasswordManager 扩展（基于现有API）
```go
// 现有API保持不变
func (p *PasswordManager) UnlockApplication(password string) error {
    // 伪代码：
    // 1. 验证密码
    // 2. 调用 lifecycleManager.SetPassword(password) - 关键改动
    // 3. 设置加密器缓存
    // 4. 初始化数据库
}

// 新增API
func (p *PasswordManager) LockApplication() error {
    // 伪代码：
    // 1. 调用 lifecycleManager.ClearPassword()
    // 2. 清除加密器缓存
    // 3. 发送 password_cleared 事件
}

func (p *PasswordManager) GetPasswordStatus() PasswordStatus {
    // 伪代码：
    // 1. 检查 lifecycleManager.IsPasswordCached()
    // 2. 返回状态（locked/unlocked/invalid）
}
```

#### LifecyclePasswordManager（新增核心组件）
```go
type LifecyclePasswordManager struct {
    // 线程安全的密码缓存
    cachedPassword    string
    isPasswordValid   bool
    lastValidation    time.Time
    mutex            sync.RWMutex
    
    // 事件通知
    eventManager     *events.EventManager
    
    // 异常处理
    maxEventRetries  int
    eventTimeout     time.Duration
    
    // 生命周期管理
    isShuttingDown   bool
    shutdownChan     chan struct{}
}

func (lpm *LifecyclePasswordManager) SetPassword(password string) error {
    // 伪代码：
    // 1. 线程安全地验证密码
    // 2. 设置缓存和有效标志
    // 3. 安全地发送 password_updated 事件
    // 4. 更新最后验证时间
}

func (lpm *LifecyclePasswordManager) GetPassword() (string, error) {
    // 伪代码：
    // 1. 线程安全地检查缓存状态
    // 2. 如果无效，发送 password_invalid 事件
    // 3. 返回 PasswordRequiredError 或缓存密码
}

func (lpm *LifecyclePasswordManager) ClearPassword() {
    // 伪代码：
    // 1. 线程安全地清除缓存
    // 2. 安全地发送 password_cleared 事件
    // 3. 重置状态
}

func (lpm *LifecyclePasswordManager) ValidatePassword() error {
    // 伪代码：
    // 1. 主动验证当前密码有效性
    // 2. 如果失效，发送 password_invalid 事件
    // 3. 更新状态
}
```

### 2. 前端组件设计

#### lock-screen.jsx 扩展（基于现有组件）
```jsx
// 基于现有组件的关键修改
export default function LockScreen() {
    const { unlockApplication } = useAppPassword();
    // ... 现有状态和逻辑保持不变
    
    const handleSubmit = async (event) => {
        event.preventDefault();
        
        // 关键改动：使用 unlockApplication 会自动缓存密码
        const unlockSuccess = await unlockApplication(password);
        
        if (unlockSuccess) {
            // 伪代码：密码已自动缓存到 LifecyclePasswordManager
            unlockScreen();
            navigate(returnPath, { replace: true });
        } else {
            // 现有的错误处理逻辑保持不变
            handlePasswordError();
        }
    };
    
    // 监听密码事件（新增）
    useEffect(() => {
        const handlePasswordCleared = () => {
            // 伪代码：如果密码被清除，确保停留在锁屏界面
            if (location.pathname !== '/lock') {
                navigate('/lock', { replace: true });
            }
        };
        
        // 监听密码清除事件
        EventsOn('password_cleared', handlePasswordCleared);
        
        return () => {
            EventsOff('password_cleared');
        };
    }, []);
    
    // 其他现有逻辑保持不变
}
```

#### password-input-dialog.jsx（新增异常处理组件）
```jsx
// 专门用于异常情况的密码输入对话框
export function PasswordInputDialog({ 
    open, 
    onClose, 
    eventData, 
    onSuccess 
}) {
    const { unlockApplication } = useAppPassword();
    const [password, setPassword] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    
    const handleSubmit = async (e) => {
        e.preventDefault();
        setIsLoading(true);
        setError('');
        
        try {
            // 伪代码：使用相同的 unlockApplication 方法
            const success = await unlockApplication(password);
            
            if (success) {
                onSuccess?.();
                onClose();
            } else {
                setError('密码验证失败，请重试');
            }
        } catch (err) {
            setError(err.message || '密码验证异常');
        } finally {
            setIsLoading(false);
        }
    };
    
    const getDialogContent = () => {
        // 伪代码：根据事件类型显示不同提示
        const eventType = eventData?.type || 'password_invalid';
        
        switch (eventType) {
            case 'password_invalid':
                return {
                    title: '密码失效',
                    message: '当前密码已失效，请重新输入密码以继续操作',
                    severity: 'warning'
                };
            case 'password_required':
                return {
                    title: '需要密码',
                    message: '系统需要验证您的密码以继续操作',
                    severity: 'info'
                };
            default:
                return {
                    title: '身份验证',
                    message: '请输入密码以继续',
                    severity: 'info'
                };
        }
    };
    
    return (
        <Dialog open={open} maxWidth="sm" fullWidth>
            {/* 伪代码：类似现有密码对话框的UI结构 */}
            <DialogContent>
                <Alert severity={getDialogContent().severity}>
                    {getDialogContent().message}
                </Alert>
                
                <TextField
                    type="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    disabled={isLoading}
                    error={!!error}
                    helperText={error}
                />
                
                <Button 
                    onClick={handleSubmit} 
                    disabled={!password || isLoading}
                >
                    {isLoading ? '验证中...' : '确认'}
                </Button>
            </DialogContent>
        </Dialog>
    );
}
```

#### useAppPassword Hook 扩展
```javascript
// 基于现有hook的扩展
export function useAppPassword() {
    // 现有功能保持不变
    const existing = useExistingHook();
    
    // 新增状态
    const [passwordStatus, setPasswordStatus] = useState('unknown');
    const [showPasswordDialog, setShowPasswordDialog] = useState(false);
    const [passwordEvent, setPasswordEvent] = useState(null);
    
    // 新增方法
    const lockApplication = useCallback(async () => {
        try {
            await AppPasswordService.LockApplication();
            return true;
        } catch (error) {
            console.error('锁定应用失败:', error);
            return false;
        }
    }, []);
    
    const getPasswordStatus = useCallback(async () => {
        try {
            const status = await AppPasswordService.GetPasswordStatus();
            setPasswordStatus(status.state);
            return status;
        } catch (error) {
            console.error('获取密码状态失败:', error);
            return { state: 'unknown' };
        }
    }, []);
    
    // 事件监听
    useEffect(() => {
        const handlePasswordRequired = (data) => {
            setPasswordEvent({ type: 'password_required', data });
            setShowPasswordDialog(true);
        };
        
        const handlePasswordInvalid = (data) => {
            setPasswordEvent({ type: 'password_invalid', data });
            setShowPasswordDialog(true);
        };
        
        const handlePasswordUpdated = (data) => {
            setShowPasswordDialog(false);
            setPasswordEvent(null);
            setPasswordStatus('unlocked');
        };
        
        const handlePasswordCleared = (data) => {
            setPasswordStatus('locked');
            setShowPasswordDialog(false);
            setPasswordEvent(null);
        };
        
        // 伪代码：监听wails3事件
        EventsOn('password_required', handlePasswordRequired);
        EventsOn('password_invalid', handlePasswordInvalid);
        EventsOn('password_updated', handlePasswordUpdated);
        EventsOn('password_cleared', handlePasswordCleared);
        
        return () => {
            EventsOff('password_required');
            EventsOff('password_invalid');
            EventsOff('password_updated');
            EventsOff('password_cleared');
        };
    }, []);
    
    return {
        ...existing,
        
        // 新增API
        lockApplication,
        getPasswordStatus,
        
        // 新增状态
        passwordStatus,
        showPasswordDialog,
        passwordEvent,
        
        // 事件控制
        hidePasswordDialog: () => setShowPasswordDialog(false),
    };
}
```


## 🔌 API设计

### 后端API（基于现有扩展）
```go
// 现有API保持不变，功能增强
type PasswordManager interface {
    // 现有API - 功能增强
    UnlockApplication(password string) error  // 现在会自动缓存密码
    
    // 新增API
    LockApplication() error
    GetPasswordStatus() PasswordStatus
    IsApplicationUnlocked() bool
}

type PasswordStatus struct {
    State       string `json:"state"`        // "locked", "unlocked", "invalid"
    HasPassword bool   `json:"has_password"`
    Message     string `json:"message"`
    LastUpdate  int64  `json:"last_update"`
}
```

### 前端API（基于现有hook扩展）
```javascript
const {
    // 现有API保持不变
    hasPassword,
    setPassword,
    unlockApplication,  // 现在会自动缓存密码
    verifyPassword,
    changePassword,
    
    // 新增API
    lockApplication,
    getPasswordStatus,
    
    // 新增状态
    passwordStatus,
    showPasswordDialog,
    passwordEvent,
    hidePasswordDialog,
} = useAppPassword();
```

## 🛡️ 安全与异常处理

### 1. 线程安全保障
```go
// 伪代码：线程安全的密码管理
type ThreadSafePasswordManager struct {
    data      map[string]interface{}
    rwMutex   sync.RWMutex
    writeCond *sync.Cond
    
    // 提供安全的读写操作
    safeRead(key string) (interface{}, bool)
    safeWrite(key string, value interface{})
    safeDelete(key string)
    
    // 条件等待机制
    waitForCondition(predicate func() bool)
}
```

### 2. 事件系统异常处理
```go
// 伪代码：健壮的事件通知机制
type RobustEventNotifier struct {
    eventManager    *events.EventManager
    circuitBreaker  *CircuitBreaker
    retryPolicy     *RetryPolicy
    
    // 带超时和重试的事件发送
    safeNotify(eventType EventType, payload interface{}) error
    
    // 异常恢复机制
    recoverFromPanic(handler func())
}
```

### 3. 内存安全管理
```go
// 伪代码：安全的密码内存管理
type SecurePasswordCache struct {
    data    []byte
    mutex   sync.RWMutex
    
    // 安全的内存操作
    setPassword(password string) error
    getPassword() (string, error)
    clearPassword()
    
    // 内存保护
    secureZero(data []byte)
    preventSwap()
}
```

## 📋 实施计划

### 阶段1：后端核心开发（1-2周）
1. **开发 LifecyclePasswordManager**
   - 实现线程安全的密码缓存
   - 添加事件通知机制
   - 完善异常处理和恢复

2. **扩展 PasswordManager**
   - 修改 `UnlockApplication` 方法集成密码缓存
   - 添加 `LockApplication` 和 `GetPasswordStatus` 方法
   - 保持现有API完全兼容

3. **修改 encryptable.Mixin**
   - 集成 LifecyclePasswordManager
   - 添加密码失效事件通知
   - 实现优雅的错误处理

### 阶段2：前端组件开发（1-2周）
1. **扩展 lock-screen.jsx**
   - 添加密码清除事件监听
   - 确保解锁时密码自动缓存
   - 保持现有UI和交互逻辑

2. **开发 password-input-dialog.jsx**
   - 专门用于异常情况的密码输入
   - 集成到现有组件体系
   - 实现事件驱动的显示逻辑

3. **扩展 useAppPassword hook**
   - 添加生命周期相关方法
   - 集成事件监听机制
   - 保持现有API完全兼容

### 阶段3：集成测试（1周）
1. **主流程测试**
   - 应用启动和锁屏解锁
   - 密码缓存和生命周期管理
   - 正常的数据库操作

2. **异常流程测试**
   - 密码失效处理
   - 事件通知机制
   - 对话框显示和隐藏

3. **安全性测试**
   - 多线程并发访问
   - 内存泄漏检查
   - 事件系统稳定性

### 阶段4：优化部署（1周）
1. **性能优化**
   - 密码缓存效率优化
   - 事件通信性能调优
   - UI响应速度提升

2. **用户体验优化**
   - 错误提示改进
   - 加载状态优化
   - 异常恢复机制

## ⚖️ 风险评估

### 核心风险及缓解措施
1. **密码缓存安全**
   - 风险：内存中的密码可能被恶意程序读取
   - 缓解：实现安全的内存清零，使用内存保护

2. **事件系统可靠性**
   - 风险：事件通知失败导致UI状态不一致
   - 缓解：实现断路器模式和重试机制

3. **现有功能兼容性**
   - 风险：修改现有API可能影响现有功能
   - 缓解：保持完全向后兼容，充分的回归测试

### 成功指标
- ✅ 用户无需频繁输入密码
- ✅ 异常情况处理优雅
- ✅ 现有功能完全正常
- ✅ 系统性能无明显下降

---

*该技术方案基于现有的锁屏界面和密码组件，通过生命周期密码管理实现用户体验的显著提升，同时保持系统的安全性和稳定性。*