package password

import (
	"log/slog"
	"sync"

	"a8.tools/backend/pkg/password"
	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/domain"
)

// PasswordManager 密码管理器 - 用于内部管理和兼容性
// 这个类不会暴露给前端，只用于内部系统和向后兼容
type PasswordManager struct {
	container   *password.Container
	appService  password.PasswordApplicationService
	config      *config.Config
	logger      *slog.Logger
	mu          sync.RWMutex
	initialized bool
}

// NewPasswordManager 创建密码管理器
func NewPasswordManager() *PasswordManager {
	return &PasswordManager{}
}

// Initialize 初始化管理器
func (m *PasswordManager) Initialize() error {
	m.mu.Lock()
	defer m.mu.Unlock()

	if m.initialized {
		return nil
	}

	// 1. 加载配置
	config := config.LoadDefaultConfig()
	logger := slog.Default()

	// 2. 创建依赖注入容器
	container := password.NewContainer(config, logger)
	if err := container.Initialize(); err != nil {
		return password.WrapError(password.ErrCodeInitializationFailed, "容器初始化失败", err)
	}

	// 3. 获取应用服务
	appService := container.GetApplicationService()
	if appService == nil {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "应用服务初始化失败", nil)
	}

	// 4. 保存引用
	m.container = container
	m.appService = appService
	m.config = config
	m.logger = logger
	m.initialized = true

	m.logger.Info("密码管理器初始化完成")
	return nil
}

// ===== 管理和监控方法 =====

// GetCacheStats 获取缓存统计信息
func (m *PasswordManager) GetCacheStats() (*CacheStats, error) {
	if err := m.ensureInitialized(); err != nil {
		return nil, err
	}

	stats := m.appService.GetCacheStats()
	return &CacheStats{
		IsValid:     stats.IsValid,
		LastUpdate:  stats.LastUpdate.Format("2006-01-02 15:04:05"),
		TTL:         int64(m.config.Cache.TTL.Milliseconds()),
		HitRate:     0.0, // TODO: 实现命中率计算
		AccessCount: stats.AccessCount,
	}, nil
}

// ClearCache 清除密码缓存
func (m *PasswordManager) ClearCache() error {
	if err := m.ensureInitialized(); err != nil {
		return err
	}
	return m.appService.ClearCache()
}

// RefreshCache 刷新缓存过期时间
func (m *PasswordManager) RefreshCache() error {
	if err := m.ensureInitialized(); err != nil {
		return err
	}
	return m.appService.RefreshCache()
}

// ValidateEncryption 验证加密功能
func (m *PasswordManager) ValidateEncryption() error {
	if err := m.ensureInitialized(); err != nil {
		return err
	}
	return m.appService.ValidateEncryption()
}

// GetEncryptionStatus 获取加密状态
func (m *PasswordManager) GetEncryptionStatus() (*domain.EncryptionStatus, error) {
	if err := m.ensureInitialized(); err != nil {
		return nil, err
	}

	// 目前还未实现 GetEncryptionStatus 方法，返回基本状态
	return &domain.EncryptionStatus{
		IsAvailable:    m.initialized,
		Algorithm:      "AES-256-GCM",
		KeyDerivation:  "PBKDF2-SHA256",
		LastValidation: "",
		ErrorMessage:   "",
	}, nil
}

// ChangePasswordWithProgress 修改密码（带进度反馈）
func (m *PasswordManager) ChangePasswordWithProgress(oldPassword, newPassword string, progressCallback func(stage string, progress float64, message string)) error {
	if err := m.ensureInitialized(); err != nil {
		return err
	}

	if err := m.validatePassword(oldPassword); err != nil {
		return password.WrapError(password.ErrCodeInvalidPassword, "旧密码验证失败", err)
	}

	if err := m.validatePassword(newPassword); err != nil {
		return password.WrapError(password.ErrCodePasswordWeak, "新密码验证失败", err)
	}

	// 使用协调器进行密码变更
	return m.orchestrator.ChangePasswordWithProgress(oldPassword, newPassword, progressCallback)
}

// ===== 兼容性方法 - 用于迁移旧系统 =====

// ClearAllPasswords 清除所有密码（兼容旧系统）
func (m *PasswordManager) ClearAllPasswords() error {
	if err := m.ensureInitialized(); err != nil {
		return err
	}

	// 清除密码缓存
	err := m.appService.ClearCache()
	if err != nil {
		return err
	}

	// 清除存储的密码（通过仓库层）
	container := m.container
	if container != nil {
		repo := container.GetPasswordRepository()
		if repo != nil {
			// 删除应用密码
			return repo.Remove()
		}
	}

	return nil
}

// GetDataEncryptionKey 获取数据加密密钥（兼容旧系统）
func (m *PasswordManager) GetDataEncryptionKey() ([]byte, error) {
	if err := m.ensureInitialized(); err != nil {
		return nil, err
	}

	// 确保应用已解锁
	if !m.appService.IsUnlocked() {
		return nil, password.NewPasswordError(password.ErrCodeCacheExpired, "应用未解锁，无法获取加密密钥", nil)
	}

	// 通过加密领域服务获取密钥
	container := m.container
	if container != nil {
		encryptionDomain := container.GetEncryptionDomain()
		if encryptionDomain != nil {
			return encryptionDomain.GetDataEncryptionKey()
		}
	}

	return nil, password.NewPasswordError(password.ErrCodeEncryptionFailed, "无法获取数据加密密钥", nil)
}

// HasAppPassword 检查是否有应用密码（兼容旧系统）
func (m *PasswordManager) HasAppPassword() bool {
	if err := m.ensureInitialized(); err != nil {
		return false
	}
	return m.appService.HasPassword()
}

// VerifyAppPassword 验证应用密码（兼容旧系统）
func (m *PasswordManager) VerifyAppPassword(password string) bool {
	if err := m.ensureInitialized(); err != nil {
		return false
	}
	return m.appService.VerifyPassword(password) == nil
}

// ClearAppPassword 清除应用密码（兼容旧系统）
func (m *PasswordManager) ClearAppPassword() error {
	return m.ClearAllPasswords()
}

// ===== 私有方法 =====

func (m *PasswordManager) ensureInitialized() error {
	m.mu.RLock()
	defer m.mu.RUnlock()

	if !m.initialized {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "管理器未初始化", nil)
	}

	return nil
}

func (m *PasswordManager) validatePassword(pwd string) error {
	if pwd == "" {
		return password.NewPasswordError(password.ErrCodeEmptyPassword, "密码不能为空", nil)
	}

	if len(pwd) < m.config.Validation.StrengthRequirement.MinLength {
		return password.NewPasswordError(password.ErrCodePasswordWeak, "密码长度不足", nil)
	}

	return nil
}

// ===== 全局实例管理 =====

var (
	globalPasswordManager *PasswordManager
	globalManagerOnce     sync.Once
)

// GetGlobalPasswordManager 获取全局密码管理器实例（兼容旧系统）
func GetGlobalPasswordManager() *PasswordManager {
	globalManagerOnce.Do(func() {
		globalPasswordManager = NewPasswordManager()
		// 自动初始化
		if err := globalPasswordManager.Initialize(); err != nil {
			slog.Error("全局密码管理器初始化失败", "error", err)
		}
	})
	return globalPasswordManager
}

// SetGlobalPasswordManager 设置全局密码管理器实例
func SetGlobalPasswordManager(manager *PasswordManager) {
	globalPasswordManager = manager
}
