package password

import (
	"a8.tools/backend/pkg/password"
	"a8.tools/backend/pkg/password/domain"
)

// PasswordService 轻量级前端适配器
// 已废弃：建议直接使用 PasswordApplicationService
// 保留此文件仅为向后兼容
type PasswordService struct {
	appService password.PasswordApplicationService
}

// NewPasswordService 创建密码服务（已废弃）
// 建议直接使用 password.NewPasswordApplicationServiceForWails()
func NewPasswordService() *PasswordService {
	container := password.GetGlobalContainer()
	if container == nil {
		return &PasswordService{}
	}

	return &PasswordService{
		appService: container.GetApplicationService(),
	}
}

// WailsApp Wails 应用接口（保持兼容性）
type WailsApp interface {
	Events() WailsEvents
}

// WailsEvents Wails 事件接口（保持兼容性）
type WailsEvents interface {
	Emit(eventName string, data interface{})
}

// Init 初始化服务（已废弃，保持兼容性）
func (s *PasswordService) Init(wailsApp WailsApp) error {
	// 现在初始化由全局容器处理
	container := password.GetGlobalContainer()
	if container == nil {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "全局容器未初始化", nil)
	}

	s.appService = container.GetApplicationService()
	return nil
}

// ===== 前端 API 适配方法 =====

// SetPassword 设置应用密码
func (s *PasswordService) SetPassword(pwd string) error {
	if s.appService == nil {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "服务未初始化", nil)
	}
	return s.appService.SetPassword(pwd)
}

// VerifyPassword 验证密码
func (s *PasswordService) VerifyPassword(pwd string) bool {
	if s.appService == nil {
		return false
	}
	return s.appService.VerifyPassword(pwd) == nil
}

// HasPassword 检查是否有密码
func (s *PasswordService) HasPassword() bool {
	if s.appService == nil {
		return false
	}
	return s.appService.HasPassword()
}

// UnlockApplication 解锁应用
func (s *PasswordService) UnlockApplication(pwd string) error {
	if s.appService == nil {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "服务未初始化", nil)
	}
	return s.appService.UnlockApplication(pwd)
}

// LockApplication 锁定应用
func (s *PasswordService) LockApplication() error {
	if s.appService == nil {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "服务未初始化", nil)
	}
	return s.appService.LockApplication()
}

// IsUnlocked 检查是否已解锁
func (s *PasswordService) IsUnlocked() bool {
	if s.appService == nil {
		return false
	}
	return s.appService.IsUnlocked()
}

// ChangePassword 修改密码
func (s *PasswordService) ChangePassword(oldPassword, newPassword string) error {
	if s.appService == nil {
		return password.NewPasswordError(password.ErrCodeInitializationFailed, "服务未初始化", nil)
	}
	return s.appService.ChangePassword(oldPassword, newPassword)
}

// PasswordStatus 前端密码状态信息（兼容性）
type PasswordStatus struct {
	HasPassword bool   `json:"has_password"`
	IsUnlocked  bool   `json:"is_unlocked"`
	LastUnlock  string `json:"last_unlock,omitempty"`
	CacheValid  bool   `json:"cache_valid"`
	CacheExpiry string `json:"cache_expiry,omitempty"`
}

// GetStatus 获取密码状态
func (s *PasswordService) GetStatus() (*PasswordStatus, error) {
	if s.appService == nil {
		return nil, password.NewPasswordError(password.ErrCodeInitializationFailed, "服务未初始化", nil)
	}

	status := s.appService.GetStatus()
	return &PasswordStatus{
		HasPassword: status.HasPassword,
		IsUnlocked:  status.IsUnlocked,
		LastUnlock:  status.LastUnlock.Format("2006-01-02 15:04:05"),
		CacheValid:  status.IsUnlocked,
		CacheExpiry: status.CacheExpiry.Format("2006-01-02 15:04:05"),
	}, nil
}

// GetPasswordStrength 获取密码强度
func (s *PasswordService) GetPasswordStrength(pwd string) (*domain.PasswordStrengthResult, error) {
	if s.appService == nil {
		return nil, password.NewPasswordError(password.ErrCodeInitializationFailed, "服务未初始化", nil)
	}

	// 通过全局容器获取密码领域服务
	container := password.GetGlobalContainer()
	if container == nil {
		return nil, password.NewPasswordError(password.ErrCodeInitializationFailed, "全局容器未初始化", nil)
	}

	passwordDomain := container.GetPasswordDomain()
	if passwordDomain == nil {
		return nil, password.NewPasswordError(password.ErrCodeInitializationFailed, "密码领域服务未初始化", nil)
	}

	result, err := passwordDomain.GetPasswordStrength(pwd)
	return result, err
}

// ===== 全局实例管理（兼容性） =====

var (
	globalPasswordService *PasswordService
)

// GetGlobalPasswordService 获取全局密码服务实例（兼容旧系统）
func GetGlobalPasswordService() *PasswordService {
	if globalPasswordService == nil {
		globalPasswordService = NewPasswordService()
	}
	return globalPasswordService
}

// SetGlobalPasswordService 设置全局密码服务实例
func SetGlobalPasswordService(service *PasswordService) {
	globalPasswordService = service
}
