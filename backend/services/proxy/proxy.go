package proxy

import (
	"a8.tools/backend/modules/proxy/api"
	"a8.tools/backend/modules/proxy/core"
)

type ProxyService struct {
	proxyApi *api.ProxyApi
}

func NewProxyService() *ProxyService {
	proxyApi := api.NewProxyApi()
	return &ProxyService{
		proxyApi: proxyApi,
	}
}

// ImportSubscription 导入订阅
func (s *ProxyService) ImportSubscription(url string) (*core.SubscriptionData, error) {
	return s.proxyApi.ImportSubscription(url)
}
