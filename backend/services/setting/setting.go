package setting

import (
	"a8.tools/backend/database"
	"a8.tools/backend/database/models"
	"gorm.io/gorm"
)

type SettingService struct {
	db *gorm.DB
}

func NewSettingService() *SettingService {
	return &SettingService{
		db: nil, // 延迟初始化
	}
}

// ensureDB 确保数据库已初始化
func (s *SettingService) ensureDB() error {
	if s.db == nil {
		database, err := database.GetDB()
		if err != nil {
			return err
		}
		s.db = database
	}
	return nil
}

// Get 获取设置
func (s *SettingService) Get(key string) (string, error) {
	if err := s.ensureDB(); err != nil {
		return "", err
	}
	var setting models.Setting
	if err := s.db.Where("key = ?", key).First(&setting).Error; err != nil {
		return "", err
	}
	return setting.Value, nil
}

// Set 设置设置
func (s *SettingService) Set(key string, value string) error {
	if err := s.ensureDB(); err != nil {
		return err
	}
	var setting models.Setting
	// 尝试查找已存在的记录
	if err := s.db.Where("key = ?", key).First(&setting).Error; err != nil {
		// 如果记录不存在，创建新记录
		if err == gorm.ErrRecordNotFound {
			setting = models.Setting{Key: key, Value: value}
		} else {
			// 其他错误直接返回
			return err
		}
	} else {
		// 记录存在，更新值
		setting.Value = value
	}
	// Save 方法会根据是否有主键自动选择 Create 或 Update
	return s.db.Save(&setting).Error
}
