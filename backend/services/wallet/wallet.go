package wallet

import (
	"a8.tools/backend/database/models"
	"a8.tools/backend/services"
)

// WalletService 钱包服务
// 注意：这里嵌入的是值类型 db.BaseService，而不是指针类型。
// 这是为了确保 WalletService 实例本身永远不会是 nil，从而避免因服务未初始化而导致的空指针问题。
// BaseService 内部的 db 连接仍然是延迟加载的，由 ensureDB() 保证安全。
type WalletService struct {
	services.BaseService[models.Wallet]
}

// NewWalletService 创建一个新的 WalletService 实例
func NewWalletService() *WalletService {
	service := services.NewBaseService[models.Wallet]()
	return &WalletService{*service}
}

// RefreshDBConnection 刷新数据库连接（用于密码变更后）
func (w *WalletService) RefreshDBConnection() error {
	return w.BaseService.RefreshDB()
}
