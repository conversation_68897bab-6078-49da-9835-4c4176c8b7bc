package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	// 保留作为降级方案
	// 新的密码服务

	"a8.tools/backend/services/password"
	"a8.tools/backend/utils/file"
)

func main() {
	// 使用密码管理器进行清理
	passwordManager := password.GetGlobalPasswordManager()
	if passwordManager != nil {
		err := passwordManager.ClearAllPasswords()
		if err != nil {
			fmt.Printf("密码清理失败: %v\n", err)
		} else {
			fmt.Println("密码清理成功")
		}
	} else {
		fmt.Println("密码管理器不可用")
	}

	// 获取应用数据目录
	dataDir, err := file.GetAppDataDir()
	if err != nil {
		log.Fatal(err)
	}

	// 构建 databases 文件夹路径
	databasesDir := filepath.Join(dataDir, "databases")

	// 检查文件夹是否存在
	if _, err := os.Stat(databasesDir); os.IsNotExist(err) {
		fmt.Println("databases文件夹不存在")
		return
	} else if err != nil {
		fmt.Printf("检查databases文件夹状态出错: %v\n", err)
		return
	}

	fmt.Printf("删除databases文件夹: %s\n", databasesDir)

	// 删除整个databases文件夹
	err = os.RemoveAll(databasesDir)
	if err != nil {
		log.Fatal(err)
	}

	fmt.Println("delete databases folder success")
}
