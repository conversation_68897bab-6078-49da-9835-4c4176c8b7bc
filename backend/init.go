package backend

import (
	"log"
	"log/slog"

	"a8.tools/backend/database"
	"a8.tools/backend/pkg/events"
	"a8.tools/backend/pkg/logger"
	"a8.tools/backend/pkg/password"
	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/provider"
	"github.com/wailsapp/wails/v3/pkg/application"
)

func Init(app *application.App) {
	// 初始化logger模块
	initLogger()

	// 初始化统一的事件管理器（用于所有服务的事件发送）
	events.GetManager().Init(app)

	// 初始化新的密码模块
	initPasswordModule(app)

	// 检查是否有应用密码，如果有则初始化数据库
	container := password.GetGlobalContainer()
	if container != nil {
		appService := container.GetApplicationService()
		if appService != nil && appService.HasPassword() {
			if err := database.InitDB(); err != nil {
				logger.Error("数据库初始化失败", slog.String("error", err.Error()))
			} else {
				logger.Debug("数据库初始化成功")
			}
		} else {
			logger.Debug("未设置应用密码，跳过数据库初始化")
		}
	}
}

// initPasswordModule 初始化密码模块
func initPasswordModule(app *application.App) {
	// 加载密码模块配置
	cfg := config.LoadDefaultConfig()

	// 初始化全局密码容器
	err := password.InitializeGlobalContainer(cfg, slog.Default())
	if err != nil {
		logger.Error("密码模块初始化失败", slog.String("error", err.Error()))
		return
	}

	logger.Debug("密码模块初始化成功")

	// 使用统一事件系统
	container := password.GetGlobalContainer()
	unifiedEventPublisher := provider.NewUnifiedEventPublisher(slog.Default())
	container.SetEventPublisher(unifiedEventPublisher)

	logger.Debug("密码模块事件发布器设置完成")
}

// initLogger 初始化logger模块
func initLogger() {

	// 创建logger配置
	config := logger.DefaultConfig()
	config.OutputFile = logger.GetDefaultLogFile("app")
	config.OutputConsole = true
	config.Level = logger.DEBUG
	config.AsyncWrite = true
	config.MaxSize = 50   // 50MB
	config.MaxAge = 30    // 30天
	config.MaxBackups = 5 // 5个备份文件
	config.Compress = true
	config.AddSource = true

	// 初始化默认logger
	if err := logger.InitDefault(config); err != nil {
		log.Printf("初始化logger失败: %v", err)
		return
	}

}
