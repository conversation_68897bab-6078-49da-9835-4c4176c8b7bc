package file

import (
	"os"
	"path/filepath"
)

// ensureDir 确保目录存在，如果不存在则创建
func ensureDir(dir string, perm os.FileMode) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, perm)
	}
	return nil
}

// getSubDir 获取应用数据目录下的子目录
func getSubDir(subPath string, perm os.FileMode) (string, error) {
	dataDir, err := GetAppDataDir()
	if err != nil {
		return "", err
	}

	targetDir := filepath.Join(dataDir, subPath)
	if err := ensureDir(targetDir, perm); err != nil {
		return "", err
	}

	return targetDir, nil
}

// GetAppDataDir 获取应用数据目录
func GetAppDataDir() (string, error) {
	basePath, err := os.UserConfigDir()
	if err != nil {
		return "", err
	}

	dataDir := filepath.Join(basePath, "A8Tools")
	if err := ensureDir(dataDir, 0700); err != nil {
		return "", err
	}

	return dataDir, nil
}

// GetDatabaseFile 获取数据库文件路径
func GetDatabaseFile(dbName string) (string, error) {
	// 创建数据库目录
	dbDir, err := getSubDir("databases", 0700)
	if err != nil {
		return "", err
	}

	dbFilePath := filepath.Join(dbDir, dbName)

	// 创建数据库文件（如果不存在）
	if _, err := os.Stat(dbFilePath); os.IsNotExist(err) {
		file, err := os.OpenFile(dbFilePath, os.O_CREATE|os.O_RDWR, 0600)
		if err != nil {
			return "", err
		}
		file.Close()
	}

	return dbFilePath, nil
}

// GetProxyBinDir 获取代理二进制文件目录
func GetProxyBinDir() (string, error) {
	return getSubDir("proxy", 0700)
}

// GetBrowserDataDir 获取浏览器数据目录
func GetBrowserDataDir() (string, error) {
	return getSubDir("browser_data", 0700)
}

func GetLogDir() (string, error) {
	return getSubDir("logs", 0755)
}
