// Package rod provides browser automation capabilities using the go-rod library.
//
// This package includes functionality for:
// - Browser automation with anti-detection capabilities
// - Challenge detection and solving (Cloudflare, reCAPTCHA, hCaptcha)
// - Extension management
// - Task-based automation with YAML configuration
//
// The configuration system is designed to be flexible and consistent across
// different components of the package.
package rod

import (
	"time"
)

// ChallengeConfig contains unified configuration for challenge handling.
// This structure centralizes all challenge-related settings to ensure consistency
// across different components of the package.
//
// Usage:
//
//	// Create a default challenge configuration
//	config := NewDefaultChallengeConfig()
//
//	// Customize the configuration
//	config.HandleChallenges = true
//	config.AutoSolveCaptchas = true
//	config.CaptchaSolvingService = "2captcha"
//	config.CaptchaAPIKey = "your-api-key"
//
//	// Apply the configuration to task settings
//	settings := &TaskSettings{}
//	ApplyChallengeConfigToTaskSettings(config, settings)
//
//	// Or apply to challenge handler options
//	opts := &ChallengeHandlerOptions{}
//	ApplyChallengeConfigToOptions(config, opts)
type ChallengeConfig struct {
	// Challenge detection and handling

	// HandleChallenges enables automatic detection and handling of challenges.
	// When true, the browser will attempt to detect and solve challenges like Cloudflare, reCAPTCHA, etc.
	HandleChallenges bool `json:"handleChallenges" yaml:"handle_challenges,omitempty"`

	// MonitorChallenges enables continuous monitoring for challenges during page navigation.
	// When true, the browser will continuously check for challenges in the background.
	MonitorChallenges bool `json:"monitorChallenges" yaml:"monitor_challenges,omitempty"`

	// ChallengeTimeout is the maximum time to wait for a challenge to be solved.
	// This is for internal use and not serialized to JSON or YAML.
	ChallengeTimeout time.Duration `json:"-" yaml:"-"`

	// ChallengeTimeoutStr is the string representation of ChallengeTimeout.
	// Format: "60s", "2m", etc.
	ChallengeTimeoutStr string `json:"challengeTimeout" yaml:"challenge_timeout,omitempty"`

	// ChallengeRetries is the number of times to retry solving a challenge.
	ChallengeRetries int `json:"challengeRetries" yaml:"challenge_retries,omitempty"`

	// ChallengeRetryDelayStr is the string representation of ChallengeRetryDelay.
	// Format: "2s", "500ms", etc.
	ChallengeRetryDelayStr string `json:"challengeRetryDelay" yaml:"challenge_retry_delay,omitempty"`

	// ChallengeRetryDelay is the delay between challenge solving retries.
	// This is for internal use and not serialized to JSON or YAML.
	ChallengeRetryDelay time.Duration `json:"-" yaml:"-"`

	// Captcha solving

	// AutoSolveCaptchas enables automatic solving of captchas.
	// When true, the browser will attempt to solve captchas using built-in methods
	// or external services if configured.
	AutoSolveCaptchas bool `json:"autoSolveCaptchas" yaml:"auto_solve_captchas,omitempty"`

	// CaptchaSolvingService specifies the external service to use for solving captchas.
	// Supported values: "2captcha", "anticaptcha", "capsolver"
	CaptchaSolvingService string `json:"captchaSolvingService" yaml:"captcha_solving_service,omitempty"`

	// CaptchaAPIKey is the API key for the captcha solving service.
	CaptchaAPIKey string `json:"captchaAPIKey" yaml:"captcha_api_key,omitempty"`

	// HumanSimulation enables human-like behavior when interacting with challenges.
	// When true, the browser will add random delays and movements to simulate human behavior.
	HumanSimulation bool `json:"humanSimulation" yaml:"human_simulation,omitempty"`
}

// NewDefaultChallengeConfig creates a default challenge configuration with reasonable settings.
// The default configuration enables challenge handling and monitoring, with a 60-second timeout
// and 3 retries with a 2-second delay between retries. Auto-solving of captchas is enabled,
// but no external captcha solving service is configured by default.
//
// Example:
//
//	config := NewDefaultChallengeConfig()
//	// Customize as needed
//	config.CaptchaSolvingService = "2captcha"
//	config.CaptchaAPIKey = "your-api-key"
func NewDefaultChallengeConfig() *ChallengeConfig {
	return &ChallengeConfig{
		HandleChallenges:       true,
		MonitorChallenges:      true,
		ChallengeTimeout:       60 * time.Second,
		ChallengeTimeoutStr:    "60s",
		ChallengeRetries:       3,
		ChallengeRetryDelay:    2 * time.Second,
		ChallengeRetryDelayStr: "2s",
		AutoSolveCaptchas:      true,
		CaptchaSolvingService:  "",
		CaptchaAPIKey:          "",
		HumanSimulation:        true,
	}
}

// ParseChallengeConfig parses string durations into time.Duration values.
// This function converts the string representations of durations (ChallengeTimeoutStr and
// ChallengeRetryDelayStr) into actual time.Duration values (ChallengeTimeout and ChallengeRetryDelay).
// If the string representations are empty, default values are used (60s for timeout, 2s for retry delay).
//
// Returns an error if the string durations cannot be parsed.
//
// Example:
//
//	config := &ChallengeConfig{
//	    ChallengeTimeoutStr: "2m",
//	    ChallengeRetryDelayStr: "500ms",
//	}
//	err := ParseChallengeConfig(config)
//	if err != nil {
//	    log.Fatalf("Failed to parse challenge config: %v", err)
//	}
//	// Now config.ChallengeTimeout is 2 minutes and config.ChallengeRetryDelay is 500 milliseconds
func ParseChallengeConfig(config *ChallengeConfig) error {
	var err error

	// Parse challenge timeout
	if config.ChallengeTimeoutStr != "" {
		config.ChallengeTimeout, err = time.ParseDuration(config.ChallengeTimeoutStr)
		if err != nil {
			return err
		}
	} else {
		config.ChallengeTimeout = 60 * time.Second // Default
	}

	// Parse challenge retry delay
	if config.ChallengeRetryDelayStr != "" {
		config.ChallengeRetryDelay, err = time.ParseDuration(config.ChallengeRetryDelayStr)
		if err != nil {
			return err
		}
	} else {
		config.ChallengeRetryDelay = 2 * time.Second // Default
	}

	return nil
}

// ApplyChallengeConfigToOptions applies the challenge configuration to ChallengeHandlerOptions.
// This function transfers the settings from a ChallengeConfig to a ChallengeHandlerOptions struct,
// which is used by the ChallengeHandler.
//
// Example:
//
//	config := NewDefaultChallengeConfig()
//	config.AutoSolveCaptchas = true
//	config.CaptchaSolvingService = "2captcha"
//	config.CaptchaAPIKey = "your-api-key"
//
//	opts := &ChallengeHandlerOptions{}
//	ApplyChallengeConfigToOptions(config, opts)
//
//	// Now opts contains the settings from config
//	handler := NewChallengeHandler(logger, opts)
func ApplyChallengeConfigToOptions(config *ChallengeConfig, opts *ChallengeHandlerOptions) {
	opts.HumanSimulation = config.HumanSimulation
	opts.AutoSolve = config.AutoSolveCaptchas
	opts.ChallengeTimeout = config.ChallengeTimeout
	opts.MaxRetries = config.ChallengeRetries
	opts.DelayBetweenTries = config.ChallengeRetryDelay
	opts.CaptchaSolvingService = config.CaptchaSolvingService
	opts.CaptchaAPIKey = config.CaptchaAPIKey
}

// ApplyChallengeConfigToTaskSettings applies the challenge configuration to TaskSettings.
// This function transfers the settings from a ChallengeConfig to a TaskSettings struct,
// which is used for task execution.
//
// Example:
//
//	config := NewDefaultChallengeConfig()
//	config.HandleChallenges = true
//	config.MonitorChallenges = true
//
//	settings := &TaskSettings{}
//	ApplyChallengeConfigToTaskSettings(config, settings)
//
//	// Now settings contains the challenge-related settings from config
//	plan := &TaskPlan{Settings: settings}
func ApplyChallengeConfigToTaskSettings(config *ChallengeConfig, settings *TaskSettings) {
	settings.HandleChallenges = config.HandleChallenges
	settings.MonitorChallenges = config.MonitorChallenges
	settings.ChallengeRetries = config.ChallengeRetries
	settings.ChallengeTimeout = config.ChallengeTimeoutStr
	settings.ChallengeRetryDelay = config.ChallengeRetryDelayStr
	settings.AutoSolveCaptchas = config.AutoSolveCaptchas
	settings.CaptchaSolvingService = config.CaptchaSolvingService
	settings.CaptchaAPIKey = config.CaptchaAPIKey
}

// ExtractChallengeConfigFromTaskSettings extracts challenge configuration from TaskSettings.
// This function creates a new ChallengeConfig from the challenge-related settings in a TaskSettings struct.
// It also parses the string durations into time.Duration values.
//
// Returns the extracted configuration and any error that occurred during parsing.
//
// Example:
//
//	settings := &TaskSettings{
//	    HandleChallenges: true,
//	    MonitorChallenges: true,
//	    ChallengeTimeout: "30s",
//	    ChallengeRetries: 5,
//	}
//
//	config, err := ExtractChallengeConfigFromTaskSettings(settings)
//	if err != nil {
//	    log.Fatalf("Failed to extract challenge config: %v", err)
//	}
//
//	// Now config contains the challenge-related settings from settings
func ExtractChallengeConfigFromTaskSettings(settings *TaskSettings) (*ChallengeConfig, error) {
	config := &ChallengeConfig{
		HandleChallenges:       settings.HandleChallenges,
		MonitorChallenges:      settings.MonitorChallenges,
		ChallengeTimeoutStr:    settings.ChallengeTimeout,
		ChallengeRetries:       settings.ChallengeRetries,
		ChallengeRetryDelayStr: settings.ChallengeRetryDelay,
		AutoSolveCaptchas:      settings.AutoSolveCaptchas,
		CaptchaSolvingService:  settings.CaptchaSolvingService,
		CaptchaAPIKey:          settings.CaptchaAPIKey,
		HumanSimulation:        true, // Default value
	}

	// Parse the string durations
	err := ParseChallengeConfig(config)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// ExtractChallengeConfigFromAntiDetectOptions extracts challenge configuration from AntiDetectOptions.
// This function creates a new ChallengeConfig from the challenge-related settings in an AntiDetectOptions struct.
//
// Example:
//
//	options := &AntiDetectOptions{
//	    HandleChallenges: true,
//	    MonitorChallenges: true,
//	    ChallengeTimeout: 30 * time.Second,
//	    SimulateHumanBehavior: true,
//	}
//
//	config := ExtractChallengeConfigFromAntiDetectOptions(options)
//
//	// Now config contains the challenge-related settings from options
func ExtractChallengeConfigFromAntiDetectOptions(options *AntiDetectOptions) *ChallengeConfig {
	return &ChallengeConfig{
		HandleChallenges:       options.HandleChallenges,
		MonitorChallenges:      options.MonitorChallenges,
		ChallengeTimeout:       options.ChallengeTimeout,
		ChallengeTimeoutStr:    options.ChallengeTimeout.String(),
		ChallengeRetries:       options.ChallengeRetries,
		ChallengeRetryDelay:    options.ChallengeRetryDelay,
		ChallengeRetryDelayStr: options.ChallengeRetryDelay.String(),
		AutoSolveCaptchas:      options.AutoSolveCaptchas,
		CaptchaSolvingService:  options.CaptchaSolvingService,
		CaptchaAPIKey:          options.CaptchaAPIKey,
		HumanSimulation:        options.SimulateHumanBehavior,
	}
}
