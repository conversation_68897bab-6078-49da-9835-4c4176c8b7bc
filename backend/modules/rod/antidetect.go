package rod

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/stealth"
)

// Global random source
var (
	randSource = rand.NewSource(time.Now().UnixNano())
	random     = rand.New(randSource)
)

// AntiDetectOptions contains options for anti-detection mechanisms
type AntiDetectOptions struct {
	// Randomization options
	RandomizeUserAgent    bool
	RandomizeViewport     bool
	RandomizeFingerprint  bool
	SimulateHumanBehavior bool

	// Webdriver detection evasion
	DisableWebDriver  bool
	DisableAutomation bool

	// Fingerprinting protection
	MaskWebRTC              bool
	MaskCanvas              bool
	MaskPlugins             bool
	MaskPlatform            bool
	MaskHardwareConcurrency bool
	MaskLanguages           bool
	MaskScreenSize          bool
	MaskTimeZone            bool
	MaskBattery             bool
	MaskAudioContext        bool
	MaskFonts               bool
	MaskWebGL               bool
	MaskClientRects         bool

	// Challenge handling
	HandleChallenges      bool
	MonitorChallenges     bool
	AutoSolveCaptchas     bool
	ChallengeTimeout      time.Duration
	ChallengeRetries      int
	ChallengeRetryDelay   time.Duration
	CaptchaSolvingService string
	CaptchaAPIKey         string

	// Advanced options
	CustomUserAgent         string  // If set, overrides RandomizeUserAgent
	CustomViewportWidth     int     // If > 0, overrides RandomizeViewport width
	CustomViewportHeight    int     // If > 0, overrides RandomizeViewport height
	CustomDeviceScaleFactor float64 // If > 0, sets a custom device scale factor
	CustomTimezoneID        string  // If set, overrides the timezone
	CustomLocale            string  // If set, overrides the locale
}

// AntiDetectProfile contains settings to avoid bot detection
type AntiDetectProfile struct {
	// Fingerprint settings
	UserAgent           string            `json:"userAgent"`
	Language            string            `json:"language"`
	ScreenWidth         int64             `json:"screenWidth"`
	ScreenHeight        int64             `json:"screenHeight"`
	DeviceScaleFactor   float64           `json:"deviceScaleFactor"`
	AcceptLanguage      string            `json:"acceptLanguage"`
	DoNotTrack          bool              `json:"doNotTrack"`
	Timezone            string            `json:"timezone"`
	TimezoneOffset      int               `json:"timezoneOffset"`
	WebRTC              WebRTCSettings    `json:"webRTC"`
	WebGL               WebGLSettings     `json:"webGL"`
	Canvas              CanvasSettings    `json:"canvas"`
	Fonts               FontSettings      `json:"fonts"`
	ClientRects         bool              `json:"clientRects"`
	AudioContext        bool              `json:"audioContext"`
	WebDriver           bool              `json:"webDriver"`
	Hardware            HardwareSettings  `json:"hardware"`
	Plugins             []PluginSettings  `json:"plugins"`
	CustomHeaders       map[string]string `json:"customHeaders"`
	CustomCookies       []CookieSettings  `json:"customCookies"`
	CustomCSS           string            `json:"customCSS"`
	CustomJS            string            `json:"customJS"`
	ExecuteOnLoad       bool              `json:"executeOnLoad"`
	ProxyEnabled        bool              `json:"proxyEnabled"`
	ProxyServer         string            `json:"proxyServer"`
	ProxyUsername       string            `json:"proxyUsername"`
	ProxyPassword       string            `json:"proxyPassword"`
	CustomScreenshotURL string            `json:"customScreenshotURL"`
	ScreenshotDelay     int               `json:"screenshotDelay"`
	BrowserFingerprint  string            `json:"browserFingerprint"`

	// Challenge handling settings
	HandleChallenges      bool              `json:"handleChallenges"`
	MonitorChallenges     bool              `json:"monitorChallenges"`
	AutoSolveCaptchas     bool              `json:"autoSolveCaptchas"`
	ChallengeTimeout      int               `json:"challengeTimeout"` // in seconds
	ChallengeRetries      int               `json:"challengeRetries"`
	ChallengeRetryDelay   int               `json:"challengeRetryDelay"` // in seconds
	CaptchaSolvingService string            `json:"captchaSolvingService"`
	CaptchaAPIKey         string            `json:"captchaAPIKey"`
	ChallengeSettings     map[string]string `json:"challengeSettings"` // Additional settings for specific challenge types
}

// WebRTCSettings contains WebRTC privacy settings
type WebRTCSettings struct {
	Mode            string `json:"mode"` // default, disabled, real_ip, public_only
	LocalIPHandling string `json:"localIPHandling"`
}

// WebGLSettings contains WebGL fingerprint settings
type WebGLSettings struct {
	Vendor        string `json:"vendor"`
	Renderer      string `json:"renderer"`
	Unmasked      bool   `json:"unmasked"`
	RandomVendor  bool   `json:"randomVendor"`
	RandomVersion bool   `json:"randomVersion"`
}

// CanvasSettings contains canvas fingerprint settings
type CanvasSettings struct {
	Mode          string  `json:"mode"` // default, noise, block
	NoiseLevel    float64 `json:"noiseLevel"`
	RandomValues  bool    `json:"randomValues"`
	RandomNoise   bool    `json:"randomNoise"`
	ConsistentKey string  `json:"consistentKey"`
}

// FontSettings contains font fingerprint settings
type FontSettings struct {
	EnableMasking  bool     `json:"enableMasking"`
	CustomFontList []string `json:"customFontList"`
	EnableSwapping bool     `json:"enableSwapping"`
}

// HardwareSettings contains hardware-related settings
type HardwareSettings struct {
	ConcealGPU      bool   `json:"concealGPU"`
	ConcealHardware bool   `json:"concealHardware"`
	HardwareName    string `json:"hardwareName"`
	ConcealOS       bool   `json:"concealOS"`
	OSName          string `json:"osName"`
}

// PluginSettings represents a browser plugin
type PluginSettings struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	MimeTypes   string `json:"mimeTypes"`
	Filename    string `json:"filename"`
	Enabled     bool   `json:"enabled"`
}

// CookieSettings represents a custom cookie
type CookieSettings struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	Domain   string `json:"domain"`
	Path     string `json:"path"`
	Secure   bool   `json:"secure"`
	HTTPOnly bool   `json:"httpOnly"`
}

// NewDefaultAntiDetectProfile creates a default profile with reasonable settings
func NewDefaultAntiDetectProfile() *AntiDetectProfile {
	return &AntiDetectProfile{
		UserAgent:         "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36",
		Language:          "en-US",
		ScreenWidth:       1920,
		ScreenHeight:      1080,
		DeviceScaleFactor: 1.0,
		AcceptLanguage:    "en-US,en;q=0.9",
		DoNotTrack:        false,
		Timezone:          "America/New_York",
		TimezoneOffset:    -300, // Eastern Time
		WebRTC: WebRTCSettings{
			Mode:            "public_only",
			LocalIPHandling: "default_public_interface_only",
		},
		WebGL: WebGLSettings{
			Vendor:        "Google Inc. (Intel)",
			Renderer:      "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5.0 ps_5.0, D3D11)",
			Unmasked:      true,
			RandomVendor:  false,
			RandomVersion: false,
		},
		Canvas: CanvasSettings{
			Mode:          "noise",
			NoiseLevel:    0.1,
			RandomValues:  false,
			RandomNoise:   false,
			ConsistentKey: "default",
		},
		Fonts: FontSettings{
			EnableMasking:  true,
			CustomFontList: []string{},
			EnableSwapping: false,
		},
		ClientRects:   true,
		AudioContext:  true,
		WebDriver:     false,
		ProxyEnabled:  false,
		ExecuteOnLoad: true,
		Hardware: HardwareSettings{
			ConcealGPU:      true,
			ConcealHardware: true,
			HardwareName:    "Intel(R) Core(TM) i7-10750H CPU @ 2.60GHz",
			ConcealOS:       false,
			OSName:          "Windows 10",
		},
		// Challenge handling settings
		HandleChallenges:    true,
		MonitorChallenges:   true,
		AutoSolveCaptchas:   true,
		ChallengeTimeout:    60, // 60 seconds
		ChallengeRetries:    3,
		ChallengeRetryDelay: 2, // 2 seconds
		ChallengeSettings:   map[string]string{},
	}
}

// LoadAntiDetectProfile loads a profile from a JSON file
func LoadAntiDetectProfile(filePath string) (*AntiDetectProfile, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read profile file: %w", err)
	}

	var profile AntiDetectProfile
	if err := json.Unmarshal(data, &profile); err != nil {
		return nil, fmt.Errorf("failed to parse profile JSON: %w", err)
	}

	return &profile, nil
}

// SaveAntiDetectProfile saves a profile to a JSON file
func SaveAntiDetectProfile(profile *AntiDetectProfile, filePath string) error {
	data, err := json.MarshalIndent(profile, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal profile to JSON: %w", err)
	}

	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write profile to file: %w", err)
	}

	return nil
}

// CreateStealthPage creates a stealth page that can't be detected as bot
func CreateStealthPage(browser *rod.Browser) (*rod.Page, error) {
	return stealth.Page(browser)
}

// RandomUserAgent returns a random user agent string
func RandomUserAgent() string {
	userAgents := []string{
		// Chrome on Windows
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",

		// Chrome on macOS
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/111.0.0.0 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/110.0.0.0 Safari/537.36",

		// Firefox on Windows
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/112.0",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/111.0",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/110.0",

		// Firefox on macOS
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/112.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/111.0",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/110.0",

		// Safari on macOS
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.4 Safari/605.1.15",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.3 Safari/605.1.15",
	}
	return userAgents[random.Intn(len(userAgents))]
}
