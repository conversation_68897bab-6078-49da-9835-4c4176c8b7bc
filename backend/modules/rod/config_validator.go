package rod

import (
	"fmt"
	"strings"
	"time"
)

// ConfigValidationResult represents the result of a configuration validation
type ConfigValidationResult struct {
	Valid    bool
	Errors   []string
	Warnings []string
}

// String returns a string representation of the validation result
func (r *ConfigValidationResult) String() string {
	var sb strings.Builder

	if r.Valid {
		sb.WriteString("Configuration is valid.\n")
	} else {
		sb.WriteString("Configuration is invalid.\n")
	}

	if len(r.Errors) > 0 {
		sb.WriteString("\nErrors:\n")
		for i, err := range r.Errors {
			sb.WriteString(fmt.Sprintf("%d. %s\n", i+1, err))
		}
	}

	if len(r.Warnings) > 0 {
		sb.WriteString("\nWarnings:\n")
		for i, warn := range r.Warnings {
			sb.WriteString(fmt.Sprintf("%d. %s\n", i+1, warn))
		}
	}

	return sb.String()
}

// ValidateChallengeConfig validates a ChallengeConfig
func ValidateChallengeConfig(config *ChallengeConfig) *ConfigValidationResult {
	result := &ConfigValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// Validate timeout
	if config.ChallengeTimeoutStr != "" {
		timeout, err := time.ParseDuration(config.ChallengeTimeoutStr)
		if err != nil {
			result.Valid = false
			result.Errors = append(result.Errors,
				fmt.Sprintf("Invalid challenge timeout: %s. Error: %v", config.ChallengeTimeoutStr, err))
		} else if timeout < time.Second {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Challenge timeout is very short: %s. Consider using a longer timeout.", config.ChallengeTimeoutStr))
		} else if timeout > 5*time.Minute {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Challenge timeout is very long: %s. Consider using a shorter timeout.", config.ChallengeTimeoutStr))
		}
	}

	// Validate retry delay
	if config.ChallengeRetryDelayStr != "" {
		delay, err := time.ParseDuration(config.ChallengeRetryDelayStr)
		if err != nil {
			result.Valid = false
			result.Errors = append(result.Errors,
				fmt.Sprintf("Invalid challenge retry delay: %s. Error: %v", config.ChallengeRetryDelayStr, err))
		} else if delay < 100*time.Millisecond {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Challenge retry delay is very short: %s. Consider using a longer delay.", config.ChallengeRetryDelayStr))
		} else if delay > 10*time.Second {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Challenge retry delay is very long: %s. Consider using a shorter delay.", config.ChallengeRetryDelayStr))
		}
	}

	// Validate retries
	if config.ChallengeRetries < 1 {
		result.Warnings = append(result.Warnings,
			fmt.Sprintf("Challenge retries is set to %d. Consider using at least 1 retry.", config.ChallengeRetries))
	} else if config.ChallengeRetries > 10 {
		result.Warnings = append(result.Warnings,
			fmt.Sprintf("Challenge retries is set to %d. Consider using fewer retries.", config.ChallengeRetries))
	}

	// Validate captcha solving service
	if config.AutoSolveCaptchas && config.CaptchaSolvingService != "" {
		validServices := map[string]bool{
			"2captcha":    true,
			"anticaptcha": true,
			"capsolver":   true,
		}

		if !validServices[config.CaptchaSolvingService] {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Unknown captcha solving service: %s. Supported services: 2captcha, anticaptcha, capsolver.",
					config.CaptchaSolvingService))
		}

		if config.CaptchaAPIKey == "" {
			result.Warnings = append(result.Warnings,
				"Captcha solving service is specified but API key is empty.")
		}
	}

	return result
}

// ValidateTaskSettings validates TaskSettings
func ValidateTaskSettings(settings *TaskSettings) *ConfigValidationResult {
	result := &ConfigValidationResult{
		Valid:    true,
		Errors:   []string{},
		Warnings: []string{},
	}

	// Validate timeout
	if settings.Timeout != "" {
		timeout, err := time.ParseDuration(settings.Timeout)
		if err != nil {
			result.Valid = false
			result.Errors = append(result.Errors,
				fmt.Sprintf("Invalid timeout: %s. Error: %v", settings.Timeout, err))
		} else if timeout < time.Second {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Timeout is very short: %s. Consider using a longer timeout.", settings.Timeout))
		}
	}

	// Validate default wait
	if settings.DefaultWait != "" {
		wait, err := time.ParseDuration(settings.DefaultWait)
		if err != nil {
			result.Valid = false
			result.Errors = append(result.Errors,
				fmt.Sprintf("Invalid default wait: %s. Error: %v", settings.DefaultWait, err))
		} else if wait < 100*time.Millisecond {
			result.Warnings = append(result.Warnings,
				fmt.Sprintf("Default wait is very short: %s. Consider using a longer wait time.", settings.DefaultWait))
		}
	}

	// Validate viewport dimensions
	if settings.ViewportWidth > 0 && settings.ViewportWidth < 320 {
		result.Warnings = append(result.Warnings,
			fmt.Sprintf("Viewport width is very small: %d. Consider using a width of at least 320.", settings.ViewportWidth))
	}

	if settings.ViewportHeight > 0 && settings.ViewportHeight < 240 {
		result.Warnings = append(result.Warnings,
			fmt.Sprintf("Viewport height is very small: %d. Consider using a height of at least 240.", settings.ViewportHeight))
	}

	// Extract and validate challenge config
	if settings.HandleChallenges || settings.MonitorChallenges || settings.AutoSolveCaptchas {
		config, err := ExtractChallengeConfigFromTaskSettings(settings)
		if err != nil {
			result.Valid = false
			result.Errors = append(result.Errors,
				fmt.Sprintf("Failed to extract challenge config: %v", err))
		} else {
			challengeResult := ValidateChallengeConfig(config)
			result.Valid = result.Valid && challengeResult.Valid
			result.Errors = append(result.Errors, challengeResult.Errors...)
			result.Warnings = append(result.Warnings, challengeResult.Warnings...)
		}
	}

	return result
}
