// Package examples contains example code for using the rod package.
package examples

import (
	"fmt"
	"log"
	"os"
	"time"

	"a8.tools/backend/modules/rod"
)

// ExampleChallengeConfig demonstrates how to use the ChallengeConfig structure
// to configure challenge handling in different components of the rod package.
func ExampleChallengeConfig() {
	// Create a logger
	logger := log.New(os.Stdout, "[Example] ", log.LstdFlags)

	// Create a default challenge configuration
	config := rod.NewDefaultChallengeConfig()

	// Customize the configuration
	config.HandleChallenges = true
	config.MonitorChallenges = true
	config.ChallengeTimeoutStr = "2m"
	config.ChallengeRetries = 5
	config.ChallengeRetryDelayStr = "3s"
	config.AutoSolveCaptchas = true
	config.CaptchaSolvingService = "2captcha"
	config.CaptchaAPIKey = "your-api-key"
	config.HumanSimulation = true

	// Parse the string durations into time.Duration values
	err := rod.ParseChallengeConfig(config)
	if err != nil {
		logger.Fatalf("Failed to parse challenge config: %v", err)
	}

	// Now config.ChallengeTimeout is 2 minutes and config.ChallengeRetryDelay is 3 seconds

	// Example 1: Apply the configuration to ChallengeHandlerOptions
	opts := &rod.ChallengeHandlerOptions{}
	rod.ApplyChallengeConfigToOptions(config, opts)

	// Create a challenge handler with the options
	handler := rod.NewChallengeHandler(logger, opts)
	fmt.Printf("Created challenge handler with timeout: %v\n", handler.GetTimeout())

	// Example 2: Apply the configuration to TaskSettings
	settings := &rod.TaskSettings{}
	rod.ApplyChallengeConfigToTaskSettings(config, settings)

	// Create a task plan with the settings
	ba := rod.NewBrowserAutomation()
	plan := ba.CreateNewPlan("Example Plan", "A plan with challenge handling")
	plan.Settings = *settings

	fmt.Printf("Created task plan with challenge handling: %v\n", plan.Settings.HandleChallenges)
	fmt.Printf("Challenge timeout: %v\n", plan.Settings.ChallengeTimeout)

	// Example 3: Extract configuration from TaskSettings
	extractedConfig, err := rod.ExtractChallengeConfigFromTaskSettings(settings)
	if err != nil {
		logger.Fatalf("Failed to extract challenge config: %v", err)
	}

	fmt.Printf("Extracted challenge timeout: %v\n", extractedConfig.ChallengeTimeout)

	// Example 4: Extract configuration from AntiDetectOptions
	antiDetectOpts := &rod.AntiDetectOptions{
		HandleChallenges:      true,
		MonitorChallenges:     true,
		ChallengeTimeout:      30 * time.Second,
		ChallengeRetries:      3,
		ChallengeRetryDelay:   1 * time.Second,
		AutoSolveCaptchas:     true,
		SimulateHumanBehavior: true,
	}

	antiDetectConfig := rod.ExtractChallengeConfigFromAntiDetectOptions(antiDetectOpts)
	fmt.Printf("Extracted anti-detect challenge timeout: %v\n", antiDetectConfig.ChallengeTimeout)
}

// ExampleChallengeHandling demonstrates how to handle challenges in a browser automation task.
func ExampleChallengeHandling() {
	// Create a browser automation instance
	ba := rod.NewBrowserAutomation()

	// Create a task plan
	plan := ba.CreateNewPlan("Challenge Handling", "A plan that handles challenges")

	// Configure challenge handling in the plan settings
	plan.Settings.HandleChallenges = true
	plan.Settings.MonitorChallenges = true
	plan.Settings.ChallengeTimeout = "60s"
	plan.Settings.ChallengeRetries = 3
	plan.Settings.AutoSolveCaptchas = true
	plan.Settings.CaptchaSolvingService = "2captcha"
	plan.Settings.CaptchaAPIKey = "your-api-key"

	// Add tasks to the plan
	ba.AddNavigateTask(plan, "https://example.com", "Navigate to example.com")
	ba.AddWaitVisibleTask(plan, "h1", rod.SelectorQuery, "5s", "Wait for heading")
	ba.AddClickTask(plan, "button", rod.SelectorQuery, "Click button")

	// Execute the plan
	err := ba.ExecutePlan(plan)
	if err != nil {
		fmt.Printf("Failed to execute plan: %v\n", err)
		return
	}

	fmt.Printf("Plan executed successfully")
}
