package rod

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"github.com/go-rod/rod"
)

// Logger is an interface for logging
type Logger interface {
	Printf(format string, v ...interface{})
	Println(v ...interface{})
}

// CaptchaSolver handles integration with external captcha solving services
type CaptchaSolver struct {
	service  string
	apiKey   string
	logger   Logger
	timeout  time.Duration
	retries  int
	interval time.Duration
}

// NewCaptchaSolver creates a new captcha solver
func NewCaptchaSolver(service, apiKey string, logger Logger) *CaptchaSolver {
	return &CaptchaSolver{
		service:  service,
		apiKey:   apiKey,
		logger:   logger,
		timeout:  5 * time.Minute,
		retries:  3,
		interval: 5 * time.Second,
	}
}

// SolveReCaptcha solves a reCAPTCHA challenge
func (cs *CaptchaSolver) SolveReCaptcha(page *rod.Page) (string, error) {
	cs.logger.Printf("Solving reCAPTCHA using %s", cs.service)

	// Extract the site key
	siteKey, err := cs.extractReCaptchaSiteKey(page)
	if err != nil {
		return "", fmt.Errorf("failed to extract reCAPTCHA site key: %w", err)
	}

	// Get the page URL
	pageURL := page.MustInfo().URL

	// Submit the captcha to the solving service
	taskID, err := cs.submitReCaptchaTask(siteKey, pageURL)
	if err != nil {
		return "", fmt.Errorf("failed to submit reCAPTCHA task: %w", err)
	}

	// Wait for the result
	solution, err := cs.waitForCaptchaSolution(taskID)
	if err != nil {
		return "", fmt.Errorf("failed to get reCAPTCHA solution: %w", err)
	}

	// Apply the solution
	err = cs.applyReCaptchaSolution(page, solution)
	if err != nil {
		return "", fmt.Errorf("failed to apply reCAPTCHA solution: %w", err)
	}

	return solution, nil
}

// SolveHCaptcha solves an hCaptcha challenge
func (cs *CaptchaSolver) SolveHCaptcha(page *rod.Page) (string, error) {
	cs.logger.Printf("Solving hCaptcha using %s", cs.service)

	// Extract the site key
	siteKey, err := cs.extractHCaptchaSiteKey(page)
	if err != nil {
		return "", fmt.Errorf("failed to extract hCaptcha site key: %w", err)
	}

	// Get the page URL
	pageURL := page.MustInfo().URL

	// Submit the captcha to the solving service
	taskID, err := cs.submitHCaptchaTask(siteKey, pageURL)
	if err != nil {
		return "", fmt.Errorf("failed to submit hCaptcha task: %w", err)
	}

	// Wait for the result
	solution, err := cs.waitForCaptchaSolution(taskID)
	if err != nil {
		return "", fmt.Errorf("failed to get hCaptcha solution: %w", err)
	}

	// Apply the solution
	err = cs.applyHCaptchaSolution(page, solution)
	if err != nil {
		return "", fmt.Errorf("failed to apply hCaptcha solution: %w", err)
	}

	return solution, nil
}

// extractReCaptchaSiteKey extracts the reCAPTCHA site key from the page
func (cs *CaptchaSolver) extractReCaptchaSiteKey(page *rod.Page) (string, error) {
	siteKey, err := page.Eval(`() => {
		// Try to find the site key in the reCAPTCHA element
		const recaptchaElements = document.querySelectorAll('.g-recaptcha');
		if (recaptchaElements.length > 0) {
			return recaptchaElements[0].getAttribute('data-sitekey');
		}

		// Try to find the site key in reCAPTCHA script
		const scripts = document.querySelectorAll('script');
		for (const script of scripts) {
			const src = script.getAttribute('src');
			if (src && src.includes('recaptcha')) {
				const match = src.match(/[?&]k=([^&]+)/);
				if (match && match[1]) {
					return match[1];
				}
			}
		}

		// Try to find the site key in the page source
		const html = document.documentElement.outerHTML;
		const match = html.match(/data-sitekey="([^"]+)"/);
		if (match && match[1]) {
			return match[1];
		}

		return null;
	}`)

	if err != nil {
		return "", err
	}

	if siteKey.Value.Str() == "" {
		return "", fmt.Errorf("could not find reCAPTCHA site key")
	}

	return siteKey.Value.Str(), nil
}

// extractHCaptchaSiteKey extracts the hCaptcha site key from the page
func (cs *CaptchaSolver) extractHCaptchaSiteKey(page *rod.Page) (string, error) {
	siteKey, err := page.Eval(`() => {
		// Try to find the site key in the hCaptcha element
		const hcaptchaElements = document.querySelectorAll('.h-captcha');
		if (hcaptchaElements.length > 0) {
			return hcaptchaElements[0].getAttribute('data-sitekey');
		}

		// Try to find the site key in the page source
		const html = document.documentElement.outerHTML;
		const match = html.match(/data-sitekey="([^"]+)"/);
		if (match && match[1]) {
			return match[1];
		}

		return null;
	}`)

	if err != nil {
		return "", err
	}

	if siteKey.Value.Str() == "" {
		return "", fmt.Errorf("could not find hCaptcha site key")
	}

	return siteKey.Value.Str(), nil
}

// submitReCaptchaTask submits a reCAPTCHA task to the solving service
func (cs *CaptchaSolver) submitReCaptchaTask(siteKey, pageURL string) (string, error) {
	switch cs.service {
	case "2captcha":
		return cs.submit2CaptchaReCaptchaTask(siteKey, pageURL)
	case "anticaptcha":
		return cs.submitAntiCaptchaReCaptchaTask(siteKey, pageURL)
	case "capsolver":
		return cs.submitCapsolverReCaptchaTask(siteKey, pageURL)
	default:
		return "", fmt.Errorf("unsupported captcha solving service: %s", cs.service)
	}
}

// submitHCaptchaTask submits an hCaptcha task to the solving service
func (cs *CaptchaSolver) submitHCaptchaTask(siteKey, pageURL string) (string, error) {
	switch cs.service {
	case "2captcha":
		return cs.submit2CaptchaHCaptchaTask(siteKey, pageURL)
	case "anticaptcha":
		return cs.submitAntiCaptchaHCaptchaTask(siteKey, pageURL)
	case "capsolver":
		return cs.submitCapsolverHCaptchaTask(siteKey, pageURL)
	default:
		return "", fmt.Errorf("unsupported captcha solving service: %s", cs.service)
	}
}

// waitForCaptchaSolution waits for the captcha solution
func (cs *CaptchaSolver) waitForCaptchaSolution(taskID string) (string, error) {
	switch cs.service {
	case "2captcha":
		return cs.wait2CaptchaSolution(taskID)
	case "anticaptcha":
		return cs.waitAntiCaptchaSolution(taskID)
	case "capsolver":
		return cs.waitCapsolverSolution(taskID)
	default:
		return "", fmt.Errorf("unsupported captcha solving service: %s", cs.service)
	}
}

// 2Captcha API implementation

// submit2CaptchaReCaptchaTask submits a reCAPTCHA task to 2Captcha
func (cs *CaptchaSolver) submit2CaptchaReCaptchaTask(siteKey, pageURL string) (string, error) {
	cs.logger.Printf("Submitting reCAPTCHA task to 2Captcha: %s, %s", siteKey, pageURL)

	// Prepare the request URL
	url := fmt.Sprintf("https://2captcha.com/in.php?key=%s&method=userrecaptcha&googlekey=%s&pageurl=%s&json=1",
		cs.apiKey, siteKey, pageURL)

	// Send the request
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("failed to send request to 2Captcha: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read 2Captcha response: %w", err)
	}

	// Parse the response
	var result struct {
		Status  int    `json:"status"`
		Request string `json:"request"`
		Error   string `json:"error"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", fmt.Errorf("failed to parse 2Captcha response: %w", err)
	}

	// Check for errors
	if result.Status != 1 {
		return "", fmt.Errorf("2Captcha error: %s", result.Error)
	}

	return result.Request, nil
}

// submit2CaptchaHCaptchaTask submits an hCaptcha task to 2Captcha
func (cs *CaptchaSolver) submit2CaptchaHCaptchaTask(siteKey, pageURL string) (string, error) {
	cs.logger.Printf("Submitting hCaptcha task to 2Captcha: %s, %s", siteKey, pageURL)

	// Prepare the request URL
	url := fmt.Sprintf("https://2captcha.com/in.php?key=%s&method=hcaptcha&sitekey=%s&pageurl=%s&json=1",
		cs.apiKey, siteKey, pageURL)

	// Send the request
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("failed to send request to 2Captcha: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read 2Captcha response: %w", err)
	}

	// Parse the response
	var result struct {
		Status  int    `json:"status"`
		Request string `json:"request"`
		Error   string `json:"error"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", fmt.Errorf("failed to parse 2Captcha response: %w", err)
	}

	// Check for errors
	if result.Status != 1 {
		return "", fmt.Errorf("2Captcha error: %s", result.Error)
	}

	return result.Request, nil
}

// wait2CaptchaSolution waits for the 2Captcha solution
func (cs *CaptchaSolver) wait2CaptchaSolution(taskID string) (string, error) {
	cs.logger.Printf("Waiting for 2Captcha solution: %s", taskID)

	// Wait for the solution
	startTime := time.Now()
	for {
		// Check if we've exceeded the timeout
		if time.Since(startTime) > cs.timeout {
			return "", fmt.Errorf("timeout waiting for 2Captcha solution")
		}

		// Wait before checking
		time.Sleep(cs.interval)

		// Prepare the request URL
		url := fmt.Sprintf("https://2captcha.com/res.php?key=%s&action=get&id=%s&json=1",
			cs.apiKey, taskID)

		// Send the request
		resp, err := http.Get(url)
		if err != nil {
			cs.logger.Printf("Failed to send request to 2Captcha: %v", err)
			continue
		}

		// Read the response
		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			cs.logger.Printf("Failed to read 2Captcha response: %v", err)
			continue
		}

		// Parse the response
		var result struct {
			Status  int    `json:"status"`
			Request string `json:"request"`
			Error   string `json:"error"`
		}

		err = json.Unmarshal(body, &result)
		if err != nil {
			cs.logger.Printf("Failed to parse 2Captcha response: %v", err)
			continue
		}

		// Check for errors
		if result.Status != 1 {
			if result.Error == "CAPCHA_NOT_READY" {
				cs.logger.Printf("2Captcha solution not ready yet")
				continue
			}
			return "", fmt.Errorf("2Captcha error: %s", result.Error)
		}

		// Solution found
		return result.Request, nil
	}
}

// Anti-Captcha API implementation

// submitAntiCaptchaReCaptchaTask submits a reCAPTCHA task to Anti-Captcha
func (cs *CaptchaSolver) submitAntiCaptchaReCaptchaTask(siteKey, pageURL string) (string, error) {
	cs.logger.Printf("Submitting reCAPTCHA task to Anti-Captcha: %s, %s", siteKey, pageURL)

	// Prepare the request payload
	type createTaskRequest struct {
		ClientKey string `json:"clientKey"`
		Task      struct {
			Type        string `json:"type"`
			WebsiteURL  string `json:"websiteURL"`
			WebsiteKey  string `json:"websiteKey"`
			IsInvisible bool   `json:"isInvisible,omitempty"`
		} `json:"task"`
		SoftID int `json:"softId,omitempty"`
	}

	req := createTaskRequest{
		ClientKey: cs.apiKey,
		Task: struct {
			Type        string `json:"type"`
			WebsiteURL  string `json:"websiteURL"`
			WebsiteKey  string `json:"websiteKey"`
			IsInvisible bool   `json:"isInvisible,omitempty"`
		}{
			Type:       "NoCaptchaTaskProxyless",
			WebsiteURL: pageURL,
			WebsiteKey: siteKey,
		},
		SoftID: 0, // Optional software ID
	}

	// Convert request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal Anti-Captcha request: %w", err)
	}

	// Send the request
	resp, err := http.Post(
		"https://api.anti-captcha.com/createTask",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return "", fmt.Errorf("failed to send request to Anti-Captcha: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read Anti-Captcha response: %w", err)
	}

	// Parse the response
	var result struct {
		ErrorID          int    `json:"errorId"`
		ErrorCode        string `json:"errorCode"`
		ErrorDescription string `json:"errorDescription"`
		TaskID           int    `json:"taskId"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", fmt.Errorf("failed to parse Anti-Captcha response: %w", err)
	}

	// Check for errors
	if result.ErrorID != 0 {
		return "", fmt.Errorf("Anti-Captcha error: %s - %s", result.ErrorCode, result.ErrorDescription)
	}

	// Return the task ID as a string
	return fmt.Sprintf("%d", result.TaskID), nil
}

// submitAntiCaptchaHCaptchaTask submits an hCaptcha task to Anti-Captcha
func (cs *CaptchaSolver) submitAntiCaptchaHCaptchaTask(siteKey, pageURL string) (string, error) {
	cs.logger.Printf("Submitting hCaptcha task to Anti-Captcha: %s, %s", siteKey, pageURL)

	// Prepare the request payload
	type createTaskRequest struct {
		ClientKey string `json:"clientKey"`
		Task      struct {
			Type       string `json:"type"`
			WebsiteURL string `json:"websiteURL"`
			WebsiteKey string `json:"websiteKey"`
		} `json:"task"`
		SoftID int `json:"softId,omitempty"`
	}

	req := createTaskRequest{
		ClientKey: cs.apiKey,
		Task: struct {
			Type       string `json:"type"`
			WebsiteURL string `json:"websiteURL"`
			WebsiteKey string `json:"websiteKey"`
		}{
			Type:       "HCaptchaTaskProxyless",
			WebsiteURL: pageURL,
			WebsiteKey: siteKey,
		},
		SoftID: 0, // Optional software ID
	}

	// Convert request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal Anti-Captcha request: %w", err)
	}

	// Send the request
	resp, err := http.Post(
		"https://api.anti-captcha.com/createTask",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return "", fmt.Errorf("failed to send request to Anti-Captcha: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read Anti-Captcha response: %w", err)
	}

	// Parse the response
	var result struct {
		ErrorID          int    `json:"errorId"`
		ErrorCode        string `json:"errorCode"`
		ErrorDescription string `json:"errorDescription"`
		TaskID           int    `json:"taskId"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", fmt.Errorf("failed to parse Anti-Captcha response: %w", err)
	}

	// Check for errors
	if result.ErrorID != 0 {
		return "", fmt.Errorf("Anti-Captcha error: %s - %s", result.ErrorCode, result.ErrorDescription)
	}

	// Return the task ID as a string
	return fmt.Sprintf("%d", result.TaskID), nil
}

// waitAntiCaptchaSolution waits for the Anti-Captcha solution
func (cs *CaptchaSolver) waitAntiCaptchaSolution(taskID string) (string, error) {
	cs.logger.Printf("Waiting for Anti-Captcha solution: %s", taskID)

	// Convert task ID to int
	taskIDInt, err := strconv.Atoi(taskID)
	if err != nil {
		return "", fmt.Errorf("invalid task ID: %w", err)
	}

	// Prepare the request payload
	type getTaskResultRequest struct {
		ClientKey string `json:"clientKey"`
		TaskID    int    `json:"taskId"`
	}

	req := getTaskResultRequest{
		ClientKey: cs.apiKey,
		TaskID:    taskIDInt,
	}

	// Wait for the solution
	startTime := time.Now()
	for {
		// Check if we've exceeded the timeout
		if time.Since(startTime) > cs.timeout {
			return "", fmt.Errorf("timeout waiting for Anti-Captcha solution")
		}

		// Wait before checking
		time.Sleep(cs.interval)

		// Convert request to JSON
		jsonData, err := json.Marshal(req)
		if err != nil {
			cs.logger.Printf("Failed to marshal Anti-Captcha request: %v", err)
			continue
		}

		// Send the request
		resp, err := http.Post(
			"https://api.anti-captcha.com/getTaskResult",
			"application/json",
			bytes.NewBuffer(jsonData),
		)
		if err != nil {
			cs.logger.Printf("Failed to send request to Anti-Captcha: %v", err)
			continue
		}

		// Read the response
		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			cs.logger.Printf("Failed to read Anti-Captcha response: %v", err)
			continue
		}

		// Parse the response
		var result struct {
			ErrorID          int    `json:"errorId"`
			ErrorCode        string `json:"errorCode"`
			ErrorDescription string `json:"errorDescription"`
			Status           string `json:"status"`
			Solution         struct {
				GRecaptchaResponse string `json:"gRecaptchaResponse"`
				HCaptchaResponse   string `json:"hCaptchaResponse"`
			} `json:"solution"`
		}

		err = json.Unmarshal(body, &result)
		if err != nil {
			cs.logger.Printf("Failed to parse Anti-Captcha response: %v", err)
			continue
		}

		// Check for errors
		if result.ErrorID != 0 {
			return "", fmt.Errorf("Anti-Captcha error: %s - %s", result.ErrorCode, result.ErrorDescription)
		}

		// Check if the task is still processing
		if result.Status == "processing" {
			cs.logger.Printf("Anti-Captcha solution not ready yet")
			continue
		}

		// Solution found
		if result.Status == "ready" {
			// Return the appropriate solution based on the captcha type
			if result.Solution.GRecaptchaResponse != "" {
				return result.Solution.GRecaptchaResponse, nil
			}
			if result.Solution.HCaptchaResponse != "" {
				return result.Solution.HCaptchaResponse, nil
			}
			return "", fmt.Errorf("Anti-Captcha returned empty solution")
		}

		// Unknown status
		return "", fmt.Errorf("Anti-Captcha returned unknown status: %s", result.Status)
	}
}

// Capsolver API implementation

// submitCapsolverReCaptchaTask submits a reCAPTCHA task to Capsolver
func (cs *CaptchaSolver) submitCapsolverReCaptchaTask(siteKey, pageURL string) (string, error) {
	cs.logger.Printf("Submitting reCAPTCHA task to Capsolver: %s, %s", siteKey, pageURL)

	// Prepare the request payload
	type createTaskRequest struct {
		APIKey string `json:"clientKey"`
		Task   struct {
			Type        string `json:"type"`
			WebsiteURL  string `json:"websiteURL"`
			WebsiteKey  string `json:"websiteKey"`
			IsInvisible bool   `json:"isInvisible,omitempty"`
		} `json:"task"`
	}

	req := createTaskRequest{
		APIKey: cs.apiKey,
		Task: struct {
			Type        string `json:"type"`
			WebsiteURL  string `json:"websiteURL"`
			WebsiteKey  string `json:"websiteKey"`
			IsInvisible bool   `json:"isInvisible,omitempty"`
		}{
			Type:       "ReCaptchaV2Task",
			WebsiteURL: pageURL,
			WebsiteKey: siteKey,
		},
	}

	// Convert request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal Capsolver request: %w", err)
	}

	// Send the request
	resp, err := http.Post(
		"https://api.capsolver.com/createTask",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return "", fmt.Errorf("failed to send request to Capsolver: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read Capsolver response: %w", err)
	}

	// Parse the response
	var result struct {
		ErrorID          int    `json:"errorId"`
		ErrorCode        string `json:"errorCode"`
		ErrorDescription string `json:"errorDescription"`
		TaskID           string `json:"taskId"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", fmt.Errorf("failed to parse Capsolver response: %w", err)
	}

	// Check for errors
	if result.ErrorID != 0 {
		return "", fmt.Errorf("Capsolver error: %s - %s", result.ErrorCode, result.ErrorDescription)
	}

	return result.TaskID, nil
}

// submitCapsolverHCaptchaTask submits an hCaptcha task to Capsolver
func (cs *CaptchaSolver) submitCapsolverHCaptchaTask(siteKey, pageURL string) (string, error) {
	cs.logger.Printf("Submitting hCaptcha task to Capsolver: %s, %s", siteKey, pageURL)

	// Prepare the request payload
	type createTaskRequest struct {
		APIKey string `json:"clientKey"`
		Task   struct {
			Type       string `json:"type"`
			WebsiteURL string `json:"websiteURL"`
			WebsiteKey string `json:"websiteKey"`
		} `json:"task"`
	}

	req := createTaskRequest{
		APIKey: cs.apiKey,
		Task: struct {
			Type       string `json:"type"`
			WebsiteURL string `json:"websiteURL"`
			WebsiteKey string `json:"websiteKey"`
		}{
			Type:       "HCaptchaTask",
			WebsiteURL: pageURL,
			WebsiteKey: siteKey,
		},
	}

	// Convert request to JSON
	jsonData, err := json.Marshal(req)
	if err != nil {
		return "", fmt.Errorf("failed to marshal Capsolver request: %w", err)
	}

	// Send the request
	resp, err := http.Post(
		"https://api.capsolver.com/createTask",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	if err != nil {
		return "", fmt.Errorf("failed to send request to Capsolver: %w", err)
	}
	defer resp.Body.Close()

	// Read the response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read Capsolver response: %w", err)
	}

	// Parse the response
	var result struct {
		ErrorID          int    `json:"errorId"`
		ErrorCode        string `json:"errorCode"`
		ErrorDescription string `json:"errorDescription"`
		TaskID           string `json:"taskId"`
	}

	err = json.Unmarshal(body, &result)
	if err != nil {
		return "", fmt.Errorf("failed to parse Capsolver response: %w", err)
	}

	// Check for errors
	if result.ErrorID != 0 {
		return "", fmt.Errorf("Capsolver error: %s - %s", result.ErrorCode, result.ErrorDescription)
	}

	return result.TaskID, nil
}

// waitCapsolverSolution waits for the Capsolver solution
func (cs *CaptchaSolver) waitCapsolverSolution(taskID string) (string, error) {
	cs.logger.Printf("Waiting for Capsolver solution: %s", taskID)

	// Prepare the request payload
	type getTaskResultRequest struct {
		APIKey string `json:"clientKey"`
		TaskID string `json:"taskId"`
	}

	req := getTaskResultRequest{
		APIKey: cs.apiKey,
		TaskID: taskID,
	}

	// Wait for the solution
	startTime := time.Now()
	for {
		// Check if we've exceeded the timeout
		if time.Since(startTime) > cs.timeout {
			return "", fmt.Errorf("timeout waiting for Capsolver solution")
		}

		// Wait before checking
		time.Sleep(cs.interval)

		// Convert request to JSON
		jsonData, err := json.Marshal(req)
		if err != nil {
			cs.logger.Printf("Failed to marshal Capsolver request: %v", err)
			continue
		}

		// Send the request
		resp, err := http.Post(
			"https://api.capsolver.com/getTaskResult",
			"application/json",
			bytes.NewBuffer(jsonData),
		)
		if err != nil {
			cs.logger.Printf("Failed to send request to Capsolver: %v", err)
			continue
		}

		// Read the response
		body, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			cs.logger.Printf("Failed to read Capsolver response: %v", err)
			continue
		}

		// Parse the response
		var result struct {
			ErrorID          int    `json:"errorId"`
			ErrorCode        string `json:"errorCode"`
			ErrorDescription string `json:"errorDescription"`
			Status           string `json:"status"`
			Solution         struct {
				GRecaptchaResponse string `json:"gRecaptchaResponse"`
				HCaptchaResponse   string `json:"hCaptchaResponse"`
			} `json:"solution"`
		}

		err = json.Unmarshal(body, &result)
		if err != nil {
			cs.logger.Printf("Failed to parse Capsolver response: %v", err)
			continue
		}

		// Check for errors
		if result.ErrorID != 0 {
			return "", fmt.Errorf("Capsolver error: %s - %s", result.ErrorCode, result.ErrorDescription)
		}

		// Check if the task is still processing
		if result.Status == "processing" {
			cs.logger.Printf("Capsolver solution not ready yet")
			continue
		}

		// Solution found
		if result.Status == "ready" {
			// Return the appropriate solution based on the captcha type
			if result.Solution.GRecaptchaResponse != "" {
				return result.Solution.GRecaptchaResponse, nil
			}
			if result.Solution.HCaptchaResponse != "" {
				return result.Solution.HCaptchaResponse, nil
			}
			return "", fmt.Errorf("Capsolver returned empty solution")
		}

		// Unknown status
		return "", fmt.Errorf("Capsolver returned unknown status: %s", result.Status)
	}
}

// applyReCaptchaSolution applies the reCAPTCHA solution to the page
func (cs *CaptchaSolver) applyReCaptchaSolution(page *rod.Page, solution string) error {
	script := `function(solution) {
		// Try to set the g-recaptcha-response textarea
		document.querySelector('textarea[name="g-recaptcha-response"]').value = solution;

		// Try to trigger the callback
		if (typeof ___grecaptcha_cfg !== 'undefined') {
			// Find the callback function
			const callbackNames = Object.keys(___grecaptcha_cfg.clients[0].R);
			for (const name of callbackNames) {
				const callback = ___grecaptcha_cfg.clients[0].R[name].callback;
				if (typeof callback === 'function') {
					callback(solution);
					return true;
				}
			}
		}

		return false;
	}`

	_, err := page.Eval(script, solution)

	return err
}

// applyHCaptchaSolution applies the hCaptcha solution to the page
func (cs *CaptchaSolver) applyHCaptchaSolution(page *rod.Page, solution string) error {
	script := `function(solution) {
		// Try to set the h-captcha-response textarea
		document.querySelector('textarea[name="h-captcha-response"]').value = solution;

		// Try to trigger the callback
		if (typeof hcaptcha !== 'undefined') {
			hcaptcha.submit(solution);
			return true;
		}

		return false;
	}`

	_, err := page.Eval(script, solution)

	return err
}
