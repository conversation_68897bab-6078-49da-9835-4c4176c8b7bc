package rod

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/go-rod/rod"
	"github.com/go-rod/rod/lib/launcher"
	"github.com/go-rod/rod/lib/proto"
	"github.com/go-rod/stealth"
)

// ResourceManager manages browser and page resources
type ResourceManager struct {
	logger             *log.Logger
	browserPool        *BrowserPool
	activeBrowsers     map[string]*ManagedBrowser
	activePages        map[string]*ManagedPage
	mu                 sync.RWMutex
	shutdownCh         chan struct{}
	cleanupInterval    time.Duration
	maxIdleTime        time.Duration
	maxBrowsers        int
	maxPagesPerBrowser int
}

// ManagedBrowser represents a browser instance with lifecycle management
type ManagedBrowser struct {
	ID         string
	Browser    *rod.Browser
	Launcher   *launcher.Launcher
	Settings   TaskSettings
	CreatedAt  time.Time
	LastUsedAt time.Time
	PageCount  int
	IsActive   bool
	ctx        context.Context
	cancel     context.CancelFunc
	mu         sync.RWMutex
}

// ManagedPage represents a page instance with lifecycle management
type ManagedPage struct {
	ID         string
	Page       *rod.Page
	BrowserID  string
	CreatedAt  time.Time
	LastUsedAt time.Time
	IsActive   bool
	ctx        context.Context
	cancel     context.CancelFunc
	mu         sync.RWMutex
}

// BrowserPool manages a pool of browser instances for reuse
type BrowserPool struct {
	browsers   []*ManagedBrowser
	maxSize    int
	mu         sync.RWMutex
	newBrowser func() (*ManagedBrowser, error)
}

// ResourceManagerOptions contains options for the resource manager
type ResourceManagerOptions struct {
	MaxBrowsers        int
	MaxPagesPerBrowser int
	CleanupInterval    time.Duration
	MaxIdleTime        time.Duration
	BrowserPoolSize    int
}

// NewResourceManager creates a new resource manager
func NewResourceManager(logger *log.Logger, opts *ResourceManagerOptions) *ResourceManager {
	if opts == nil {
		opts = &ResourceManagerOptions{
			MaxBrowsers:        5,
			MaxPagesPerBrowser: 10,
			CleanupInterval:    5 * time.Minute,
			MaxIdleTime:        10 * time.Minute,
			BrowserPoolSize:    3,
		}
	}

	rm := &ResourceManager{
		logger:             logger,
		activeBrowsers:     make(map[string]*ManagedBrowser),
		activePages:        make(map[string]*ManagedPage),
		shutdownCh:         make(chan struct{}),
		cleanupInterval:    opts.CleanupInterval,
		maxIdleTime:        opts.MaxIdleTime,
		maxBrowsers:        opts.MaxBrowsers,
		maxPagesPerBrowser: opts.MaxPagesPerBrowser,
	}

	// Initialize browser pool
	rm.browserPool = &BrowserPool{
		browsers: make([]*ManagedBrowser, 0, opts.BrowserPoolSize),
		maxSize:  opts.BrowserPoolSize,
		newBrowser: func() (*ManagedBrowser, error) {
			return rm.createBrowser(context.Background(), TaskSettings{
				Headless:       true,
				ViewportWidth:  1366,
				ViewportHeight: 768,
				AntiDetection:  true,
			})
		},
	}

	// Start cleanup goroutine
	go rm.startCleanupRoutine()

	return rm
}

// GetBrowser gets or creates a browser instance
func (rm *ResourceManager) GetBrowser(ctx context.Context, settings TaskSettings) (*ManagedBrowser, error) {
	// Try to get from pool first
	if browser := rm.browserPool.Get(); browser != nil {
		rm.mu.Lock()
		rm.activeBrowsers[browser.ID] = browser
		rm.mu.Unlock()
		browser.updateLastUsed()
		return browser, nil
	}

	// Check if we've reached the limit
	rm.mu.RLock()
	if len(rm.activeBrowsers) >= rm.maxBrowsers {
		rm.mu.RUnlock()
		return nil, NewError(ErrCodeResourceExhausted,
			fmt.Sprintf("Maximum number of browsers (%d) reached", rm.maxBrowsers), nil)
	}
	rm.mu.RUnlock()

	// Create new browser
	browser, err := rm.createBrowser(ctx, settings)
	if err != nil {
		return nil, NewBrowserInitError(err)
	}

	rm.mu.Lock()
	rm.activeBrowsers[browser.ID] = browser
	rm.mu.Unlock()

	return browser, nil
}

// GetPage gets or creates a page instance
func (rm *ResourceManager) GetPage(ctx context.Context, browserID string) (*ManagedPage, error) {
	rm.mu.RLock()
	browser, exists := rm.activeBrowsers[browserID]
	rm.mu.RUnlock()

	if !exists {
		return nil, NewError(ErrCodeBrowserClosed, "Browser not found or closed", nil)
	}

	// Check page limit for this browser
	browser.mu.RLock()
	if browser.PageCount >= rm.maxPagesPerBrowser {
		browser.mu.RUnlock()
		return nil, NewError(ErrCodeResourceExhausted,
			fmt.Sprintf("Maximum pages per browser (%d) reached", rm.maxPagesPerBrowser), nil)
	}
	browser.mu.RUnlock()

	// Create new page
	page, err := rm.createPage(ctx, browser)
	if err != nil {
		return nil, NewError(ErrCodePageCreate, "Failed to create page", err)
	}

	rm.mu.Lock()
	rm.activePages[page.ID] = page
	rm.mu.Unlock()

	browser.mu.Lock()
	browser.PageCount++
	browser.mu.Unlock()

	return page, nil
}

// ReleaseBrowser releases a browser back to the pool or closes it
func (rm *ResourceManager) ReleaseBrowser(browserID string) error {
	rm.mu.Lock()
	browser, exists := rm.activeBrowsers[browserID]
	if !exists {
		rm.mu.Unlock()
		return nil // Already released
	}
	delete(rm.activeBrowsers, browserID)
	rm.mu.Unlock()

	// Close all pages for this browser
	rm.mu.RLock()
	for pageID, page := range rm.activePages {
		if page.BrowserID == browserID {
			go rm.ReleasePage(pageID) // Release in background
		}
	}
	rm.mu.RUnlock()

	// Try to return to pool if it's reusable
	if rm.browserPool.Put(browser) {
		return nil
	}

	// Otherwise close it
	return rm.closeBrowser(browser)
}

// ReleasePage releases a page
func (rm *ResourceManager) ReleasePage(pageID string) error {
	rm.mu.Lock()
	page, exists := rm.activePages[pageID]
	if !exists {
		rm.mu.Unlock()
		return nil // Already released
	}
	delete(rm.activePages, pageID)
	rm.mu.Unlock()

	// Update browser page count
	rm.mu.RLock()
	browser, exists := rm.activeBrowsers[page.BrowserID]
	rm.mu.RUnlock()

	if exists {
		browser.mu.Lock()
		browser.PageCount--
		browser.mu.Unlock()
	}

	return rm.closePage(page)
}

// Shutdown gracefully shuts down the resource manager
func (rm *ResourceManager) Shutdown(ctx context.Context) error {
	rm.logger.Println("Shutting down resource manager...")

	// Signal cleanup routine to stop
	close(rm.shutdownCh)

	// Create error group for collecting errors
	errorGroup := NewErrorGroup()

	// Close all active pages
	rm.mu.RLock()
	pageIDs := make([]string, 0, len(rm.activePages))
	for pageID := range rm.activePages {
		pageIDs = append(pageIDs, pageID)
	}
	rm.mu.RUnlock()

	for _, pageID := range pageIDs {
		if err := rm.ReleasePage(pageID); err != nil {
			errorGroup.Add(err)
		}
	}

	// Close all active browsers
	rm.mu.RLock()
	browserIDs := make([]string, 0, len(rm.activeBrowsers))
	for browserID := range rm.activeBrowsers {
		browserIDs = append(browserIDs, browserID)
	}
	rm.mu.RUnlock()

	for _, browserID := range browserIDs {
		if err := rm.ReleaseBrowser(browserID); err != nil {
			errorGroup.Add(err)
		}
	}

	// Close browser pool
	if err := rm.browserPool.Close(); err != nil {
		errorGroup.Add(err)
	}

	if errorGroup.HasErrors() {
		return errorGroup
	}

	rm.logger.Println("Resource manager shutdown complete")
	return nil
}

// createBrowser creates a new managed browser
func (rm *ResourceManager) createBrowser(ctx context.Context, settings TaskSettings) (*ManagedBrowser, error) {
	// Create launcher
	l := launcher.New()

	// Configure launcher based on settings
	if settings.Headless {
		l = l.Headless(true)
	}

	if settings.UserDataDir != "" {
		l = l.UserDataDir(settings.UserDataDir)
	}

	if settings.Proxy != "" {
		l = l.Proxy(settings.Proxy)
	}

	// Add common flags for stability
	l = l.NoSandbox(true).
		Set("disable-web-security", "true").
		Set("disable-features", "VizDisplayCompositor").
		Set("disable-dev-shm-usage", "true")

	// Launch browser
	url, err := l.Launch()
	if err != nil {
		return nil, fmt.Errorf("failed to launch browser: %w", err)
	}

	// Connect to browser
	browser := rod.New().ControlURL(url)

	// Create context with cancellation
	browserCtx, cancel := context.WithCancel(ctx)

	// Connect with context
	err = browser.Context(browserCtx).Connect()
	if err != nil {
		cancel()
		return nil, fmt.Errorf("failed to connect to browser: %w", err)
	}

	browserID := generateID("browser")

	managedBrowser := &ManagedBrowser{
		ID:         browserID,
		Browser:    browser,
		Launcher:   l,
		Settings:   settings,
		CreatedAt:  time.Now(),
		LastUsedAt: time.Now(),
		IsActive:   true,
		ctx:        browserCtx,
		cancel:     cancel,
	}

	rm.logger.Printf("Created browser %s", browserID)
	return managedBrowser, nil
}

// createPage creates a new managed page
func (rm *ResourceManager) createPage(ctx context.Context, browser *ManagedBrowser) (*ManagedPage, error) {
	// Create page context
	pageCtx, cancel := context.WithCancel(ctx)

	var page *rod.Page
	var err error

	// Create page with stealth if anti-detection is enabled
	if browser.Settings.AntiDetection {
		page, err = stealth.Page(browser.Browser)
		if err != nil {
			cancel()
			return nil, fmt.Errorf("failed to create stealth page: %w", err)
		}
	} else {
		page, err = browser.Browser.Page(proto.TargetCreateTarget{})
		if err != nil {
			cancel()
			return nil, fmt.Errorf("failed to create page: %w", err)
		}
	}

	// Set viewport
	if browser.Settings.ViewportWidth > 0 && browser.Settings.ViewportHeight > 0 {
		err = page.SetViewport(&proto.EmulationSetDeviceMetricsOverride{
			Width:  browser.Settings.ViewportWidth,
			Height: browser.Settings.ViewportHeight,
		})
		if err != nil {
			cancel()
			page.Close()
			return nil, fmt.Errorf("failed to set viewport: %w", err)
		}
	}

	// Set user agent if specified
	if browser.Settings.UserAgent != "" {
		err = page.SetUserAgent(&proto.NetworkSetUserAgentOverride{
			UserAgent: browser.Settings.UserAgent,
		})
		if err != nil {
			cancel()
			page.Close()
			return nil, fmt.Errorf("failed to set user agent: %w", err)
		}
	}

	pageID := generateID("page")

	managedPage := &ManagedPage{
		ID:         pageID,
		Page:       page,
		BrowserID:  browser.ID,
		CreatedAt:  time.Now(),
		LastUsedAt: time.Now(),
		IsActive:   true,
		ctx:        pageCtx,
		cancel:     cancel,
	}

	rm.logger.Printf("Created page %s for browser %s", pageID, browser.ID)
	return managedPage, nil
}

// closeBrowser closes a managed browser
func (rm *ResourceManager) closeBrowser(browser *ManagedBrowser) error {
	rm.logger.Printf("Closing browser %s", browser.ID)

	browser.mu.Lock()
	browser.IsActive = false
	browser.mu.Unlock()

	// Cancel context
	browser.cancel()

	// Close browser
	if browser.Browser != nil {
		browser.Browser.Close()
	}

	return nil
}

// closePage closes a managed page
func (rm *ResourceManager) closePage(page *ManagedPage) error {
	rm.logger.Printf("Closing page %s", page.ID)

	page.mu.Lock()
	page.IsActive = false
	page.mu.Unlock()

	// Cancel context
	page.cancel()

	// Close page
	if page.Page != nil {
		page.Page.Close()
	}

	return nil
}

// startCleanupRoutine starts the resource cleanup routine
func (rm *ResourceManager) startCleanupRoutine() {
	ticker := time.NewTicker(rm.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			rm.cleanup()
		case <-rm.shutdownCh:
			return
		}
	}
}

// cleanup performs resource cleanup
func (rm *ResourceManager) cleanup() {
	now := time.Now()

	// Cleanup idle pages
	rm.mu.RLock()
	idlePages := make([]string, 0)
	for pageID, page := range rm.activePages {
		page.mu.RLock()
		if now.Sub(page.LastUsedAt) > rm.maxIdleTime {
			idlePages = append(idlePages, pageID)
		}
		page.mu.RUnlock()
	}
	rm.mu.RUnlock()

	for _, pageID := range idlePages {
		rm.logger.Printf("Cleaning up idle page %s", pageID)
		rm.ReleasePage(pageID)
	}

	// Cleanup idle browsers
	rm.mu.RLock()
	idleBrowsers := make([]string, 0)
	for browserID, browser := range rm.activeBrowsers {
		browser.mu.RLock()
		if browser.PageCount == 0 && now.Sub(browser.LastUsedAt) > rm.maxIdleTime {
			idleBrowsers = append(idleBrowsers, browserID)
		}
		browser.mu.RUnlock()
	}
	rm.mu.RUnlock()

	for _, browserID := range idleBrowsers {
		rm.logger.Printf("Cleaning up idle browser %s", browserID)
		rm.ReleaseBrowser(browserID)
	}
}

// ManagedBrowser methods

// UpdateLastUsed updates the last used timestamp
func (mb *ManagedBrowser) updateLastUsed() {
	mb.mu.Lock()
	mb.LastUsedAt = time.Now()
	mb.mu.Unlock()
}

// IsIdle checks if the browser is idle
func (mb *ManagedBrowser) IsIdle(maxIdleTime time.Duration) bool {
	mb.mu.RLock()
	defer mb.mu.RUnlock()
	return time.Since(mb.LastUsedAt) > maxIdleTime && mb.PageCount == 0
}

// ManagedPage methods

// UpdateLastUsed updates the last used timestamp
func (mp *ManagedPage) updateLastUsed() {
	mp.mu.Lock()
	mp.LastUsedAt = time.Now()
	mp.mu.Unlock()
}

// IsIdle checks if the page is idle
func (mp *ManagedPage) IsIdle(maxIdleTime time.Duration) bool {
	mp.mu.RLock()
	defer mp.mu.RUnlock()
	return time.Since(mp.LastUsedAt) > maxIdleTime
}

// BrowserPool methods

// Get gets a browser from the pool
func (bp *BrowserPool) Get() *ManagedBrowser {
	bp.mu.Lock()
	defer bp.mu.Unlock()

	if len(bp.browsers) == 0 {
		return nil
	}

	// Get the last browser (LIFO)
	browser := bp.browsers[len(bp.browsers)-1]
	bp.browsers = bp.browsers[:len(bp.browsers)-1]

	return browser
}

// Put puts a browser back to the pool
func (bp *BrowserPool) Put(browser *ManagedBrowser) bool {
	if browser == nil {
		return false
	}

	bp.mu.Lock()
	defer bp.mu.Unlock()

	// Check if pool is full
	if len(bp.browsers) >= bp.maxSize {
		return false
	}

	// Reset browser state
	browser.updateLastUsed()
	browser.PageCount = 0

	bp.browsers = append(bp.browsers, browser)
	return true
}

// Close closes all browsers in the pool
func (bp *BrowserPool) Close() error {
	bp.mu.Lock()
	defer bp.mu.Unlock()

	errorGroup := NewErrorGroup()
	for _, browser := range bp.browsers {
		if browser.Browser != nil {
			browser.Browser.Close()
		}
	}

	bp.browsers = bp.browsers[:0]

	if errorGroup.HasErrors() {
		return errorGroup
	}
	return nil
}

// generateID generates a unique ID
func generateID(prefix string) string {
	return fmt.Sprintf("%s_%d", prefix, time.Now().UnixNano())
}
