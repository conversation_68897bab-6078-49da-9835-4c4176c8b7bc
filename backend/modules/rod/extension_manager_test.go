package rod

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestNewExtensionManager tests the creation of a new extension manager
func TestNewExtensionManager(t *testing.T) {
	logger := &MockLogger{}
	userDataDir := "/tmp/test-user-data"

	em := NewExtensionManager(logger, userDataDir)
	assert.NotNil(t, em, "Extension manager should not be nil")
	assert.Equal(t, logger, em.logger, "Logger should be set correctly")
	assert.Equal(t, userDataDir, em.userDataDir, "User data directory should be set correctly")
}

// TestListInstalledExtensions tests listing installed extensions
func TestListInstalledExtensions(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "rod-extension-test-*")
	require.NoError(t, err, "Failed to create temp directory")
	defer os.RemoveAll(tempDir)

	// Create a mock extension directory structure
	extensionsDir := filepath.Join(tempDir, "Extensions")
	err = os.MkdirAll(extensionsDir, 0755)
	require.NoError(t, err, "Failed to create extensions directory")

	// Create a mock extension
	extensionID := "abcdefghijklmnopqrstuvwxyz123456"
	extensionDir := filepath.Join(extensionsDir, extensionID)
	err = os.MkdirAll(extensionDir, 0755)
	require.NoError(t, err, "Failed to create extension directory")

	// Create a mock version directory
	versionDir := filepath.Join(extensionDir, "1.0")
	err = os.MkdirAll(versionDir, 0755)
	require.NoError(t, err, "Failed to create version directory")

	// Create a mock manifest.json file
	manifest := map[string]interface{}{
		"name":        "Test Extension",
		"version":     "1.0",
		"description": "A test extension",
	}
	manifestData, err := json.MarshalIndent(manifest, "", "  ")
	require.NoError(t, err, "Failed to marshal manifest")

	manifestPath := filepath.Join(versionDir, "manifest.json")
	err = os.WriteFile(manifestPath, manifestData, 0644)
	require.NoError(t, err, "Failed to write manifest file")

	// Create a mock preferences file
	preferencesDir := filepath.Join(tempDir, "Default")
	err = os.MkdirAll(preferencesDir, 0755)
	require.NoError(t, err, "Failed to create preferences directory")

	preferences := map[string]interface{}{
		"extensions": map[string]interface{}{
			"settings": map[string]interface{}{
				extensionID: map[string]interface{}{
					"state": 1,
				},
			},
		},
	}
	preferencesData, err := json.MarshalIndent(preferences, "", "  ")
	require.NoError(t, err, "Failed to marshal preferences")

	preferencesPath := filepath.Join(preferencesDir, "Preferences")
	err = os.WriteFile(preferencesPath, preferencesData, 0644)
	require.NoError(t, err, "Failed to write preferences file")

	// Create an extension manager
	logger := &MockLogger{}
	em := NewExtensionManager(logger, tempDir)

	// List installed extensions
	extensions, err := em.ListInstalledExtensions()
	assert.NoError(t, err, "ListInstalledExtensions should not return an error")
	assert.Len(t, extensions, 1, "There should be one extension")

	// Check the extension info
	extension := extensions[0]
	assert.Equal(t, extensionID, extension.ID, "Extension ID should match")
	assert.Equal(t, "Test Extension", extension.Name, "Extension name should match")
	assert.Equal(t, "1.0", extension.Version, "Extension version should match")
	assert.Equal(t, "A test extension", extension.Description, "Extension description should match")
	assert.True(t, extension.Enabled, "Extension should be enabled")
	assert.Equal(t, versionDir, extension.Path, "Extension path should match")
}

// TestEnableDisableExtension tests enabling and disabling an extension
func TestEnableDisableExtension(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "rod-extension-test-*")
	require.NoError(t, err, "Failed to create temp directory")
	defer os.RemoveAll(tempDir)

	// Create a mock preferences file
	preferencesDir := filepath.Join(tempDir, "Default")
	err = os.MkdirAll(preferencesDir, 0755)
	require.NoError(t, err, "Failed to create preferences directory")

	extensionID := "abcdefghijklmnopqrstuvwxyz123456"
	preferences := map[string]interface{}{
		"extensions": map[string]interface{}{
			"settings": map[string]interface{}{
				extensionID: map[string]interface{}{
					"state": 1,
				},
			},
		},
	}
	preferencesData, err := json.MarshalIndent(preferences, "", "  ")
	require.NoError(t, err, "Failed to marshal preferences")

	preferencesPath := filepath.Join(preferencesDir, "Preferences")
	err = os.WriteFile(preferencesPath, preferencesData, 0644)
	require.NoError(t, err, "Failed to write preferences file")

	// Create an extension manager
	logger := &MockLogger{}
	em := NewExtensionManager(logger, tempDir)

	// Disable the extension
	err = em.DisableExtension(extensionID)
	assert.NoError(t, err, "DisableExtension should not return an error")

	// Read the preferences file to verify the extension is disabled
	preferencesData, err = os.ReadFile(preferencesPath)
	require.NoError(t, err, "Failed to read preferences file")

	err = json.Unmarshal(preferencesData, &preferences)
	require.NoError(t, err, "Failed to unmarshal preferences")

	settings := preferences["extensions"].(map[string]interface{})["settings"].(map[string]interface{})
	extensionSettings := settings[extensionID].(map[string]interface{})
	assert.Equal(t, float64(0), extensionSettings["state"], "Extension should be disabled")

	// Enable the extension
	err = em.EnableExtension(extensionID)
	assert.NoError(t, err, "EnableExtension should not return an error")

	// Read the preferences file to verify the extension is enabled
	preferencesData, err = os.ReadFile(preferencesPath)
	require.NoError(t, err, "Failed to read preferences file")

	err = json.Unmarshal(preferencesData, &preferences)
	require.NoError(t, err, "Failed to unmarshal preferences")

	settings = preferences["extensions"].(map[string]interface{})["settings"].(map[string]interface{})
	extensionSettings = settings[extensionID].(map[string]interface{})
	assert.Equal(t, float64(1), extensionSettings["state"], "Extension should be enabled")
}

// TestRemoveExtension tests removing an extension
func TestRemoveExtension(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "rod-extension-test-*")
	require.NoError(t, err, "Failed to create temp directory")
	defer os.RemoveAll(tempDir)

	// Create a mock extension directory structure
	extensionsDir := filepath.Join(tempDir, "Extensions")
	err = os.MkdirAll(extensionsDir, 0755)
	require.NoError(t, err, "Failed to create extensions directory")

	// Create a mock extension
	extensionID := "abcdefghijklmnopqrstuvwxyz123456"
	extensionDir := filepath.Join(extensionsDir, extensionID)
	err = os.MkdirAll(extensionDir, 0755)
	require.NoError(t, err, "Failed to create extension directory")

	// Create a mock preferences file
	preferencesDir := filepath.Join(tempDir, "Default")
	err = os.MkdirAll(preferencesDir, 0755)
	require.NoError(t, err, "Failed to create preferences directory")

	preferences := map[string]interface{}{
		"extensions": map[string]interface{}{
			"settings": map[string]interface{}{
				extensionID: map[string]interface{}{
					"state": 1,
				},
			},
		},
	}
	preferencesData, err := json.MarshalIndent(preferences, "", "  ")
	require.NoError(t, err, "Failed to marshal preferences")

	preferencesPath := filepath.Join(preferencesDir, "Preferences")
	err = os.WriteFile(preferencesPath, preferencesData, 0644)
	require.NoError(t, err, "Failed to write preferences file")

	// Create an extension manager
	logger := &MockLogger{}
	em := NewExtensionManager(logger, tempDir)

	// Remove the extension
	err = em.RemoveExtension(extensionID)
	assert.NoError(t, err, "RemoveExtension should not return an error")

	// Verify the extension directory is removed
	_, err = os.Stat(extensionDir)
	assert.True(t, os.IsNotExist(err), "Extension directory should be removed")

	// Read the preferences file to verify the extension settings are removed
	preferencesData, err = os.ReadFile(preferencesPath)
	require.NoError(t, err, "Failed to read preferences file")

	err = json.Unmarshal(preferencesData, &preferences)
	require.NoError(t, err, "Failed to unmarshal preferences")

	settings := preferences["extensions"].(map[string]interface{})["settings"].(map[string]interface{})
	_, exists := settings[extensionID]
	assert.False(t, exists, "Extension settings should be removed")
}
