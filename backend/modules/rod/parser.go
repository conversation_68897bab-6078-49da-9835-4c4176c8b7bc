package rod

import (
	"fmt"
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// <PERSON><PERSON><PERSON> is responsible for parsing task YAML files
type Parser struct {
	// Configuration options for the parser
	StrictMode bool // If true, will error on unknown fields
}

// NewParser creates a new YAML parser
func NewParser() *Parser {
	return &Parser{
		StrictMode: false,
	}
}

// ParseFile parses a task plan from a YAML file
func (p *Parser) ParseFile(filePath string) (*TaskPlan, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read task file: %w", err)
	}

	return p.ParseBytes(data)
}

// ParseBytes parses a task plan from a byte slice
func (p *Parser) ParseBytes(data []byte) (*TaskPlan, error) {
	var plan TaskPlan
	err := yaml.Unmarshal(data, &plan)
	if err != nil {
		return nil, fmt.Errorf("failed to parse YAML: %w", err)
	}

	// Validate the plan
	if err := p.validatePlan(&plan); err != nil {
		return nil, err
	}

	return &plan, nil
}

// validatePlan validates a task plan
func (p *Parser) validatePlan(plan *TaskPlan) error {
	if plan.Name == "" {
		return fmt.Errorf("task plan must have a name")
	}

	if len(plan.Tasks) == 0 {
		return fmt.Errorf("task plan must have at least one task")
	}

	// Validate each task
	for i, task := range plan.Tasks {
		if err := p.validateTask(task); err != nil {
			return fmt.Errorf("invalid task at index %d: %w", i, err)
		}
	}

	return nil
}

// validateTask validates a single task
func (p *Parser) validateTask(task Task) error {
	if task.Type == "" {
		return fmt.Errorf("task must have a type")
	}

	// Check if the task type is valid
	validTypes := map[TaskType]bool{
		// Navigation tasks
		TaskNavigate:      true,
		TaskNavigateBack:  true,
		TaskNavigateForwd: true,
		TaskReload:        true,

		// Query tasks
		TaskClick:       true,
		TaskDoubleClick: true,
		TaskTypeText:    true,
		TaskSelect:      true,
		TaskClear:       true,
		TaskSubmit:      true,
		TaskFocus:       true,
		TaskBlur:        true,
		TaskScrollInto:  true,

		// Wait tasks
		TaskWaitVisible:    true,
		TaskWaitNotVisible: true,
		TaskWaitPresent:    true,
		TaskWaitNotPresent: true,
		TaskWaitEnabled:    true,
		TaskWaitSelected:   true,
		TaskSleep:          true,

		// Extract tasks
		TaskGetText:        true,
		TaskGetHTML:        true,
		TaskGetOuterHTML:   true,
		TaskGetAttribute:   true,
		TaskGetValue:       true,
		TaskScreenshot:     true,
		TaskFullScreenshot: true,

		// JavaScript tasks
		TaskEvaluate: true,
		TaskPoll:     true,

		// Flow control
		TaskIf:       true,
		TaskLoop:     true,
		TaskForEach:  true,
		TaskBreak:    true,
		TaskContinue: true,

		// Challenge handling tasks
		TaskDetectChallenge:   true,
		TaskHandleChallenge:   true,
		TaskSolveReCaptcha:    true,
		TaskSolveCloudflare:   true,
		TaskSolveHCaptcha:     true,
		TaskMonitorChallenges: true,

		// Extension handling tasks
		TaskInstallExtension: true,
	}

	if !validTypes[task.Type] {
		return fmt.Errorf("invalid task type: %s", task.Type)
	}

	// Validate based on task type
	switch task.Type {
	case TaskNavigate:
		if task.URL == "" {
			return fmt.Errorf("navigate task must have a URL")
		}
	case TaskClick, TaskDoubleClick, TaskTypeText, TaskSelect, TaskClear, TaskSubmit, TaskFocus, TaskBlur, TaskScrollInto:
		if task.Selector == "" {
			return fmt.Errorf("%s task must have a selector", task.Type)
		}
	case TaskWaitVisible, TaskWaitNotVisible, TaskWaitPresent, TaskWaitNotPresent, TaskWaitEnabled, TaskWaitSelected:
		if task.Selector == "" {
			return fmt.Errorf("%s task must have a selector", task.Type)
		}
	case TaskGetText, TaskGetHTML, TaskGetOuterHTML, TaskGetAttribute, TaskGetValue:
		if task.Selector == "" {
			return fmt.Errorf("%s task must have a selector", task.Type)
		}
		if task.Variable == "" {
			return fmt.Errorf("%s task must have a variable to store the result", task.Type)
		}
	case TaskScreenshot:
		if task.Selector == "" && task.Type != TaskFullScreenshot {
			return fmt.Errorf("screenshot task must have a selector")
		}
		if task.FileName == "" {
			return fmt.Errorf("screenshot task must have a filename")
		}
	case TaskEvaluate, TaskPoll:
		if task.JavaScript == "" {
			return fmt.Errorf("%s task must have JavaScript code", task.Type)
		}
	case TaskIf:
		if task.Condition == "" {
			return fmt.Errorf("if task must have a condition")
		}
		if len(task.Tasks) == 0 {
			return fmt.Errorf("if task must have subtasks")
		}
	case TaskLoop, TaskForEach:
		if len(task.Tasks) == 0 {
			return fmt.Errorf("%s task must have subtasks", task.Type)
		}
	case TaskInstallExtension:
		if task.Value == "" {
			return fmt.Errorf("install_extension task must have a value (path or URL)")
		}
	case TaskSolveReCaptcha, TaskSolveHCaptcha, TaskSolveCloudflare:
		// These tasks don't require additional validation
	case TaskDetectChallenge, TaskHandleChallenge, TaskMonitorChallenges:
		// These tasks don't require additional validation
	}

	// Validate nested tasks
	for i, subtask := range task.Tasks {
		if err := p.validateTask(subtask); err != nil {
			return fmt.Errorf("invalid subtask at index %d: %w", i, err)
		}
	}

	return nil
}

// ParseTimeout parses a timeout string into a duration
func ParseTimeout(timeout string) (time.Duration, error) {
	if timeout == "" {
		return 30 * time.Second, nil // Default timeout
	}

	return time.ParseDuration(timeout)
}

// SaveTaskPlan saves a task plan to a YAML file
func SaveTaskPlan(plan *TaskPlan, filePath string) error {
	data, err := yaml.Marshal(plan)
	if err != nil {
		return fmt.Errorf("failed to marshal task plan: %w", err)
	}

	err = os.WriteFile(filePath, data, 0644)
	if err != nil {
		return fmt.Errorf("failed to write task plan to file: %w", err)
	}

	return nil
}
