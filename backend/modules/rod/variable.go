package rod

import (
	"fmt"
	"regexp"
	"strings"
)

// Regular expression to match variable references like {{variable_name}}
var variableRegex = regexp.MustCompile(`\{\{([^{}]+)\}\}`)

// SubstituteVariables replaces variable references in a string with their values
// from the task context. Variable references are in the form {{variable_name}}.
func SubstituteVariables(input string, variables map[string]any) string {
	if input == "" {
		return input
	}

	// Find all variable references in the input string
	matches := variableRegex.FindAllStringSubmatch(input, -1)
	if len(matches) == 0 {
		return input // No variables to substitute
	}

	// Replace each variable reference with its value
	result := input
	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		varName := strings.TrimSpace(match[1])
		varValue, exists := variables[varName]
		if !exists {
			// Variable not found, leave the reference as is
			continue
		}

		// Convert the variable value to a string
		var strValue string
		switch v := varValue.(type) {
		case string:
			strValue = v
		case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, float32, float64, bool:
			strValue = fmt.Sprintf("%v", v)
		default:
			// For complex types, use %v format
			strValue = fmt.Sprintf("%v", v)
		}

		// Replace the variable reference with its value
		result = strings.Replace(result, match[0], strValue, -1)
	}

	return result
}

// SubstituteTaskVariables replaces variable references in task fields with their values
// from the task context.
func SubstituteTaskVariables(task *Task, variables map[string]any) {
	// Substitute variables in string fields
	task.URL = SubstituteVariables(task.URL, variables)
	task.Value = SubstituteVariables(task.Value, variables)
	task.Selector = SubstituteVariables(task.Selector, variables)
	task.Attribute = SubstituteVariables(task.Attribute, variables)
	task.JavaScript = SubstituteVariables(task.JavaScript, variables)
	task.FileName = SubstituteVariables(task.FileName, variables)
	task.Condition = SubstituteVariables(task.Condition, variables)
	task.WaitTime = SubstituteVariables(task.WaitTime, variables)
	task.Timeout = SubstituteVariables(task.Timeout, variables)
	task.RetryDelay = SubstituteVariables(task.RetryDelay, variables)

	// Recursively substitute variables in subtasks
	for i := range task.Tasks {
		SubstituteTaskVariables(&task.Tasks[i], variables)
	}
}
