package rod

import (
	"log"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestIntegrationChallengeConfig tests the integration of ChallengeConfig with different components
func TestIntegrationChallengeConfig(t *testing.T) {
	// Skip this test in CI environments
	if os.Getenv("CI") != "" {
		t.Skip("Skipping integration test in CI environment")
	}

	// Create a default challenge configuration
	config := NewDefaultChallengeConfig()
	config.HandleChallenges = true
	config.MonitorChallenges = true
	config.ChallengeTimeoutStr = "45s"
	config.ChallengeRetries = 4
	config.ChallengeRetryDelayStr = "1500ms"
	config.AutoSolveCaptchas = true
	config.HumanSimulation = true

	// Parse the string durations
	err := ParseChallengeConfig(config)
	require.NoError(t, err, "Failed to parse challenge config")

	// Test integration with ChallengeHandlerOptions
	t.Run("IntegrationWithChallengeHandler", func(t *testing.T) {
		opts := &ChallengeHandlerOptions{}
		ApplyChallengeConfigToOptions(config, opts)

		// Verify that the options were set correctly
		assert.Equal(t, config.HumanSimulation, opts.HumanSimulation, "HumanSimulation should match")
		assert.Equal(t, config.AutoSolveCaptchas, opts.AutoSolve, "AutoSolve should match")
		assert.Equal(t, config.ChallengeTimeout, opts.ChallengeTimeout, "ChallengeTimeout should match")
		assert.Equal(t, config.ChallengeRetries, opts.MaxRetries, "MaxRetries should match")
		assert.Equal(t, config.ChallengeRetryDelay, opts.DelayBetweenTries, "DelayBetweenTries should match")

		// Create a challenge handler with the options
		logger := createTestLogger()
		handler := NewChallengeHandler(logger, opts)

		// Verify that the handler was created with the correct settings
		assert.Equal(t, config.ChallengeTimeout, handler.GetTimeout(), "Handler timeout should match")
	})

	// Test integration with TaskSettings
	t.Run("IntegrationWithTaskSettings", func(t *testing.T) {
		settings := &TaskSettings{}
		ApplyChallengeConfigToTaskSettings(config, settings)

		// Verify that the settings were set correctly
		assert.Equal(t, config.HandleChallenges, settings.HandleChallenges, "HandleChallenges should match")
		assert.Equal(t, config.MonitorChallenges, settings.MonitorChallenges, "MonitorChallenges should match")
		assert.Equal(t, config.ChallengeRetries, settings.ChallengeRetries, "ChallengeRetries should match")
		assert.Equal(t, config.ChallengeTimeoutStr, settings.ChallengeTimeout, "ChallengeTimeout should match")
		assert.Equal(t, config.ChallengeRetryDelayStr, settings.ChallengeRetryDelay, "ChallengeRetryDelay should match")
		assert.Equal(t, config.AutoSolveCaptchas, settings.AutoSolveCaptchas, "AutoSolveCaptchas should match")

		// Extract the config from the settings
		extractedConfig, err := ExtractChallengeConfigFromTaskSettings(settings)
		require.NoError(t, err, "Failed to extract challenge config")

		// Verify that the extracted config matches the original
		assert.Equal(t, config.HandleChallenges, extractedConfig.HandleChallenges, "HandleChallenges should match")
		assert.Equal(t, config.MonitorChallenges, extractedConfig.MonitorChallenges, "MonitorChallenges should match")
		assert.Equal(t, config.ChallengeRetries, extractedConfig.ChallengeRetries, "ChallengeRetries should match")
		assert.Equal(t, config.ChallengeTimeout, extractedConfig.ChallengeTimeout, "ChallengeTimeout should match")
		assert.Equal(t, config.ChallengeRetryDelay, extractedConfig.ChallengeRetryDelay, "ChallengeRetryDelay should match")
		assert.Equal(t, config.AutoSolveCaptchas, extractedConfig.AutoSolveCaptchas, "AutoSolveCaptchas should match")
	})

	// Test integration with AntiDetectOptions
	t.Run("IntegrationWithAntiDetectOptions", func(t *testing.T) {
		options := &AntiDetectOptions{
			HandleChallenges:      true,
			MonitorChallenges:     true,
			ChallengeTimeout:      30 * time.Second,
			ChallengeRetries:      3,
			ChallengeRetryDelay:   1 * time.Second,
			AutoSolveCaptchas:     true,
			SimulateHumanBehavior: true,
		}

		extractedConfig := ExtractChallengeConfigFromAntiDetectOptions(options)

		// Verify that the extracted config matches the options
		assert.Equal(t, options.HandleChallenges, extractedConfig.HandleChallenges, "HandleChallenges should match")
		assert.Equal(t, options.MonitorChallenges, extractedConfig.MonitorChallenges, "MonitorChallenges should match")
		assert.Equal(t, options.ChallengeTimeout, extractedConfig.ChallengeTimeout, "ChallengeTimeout should match")
		assert.Equal(t, options.ChallengeRetries, extractedConfig.ChallengeRetries, "ChallengeRetries should match")
		assert.Equal(t, options.ChallengeRetryDelay, extractedConfig.ChallengeRetryDelay, "ChallengeRetryDelay should match")
		assert.Equal(t, options.AutoSolveCaptchas, extractedConfig.AutoSolveCaptchas, "AutoSolveCaptchas should match")
		assert.Equal(t, options.SimulateHumanBehavior, extractedConfig.HumanSimulation, "HumanSimulation should match")
	})
}

// TestIntegrationExtensionManagement tests the integration of ExtensionManager with BrowserAutomation
func TestIntegrationExtensionManagement(t *testing.T) {
	// Skip this test in CI environments
	if os.Getenv("CI") != "" {
		t.Skip("Skipping integration test in CI environment")
	}

	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "rod-extension-test-*")
	require.NoError(t, err, "Failed to create temp directory")
	defer os.RemoveAll(tempDir)

	// Create a browser automation instance with the temp directory as user data dir
	ba := NewBrowserAutomation(tempDir)

	// Create a dummy extension
	extensionDir := filepath.Join(tempDir, "dummy-extension")
	err = os.MkdirAll(extensionDir, 0755)
	require.NoError(t, err, "Failed to create extension directory")

	// Create a manifest.json file
	manifestContent := `{
		"name": "Dummy Extension",
		"version": "1.0",
		"manifest_version": 2,
		"description": "A dummy extension for testing"
	}`
	err = os.WriteFile(filepath.Join(extensionDir, "manifest.json"), []byte(manifestContent), 0644)
	require.NoError(t, err, "Failed to create manifest.json")

	// Test installing the extension
	err = ba.InstallExtension(extensionDir)
	assert.NoError(t, err, "Failed to install extension")

	// Since we're not actually launching a browser, we can't fully test the extension management
	// But we can verify that the extension manager was initialized
	assert.NotNil(t, ba.extensionManager, "Extension manager should be initialized")
}

// createTestLogger creates a test logger for integration tests
func createTestLogger() *log.Logger {
	return log.New(os.Stdout, "[Test] ", log.LstdFlags)
}
