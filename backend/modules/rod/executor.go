package rod

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/go-rod/rod"
)

// Executor handles the execution of tasks
type Executor struct {
	browser          *rod.Browser
	page             *rod.Page
	selector         *ElementSelector
	logger           *log.Logger
	settings         TaskSettings
	taskCtx          *TaskContext
	challengeHandler *ChallengeHandler
	resourceManager  *ResourceManager
	managedBrowser   *ManagedBrowser
	managedPage      *ManagedPage
	ctx              context.Context
	cancel           context.CancelFunc
}

// NewExecutor creates a new executor
func NewExecutor(logger *log.Logger) *Executor {
	if logger == nil {
		logger = log.New(os.Stdout, "[Rod] ", log.LstdFlags)
	}

	// Create context for the executor
	ctx, cancel := context.WithCancel(context.Background())

	// Create resource manager
	resourceManager := NewResourceManager(logger, nil)

	return &Executor{
		logger: logger,
		settings: TaskSettings{
			Headless:       true,
			Timeout:        "30s",
			ViewportWidth:  1366,
			ViewportHeight: 768,
			AntiDetection:  true,
		},
		taskCtx: &TaskContext{
			Variables: make(map[string]interface{}),
			Results:   make(map[string]TaskResult),
		},
		resourceManager: resourceManager,
		ctx:             ctx,
		cancel:          cancel,
	}
}

// ExecutePlan executes a task plan
func (e *Executor) ExecutePlan(plan *TaskPlan) error {
	// Apply settings from the plan
	e.applySettings(plan.Settings)

	// Initialize variables from the plan
	for k, v := range plan.Variables {
		e.taskCtx.Variables[k] = v
	}

	// Initialize the browser using resource manager
	if err := e.initBrowserWithResourceManager(); err != nil {
		return NewBrowserInitError(err)
	}

	// Initialize the challenge handler
	e.initChallengeHandler()

	// Execute each task
	errorGroup := NewErrorGroup()
	for i, task := range plan.Tasks {
		// Substitute variables in the task
		SubstituteTaskVariables(&task, e.taskCtx.Variables)

		// Execute the task with timeout
		result, err := e.executeTaskWithTimeout(task)
		if err != nil {
			e.logger.Printf("Task %d (%s) failed: %v", i, task.Type, err)

			// Add error to group
			taskErr := NewTaskExecutionError(task.Type, task.Description, err)
			errorGroup.Add(taskErr)

			// If it's not a retryable error, stop execution
			if !IsRetryableError(err) {
				break
			}
			continue
		}

		// Store the result if a variable name was provided
		if task.Variable != "" {
			e.taskCtx.Results[task.Variable] = result
			e.taskCtx.Variables[task.Variable] = result.Value
		}
	}

	// Cleanup resources if not keeping them open
	if !e.settings.KeepOpen {
		e.cleanup()
	}

	if errorGroup.HasErrors() {
		return errorGroup
	}

	return nil
}

// initBrowserWithResourceManager initializes browser using the resource manager
func (e *Executor) initBrowserWithResourceManager() error {
	// Get browser from resource manager
	browser, err := e.resourceManager.GetBrowser(e.ctx, e.settings)
	if err != nil {
		return fmt.Errorf("failed to get browser from resource manager: %w", err)
	}

	e.managedBrowser = browser
	e.browser = browser.Browser

	// Get page from resource manager
	page, err := e.resourceManager.GetPage(e.ctx, browser.ID)
	if err != nil {
		return fmt.Errorf("failed to get page from resource manager: %w", err)
	}

	e.managedPage = page
	e.page = page.Page

	// Create element selector
	e.selector = NewElementSelector(e.page)

	e.logger.Printf("Initialized browser %s and page %s", browser.ID, page.ID)
	return nil
}

// executeTaskWithTimeout executes a task with timeout control
func (e *Executor) executeTaskWithTimeout(task Task) (TaskResult, error) {
	// Parse timeout
	timeout, err := ParseTimeout(task.Timeout)
	if err != nil {
		timeout, _ = ParseTimeout(e.settings.Timeout)
	}

	// Create context with timeout
	ctx, cancel := context.WithTimeout(e.ctx, timeout)
	defer cancel()

	// Execute task in goroutine
	resultCh := make(chan TaskResult, 1)
	errorCh := make(chan error, 1)

	go func() {
		result, err := e.executeTask(task)
		if err != nil {
			errorCh <- err
			return
		}
		resultCh <- result
	}()

	// Wait for completion or timeout
	select {
	case result := <-resultCh:
		return result, nil
	case err := <-errorCh:
		return TaskResult{}, err
	case <-ctx.Done():
		return TaskResult{}, NewError(ErrCodeTaskTimeout,
			fmt.Sprintf("Task %s timed out after %v", task.Type, timeout), ctx.Err())
	}
}

// cleanup cleans up resources
func (e *Executor) cleanup() {
	if e.managedPage != nil {
		if err := e.resourceManager.ReleasePage(e.managedPage.ID); err != nil {
			e.logger.Printf("Failed to release page: %v", err)
		}
		e.managedPage = nil
		e.page = nil
	}

	if e.managedBrowser != nil {
		if err := e.resourceManager.ReleaseBrowser(e.managedBrowser.ID); err != nil {
			e.logger.Printf("Failed to release browser: %v", err)
		}
		e.managedBrowser = nil
		e.browser = nil
	}
}

// Close closes the executor and cleans up resources
func (e *Executor) Close() {
	// Cancel context
	if e.cancel != nil {
		e.cancel()
	}

	// Cleanup resources
	e.cleanup()

	// Shutdown resource manager
	if e.resourceManager != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		if err := e.resourceManager.Shutdown(ctx); err != nil {
			e.logger.Printf("Failed to shutdown resource manager: %v", err)
		}
	}
}

// applySettings applies settings from the task plan
func (e *Executor) applySettings(settings TaskSettings) {
	// Only override non-empty settings
	if settings.Timeout != "" {
		e.settings.Timeout = settings.Timeout
	}
	if settings.UserAgent != "" {
		e.settings.UserAgent = settings.UserAgent
	}
	if settings.ViewportWidth > 0 {
		e.settings.ViewportWidth = settings.ViewportWidth
	}
	if settings.ViewportHeight > 0 {
		e.settings.ViewportHeight = settings.ViewportHeight
	}
	if settings.Proxy != "" {
		e.settings.Proxy = settings.Proxy
	}
	if settings.DefaultWait != "" {
		e.settings.DefaultWait = settings.DefaultWait
	}
	if len(settings.Headers) > 0 {
		e.settings.Headers = settings.Headers
	}
	if len(settings.Cookies) > 0 {
		e.settings.Cookies = settings.Cookies
	}
	if settings.UserDataDir != "" {
		e.settings.UserDataDir = settings.UserDataDir
	}
	if len(settings.Extensions) > 0 {
		e.settings.Extensions = settings.Extensions
	}
	if settings.ExtensionLoadType != "" {
		e.settings.ExtensionLoadType = settings.ExtensionLoadType
	}
	if settings.DeviceScaleFactor > 0 {
		e.settings.DeviceScaleFactor = settings.DeviceScaleFactor
	}

	// Boolean settings
	e.settings.Headless = settings.Headless
	e.settings.AntiDetection = settings.AntiDetection
	e.settings.KeepOpen = settings.KeepOpen
	e.settings.HandleChallenges = settings.HandleChallenges
	e.settings.MonitorChallenges = settings.MonitorChallenges
	e.settings.AutoSolveCaptchas = settings.AutoSolveCaptchas
}

// initChallengeHandler initializes the challenge handler
func (e *Executor) initChallengeHandler() {
	// Parse challenge timeout
	challengeTimeout, err := ParseTimeout(e.settings.ChallengeTimeout)
	if err != nil {
		challengeTimeout = 60 * time.Second // Default to 60 seconds
	}

	// Parse challenge retry delay
	challengeRetryDelay, err := ParseTimeout(e.settings.ChallengeRetryDelay)
	if err != nil {
		challengeRetryDelay = 2 * time.Second // Default to 2 seconds
	}

	// Create challenge handler options
	opts := &ChallengeHandlerOptions{
		WaitTimeout:       30 * time.Second,
		MaxRetries:        e.settings.ChallengeRetries,
		DelayBetweenTries: challengeRetryDelay,
		HumanSimulation:   true,
		AutoSolve:         e.settings.AutoSolveCaptchas,
		ChallengeTimeout:  challengeTimeout,
	}

	// Create the challenge handler
	e.challengeHandler = NewChallengeHandler(e.logger, opts)

	// Start monitoring for challenges if enabled
	if e.settings.MonitorChallenges {
		e.challengeHandler.MonitorForChallenges(e.page)
	}
}

// executeTask executes a single task
func (e *Executor) executeTask(task Task) (TaskResult, error) {
	// Start timing
	startTime := time.Now()

	// Create a default result
	result := TaskResult{
		Success:     false,
		Value:       nil,
		Screenshot:  nil,
		Error:       nil,
		ElapsedTime: 0,
	}

	// Parse timeout
	timeout, err := ParseTimeout(task.Timeout)
	if err != nil {
		timeout, _ = ParseTimeout(e.settings.Timeout) // Use default timeout
	}

	// Execute the task based on its type
	var taskErr error
	switch task.Type {
	case TaskNavigate:
		taskErr = e.executeNavigate(task)
	case TaskNavigateBack:
		taskErr = e.executeNavigateBack(task)
	case TaskNavigateForwd:
		taskErr = e.executeNavigateForward(task)
	case TaskReload:
		taskErr = e.executeReload(task)
	case TaskClick:
		taskErr = e.executeClick(task, timeout)
	case TaskDoubleClick:
		taskErr = e.executeDoubleClick(task, timeout)
	case TaskTypeText:
		taskErr = e.executeTypeText(task, timeout)
	case TaskSelect:
		taskErr = e.executeSelect(task, timeout)
	case TaskClear:
		taskErr = e.executeClear(task, timeout)
	case TaskSubmit:
		taskErr = e.executeSubmit(task, timeout)
	case TaskFocus:
		taskErr = e.executeFocus(task, timeout)
	case TaskBlur:
		taskErr = e.executeBlur(task, timeout)
	case TaskScrollInto:
		taskErr = e.executeScrollIntoView(task, timeout)
	case TaskWaitVisible:
		taskErr = e.executeWaitVisible(task, timeout)
	case TaskWaitNotVisible:
		taskErr = e.executeWaitNotVisible(task, timeout)
	case TaskWaitPresent:
		taskErr = e.executeWaitPresent(task, timeout)
	case TaskWaitNotPresent:
		taskErr = e.executeWaitNotPresent(task, timeout)
	case TaskWaitEnabled:
		taskErr = e.executeWaitEnabled(task, timeout)
	case TaskSleep:
		taskErr = e.executeSleep(task)
	case TaskGetText:
		result.Value, taskErr = e.executeGetText(task, timeout)
	case TaskGetHTML:
		result.Value, taskErr = e.executeGetHTML(task, timeout)
	case TaskGetOuterHTML:
		result.Value, taskErr = e.executeGetOuterHTML(task, timeout)
	case TaskGetAttribute:
		result.Value, taskErr = e.executeGetAttribute(task, timeout)
	case TaskGetValue:
		result.Value, taskErr = e.executeGetValue(task, timeout)
	case TaskScreenshot:
		result.Screenshot, taskErr = e.executeScreenshot(task, timeout)
	case TaskFullScreenshot:
		result.Screenshot, taskErr = e.executeFullScreenshot(task)
	case TaskEvaluate:
		result.Value, taskErr = e.executeEvaluate(task)
	case TaskPoll:
		result.Value, taskErr = e.executePoll(task, timeout)
	case TaskIf:
		taskErr = e.executeIf(task)
	case TaskLoop:
		taskErr = e.executeLoop(task)
	case TaskForEach:
		taskErr = e.executeForEach(task)
	case TaskDetectChallenge:
		result.Value, taskErr = e.executeDetectChallenge(task)
	case TaskHandleChallenge:
		taskErr = e.executeHandleChallenge(task)
	case TaskSolveReCaptcha:
		taskErr = e.executeSolveReCaptcha(task, timeout)
	case TaskSolveCloudflare:
		taskErr = e.executeSolveCloudflare(task, timeout)
	case TaskMonitorChallenges:
		taskErr = e.executeMonitorChallenges(task)
	case TaskInstallExtension:
		taskErr = e.executeInstallExtension(task)
	default:
		taskErr = fmt.Errorf("unknown task type: %s", task.Type)
	}

	// Update the result
	result.Success = taskErr == nil
	result.Error = taskErr
	result.ElapsedTime = time.Since(startTime)

	return result, taskErr
}
