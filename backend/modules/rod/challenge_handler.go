package rod

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/go-rod/rod"
)

// ChallengeType represents the type of challenge detected
type ChallengeType string

const (
	// Challenge types
	ChallengeCloudflare ChallengeType = "cloudflare"
	ChallengeReCaptcha  ChallengeType = "recaptcha"
	ChallengeCaptcha    ChallengeType = "captcha"
	ChallengeHCaptcha   ChallengeType = "hcaptcha"
	ChallengeUnknown    ChallengeType = "unknown"
)

// ChallengeHandler manages detection and handling of various challenges
type ChallengeHandler struct {
	logger                *log.Logger
	waitTimeout           time.Duration
	maxRetries            int
	delayBetweenTries     time.Duration
	humanSimulation       bool
	autoSolve             bool
	challengeTimeout      time.Duration
	captchaSolvingService string
	captchaAPIKey         string
	captchaSolver         *CaptchaSolver
}

// ChallengeHandlerOptions contains options for the challenge handler
type ChallengeHandlerOptions struct {
	WaitTimeout           time.Duration
	MaxRetries            int
	DelayBetweenTries     time.Duration
	HumanSimulation       bool
	AutoSolve             bool
	ChallengeTimeout      time.Duration
	CaptchaSolvingService string
	CaptchaAPIK<PERSON>         string
}

// NewChallengeHandler creates a new challenge handler
func NewChallengeHandler(logger *log.Logger, opts *ChallengeHandlerOptions) *ChallengeHandler {
	if logger == nil {
		logger = log.New(log.Writer(), "[ChallengeHandler] ", log.LstdFlags)
	}

	if opts == nil {
		opts = &ChallengeHandlerOptions{
			WaitTimeout:       30 * time.Second,
			MaxRetries:        3,
			DelayBetweenTries: 2 * time.Second,
			HumanSimulation:   true,
			AutoSolve:         true,
			ChallengeTimeout:  60 * time.Second,
		}
	}

	ch := &ChallengeHandler{
		logger:                logger,
		waitTimeout:           opts.WaitTimeout,
		maxRetries:            opts.MaxRetries,
		delayBetweenTries:     opts.DelayBetweenTries,
		humanSimulation:       opts.HumanSimulation,
		autoSolve:             opts.AutoSolve,
		challengeTimeout:      opts.ChallengeTimeout,
		captchaSolvingService: opts.CaptchaSolvingService,
		captchaAPIKey:         opts.CaptchaAPIKey,
	}

	// Initialize captcha solver if service and API key are provided
	if opts.CaptchaSolvingService != "" && opts.CaptchaAPIKey != "" {
		ch.captchaSolver = NewCaptchaSolver(opts.CaptchaSolvingService, opts.CaptchaAPIKey, logger)
	}

	return ch
}

// DetectChallenge detects if there's a challenge on the current page
func (ch *ChallengeHandler) DetectChallenge(page *rod.Page) (ChallengeType, error) {
	// Check for Cloudflare challenge
	hasCloudflare, err := page.Eval(`() => {
		return document.querySelector('#cf-error-details') !== null ||
			document.querySelector('.cf-browser-verification') !== null ||
			document.querySelector('.cf-im-under-attack') !== null ||
			document.querySelector('iframe[src*="challenges.cloudflare.com"]') !== null
	}`)
	if err != nil {
		return ChallengeUnknown, fmt.Errorf("failed to check for Cloudflare challenge: %w", err)
	}

	if hasCloudflare.Value.Bool() {
		ch.logger.Println("Detected Cloudflare challenge")
		return ChallengeCloudflare, nil
	}

	// Check for reCAPTCHA
	hasReCaptcha, err := page.Eval(`() => {
		return document.querySelector('.g-recaptcha') !== null ||
			document.querySelector('iframe[src*="google.com/recaptcha"]') !== null ||
			document.querySelector('iframe[src*="recaptcha.net"]') !== null
	}`)
	if err != nil {
		return ChallengeUnknown, fmt.Errorf("failed to check for reCAPTCHA challenge: %w", err)
	}

	if hasReCaptcha.Value.Bool() {
		ch.logger.Println("Detected reCAPTCHA challenge")
		return ChallengeReCaptcha, nil
	}

	// Check for hCaptcha
	hasHCaptcha, err := page.Eval(`() => {
		return document.querySelector('.h-captcha') !== null ||
			document.querySelector('iframe[src*="hcaptcha.com"]') !== null
	}`)
	if err != nil {
		return ChallengeUnknown, fmt.Errorf("failed to check for hCaptcha challenge: %w", err)
	}

	if hasHCaptcha.Value.Bool() {
		ch.logger.Println("Detected hCaptcha challenge")
		return ChallengeHCaptcha, nil
	}

	// Check for generic captcha
	hasCaptcha, err := page.Eval(`() => {
		return document.querySelector('input[name="captcha"]') !== null ||
			document.querySelector('img[alt*="captcha" i]') !== null ||
			document.querySelector('div[class*="captcha" i]') !== null ||
			document.querySelector('div[id*="captcha" i]') !== null
	}`)
	if err != nil {
		return ChallengeUnknown, fmt.Errorf("failed to check for generic captcha challenge: %w", err)
	}

	if hasCaptcha.Value.Bool() {
		ch.logger.Println("Detected generic captcha challenge")
		return ChallengeCaptcha, nil
	}

	return ChallengeUnknown, nil
}

// HandleChallenge attempts to handle the detected challenge
func (ch *ChallengeHandler) HandleChallenge(page *rod.Page, challengeType ChallengeType) error {
	switch challengeType {
	case ChallengeCloudflare:
		return ch.handleCloudflare(page)
	case ChallengeReCaptcha:
		return ch.handleReCaptcha(page)
	case ChallengeHCaptcha:
		return ch.handleHCaptcha(page)
	case ChallengeCaptcha:
		return ch.handleGenericCaptcha(page)
	default:
		return fmt.Errorf("unknown challenge type: %s", challengeType)
	}
}

// handleCloudflare attempts to bypass Cloudflare protection
func (ch *ChallengeHandler) handleCloudflare(page *rod.Page) error {
	ch.logger.Println("Attempting to handle Cloudflare challenge")

	// Check if it's a "waiting" screen
	isWaiting, err := page.Eval(`() => {
		return document.body.textContent.includes("Checking your browser") ||
			document.body.textContent.includes("Please wait") ||
			document.body.textContent.includes("Just a moment")
	}`)
	if err != nil {
		return fmt.Errorf("failed to check Cloudflare waiting status: %w", err)
	}

	if isWaiting.Value.Bool() {
		ch.logger.Println("Detected Cloudflare waiting screen, waiting for it to resolve...")

		// Create a timeout context
		ctx, cancel := context.WithTimeout(page.GetContext(), ch.challengeTimeout)
		defer cancel()

		// Wait for the Cloudflare elements to disappear
		err = page.Context(ctx).WaitStable(2 * time.Second)
		if err != nil {
			return fmt.Errorf("timed out waiting for Cloudflare challenge to resolve: %w", err)
		}

		// Check if the challenge is still present
		hasChallenge, err := page.Eval(`() => {
			return document.querySelector('#cf-error-details') !== null ||
				document.querySelector('.cf-browser-verification') !== null ||
				document.querySelector('.cf-im-under-attack') !== null ||
				document.querySelector('iframe[src*="challenges.cloudflare.com"]') !== null
		}`)
		if err != nil {
			return fmt.Errorf("failed to check if Cloudflare challenge is resolved: %w", err)
		}

		if !hasChallenge.Value.Bool() {
			ch.logger.Println("Cloudflare challenge appears to be resolved")
			return nil
		}
	}

	// Check if it's a challenge that requires interaction
	requiresInteraction, err := page.Eval(`() => {
		return document.querySelector('iframe[src*="challenges.cloudflare.com"]') !== null ||
			document.querySelector('#challenge-form') !== null
	}`)
	if err != nil {
		return fmt.Errorf("failed to check Cloudflare interaction status: %w", err)
	}

	if requiresInteraction.Value.Bool() {
		ch.logger.Println("Detected Cloudflare interactive challenge")

		// Try to find and click the checkbox or button
		clicked, err := page.Eval(`() => {
			function clickChallengeElement() {
				// Try to find the checkbox
				const checkbox = document.querySelector('.cf-checkbox-container input[type="checkbox"]');
				if (checkbox) {
					checkbox.click();
					return true;
				}

				// Try to find the "I am human" button
				const buttons = Array.from(document.querySelectorAll('button, input[type="button"], input[type="submit"]'));
				for (const button of buttons) {
					if (button.textContent.includes('human') ||
						button.value?.includes('human') ||
						button.textContent.includes('verify') ||
						button.value?.includes('verify')) {
						button.click();
						return true;
					}
				}

				return false;
			}
			return clickChallengeElement();
		}`)
		if err != nil {
			return fmt.Errorf("failed to interact with Cloudflare challenge: %w", err)
		}

		if clicked.Value.Bool() {
			ch.logger.Println("Clicked on Cloudflare challenge element")

			// Create a timeout context
			ctx, cancel := context.WithTimeout(page.GetContext(), ch.challengeTimeout)
			defer cancel()

			// Wait for the page to stabilize
			err = page.Context(ctx).WaitStable(2 * time.Second)
			if err != nil {
				return fmt.Errorf("timed out waiting for Cloudflare challenge to resolve after interaction: %w", err)
			}

			// Check if the challenge is still present
			hasChallenge, err := page.Eval(`() => {
				return document.querySelector('#cf-error-details') !== null ||
					document.querySelector('.cf-browser-verification') !== null ||
					document.querySelector('.cf-im-under-attack') !== null ||
					document.querySelector('iframe[src*="challenges.cloudflare.com"]') !== null ||
					document.querySelector('#challenge-form') !== null
			}`)
			if err != nil {
				return fmt.Errorf("failed to check if Cloudflare challenge is resolved: %w", err)
			}

			if !hasChallenge.Value.Bool() {
				ch.logger.Println("Cloudflare challenge appears to be resolved after interaction")
				return nil
			}
		}
	}

	return fmt.Errorf("unable to handle Cloudflare challenge automatically")
}

// handleReCaptcha attempts to solve reCAPTCHA challenges
func (ch *ChallengeHandler) handleReCaptcha(page *rod.Page) error {
	ch.logger.Println("Attempting to handle reCAPTCHA challenge")

	// Check if auto-solve is enabled
	if !ch.autoSolve {
		ch.logger.Println("Auto-solve is disabled, waiting for manual intervention")
		// Wait for the challenge to be solved manually
		return ch.waitForManualCaptchaSolve(page, ChallengeReCaptcha)
	}

	// Try to use external captcha solving service if configured
	if ch.captchaSolvingService != "" && ch.captchaAPIKey != "" {
		return ch.solveWithExternalService(page, ChallengeReCaptcha)
	}

	// Attempt to solve using built-in methods
	return ch.solveReCaptchaBuiltIn(page)
}

// solveReCaptchaBuiltIn attempts to solve reCAPTCHA using built-in methods
func (ch *ChallengeHandler) solveReCaptchaBuiltIn(page *rod.Page) error {
	ch.logger.Println("Attempting to solve reCAPTCHA using built-in methods")

	// Try to find and click the reCAPTCHA checkbox
	clicked, err := page.Eval(`() => {
		// Find the reCAPTCHA checkbox
		const checkbox = document.querySelector('.recaptcha-checkbox-border');
		if (checkbox) {
			checkbox.click();
			return true;
		}

		// Try to find the reCAPTCHA iframe and switch to it
		const recaptchaIframe = document.querySelector('iframe[src*="google.com/recaptcha"]') ||
			document.querySelector('iframe[src*="recaptcha.net"]');
		if (recaptchaIframe) {
			// We found an iframe but can't directly interact with its contents
			// due to same-origin policy restrictions
			return false;
		}

		return false;
	}`)
	if err != nil {
		return fmt.Errorf("failed to interact with reCAPTCHA: %w", err)
	}

	if clicked.Value.Bool() {
		ch.logger.Println("Clicked on reCAPTCHA checkbox")

		// Wait for the challenge to complete or for image challenge to appear
		time.Sleep(2 * time.Second)

		// Check if the captcha was solved by clicking the checkbox
		solved, err := page.Eval(`() => {
			// Check if the checkbox is checked
			const checkbox = document.querySelector('.recaptcha-checkbox-checked');
			if (checkbox) {
				return true;
			}

			// Check if we need to solve an image challenge
			const imageChallenge = document.querySelector('.rc-imageselect-instructions');
			if (imageChallenge) {
				return false;
			}

			return false;
		}`)
		if err != nil {
			return fmt.Errorf("failed to check if captcha was solved: %w", err)
		}

		if solved.Value.Bool() {
			ch.logger.Println("reCAPTCHA solved by clicking the checkbox")
			return nil
		}

		// Check if we need to solve an image challenge
		hasImageChallenge, err := page.Eval(`() => {
			return document.querySelector('.rc-imageselect-instructions') !== null;
		}`)
		if err != nil {
			return fmt.Errorf("failed to check for image challenge: %w", err)
		}

		if hasImageChallenge.Value.Bool() {
			ch.logger.Println("Image challenge detected, this requires external solving service")
			if ch.captchaSolvingService != "" && ch.captchaAPIKey != "" {
				return ch.solveWithExternalService(page, ChallengeReCaptcha)
			}
			return ch.waitForManualCaptchaSolve(page, ChallengeReCaptcha)
		}
	}

	// If we couldn't solve it automatically, wait for manual intervention
	ch.logger.Println("Could not automatically solve reCAPTCHA, waiting for manual intervention")
	return ch.waitForManualCaptchaSolve(page, ChallengeReCaptcha)
}

// handleHCaptcha attempts to solve hCaptcha challenges
func (ch *ChallengeHandler) handleHCaptcha(page *rod.Page) error {
	ch.logger.Println("Attempting to handle hCaptcha challenge")

	// Check if auto-solve is enabled
	if !ch.autoSolve {
		ch.logger.Println("Auto-solve is disabled, waiting for manual intervention")
		// Wait for the challenge to be solved manually
		return ch.waitForManualCaptchaSolve(page, ChallengeHCaptcha)
	}

	// Try to use external captcha solving service if configured
	if ch.captchaSolvingService != "" && ch.captchaAPIKey != "" {
		return ch.solveWithExternalService(page, ChallengeHCaptcha)
	}

	// Attempt to solve using built-in methods
	return ch.solveHCaptchaBuiltIn(page)
}

// solveHCaptchaBuiltIn attempts to solve hCaptcha using built-in methods
func (ch *ChallengeHandler) solveHCaptchaBuiltIn(page *rod.Page) error {
	ch.logger.Println("Attempting to solve hCaptcha using built-in methods")

	// Try to find and click the hCaptcha checkbox
	clicked, err := page.Eval(`() => {
		// Find the hCaptcha checkbox
		const checkbox = document.querySelector('.h-captcha');
		if (checkbox) {
			const checkboxInner = checkbox.querySelector('input[type="checkbox"]') ||
				checkbox.querySelector('.checkbox');
			if (checkboxInner) {
				checkboxInner.click();
				return true;
			}
		}

		return false;
	}`)
	if err != nil {
		return fmt.Errorf("failed to interact with hCaptcha: %w", err)
	}

	if clicked.Value.Bool() {
		ch.logger.Println("Clicked on hCaptcha checkbox")

		// Wait for the challenge to complete or for image challenge to appear
		time.Sleep(2 * time.Second)

		// Check if we need to solve an image challenge
		hasImageChallenge, err := page.Eval(`() => {
			return document.querySelector('.challenge-container') !== null;
		}`)
		if err != nil {
			return fmt.Errorf("failed to check for image challenge: %w", err)
		}

		if hasImageChallenge.Value.Bool() {
			ch.logger.Println("Image challenge detected, this requires external solving service")
			if ch.captchaSolvingService != "" && ch.captchaAPIKey != "" {
				return ch.solveWithExternalService(page, ChallengeHCaptcha)
			}
			return ch.waitForManualCaptchaSolve(page, ChallengeHCaptcha)
		}
	}

	// If we couldn't solve it automatically, wait for manual intervention
	ch.logger.Println("Could not automatically solve hCaptcha, waiting for manual intervention")
	return ch.waitForManualCaptchaSolve(page, ChallengeHCaptcha)
}

// handleGenericCaptcha attempts to solve generic captcha challenges
func (ch *ChallengeHandler) handleGenericCaptcha(page *rod.Page) error {
	ch.logger.Println("Attempting to handle generic captcha challenge")

	// Check if auto-solve is enabled
	if !ch.autoSolve {
		ch.logger.Println("Auto-solve is disabled, waiting for manual intervention")
		// Wait for the challenge to be solved manually
		return ch.waitForManualCaptchaSolve(page, ChallengeCaptcha)
	}

	// Try to use external captcha solving service if configured
	if ch.captchaSolvingService != "" && ch.captchaAPIKey != "" {
		return ch.solveWithExternalService(page, ChallengeCaptcha)
	}

	// For generic captchas, we need manual intervention
	ch.logger.Println("Generic captcha requires manual intervention")
	return ch.waitForManualCaptchaSolve(page, ChallengeCaptcha)
}

// waitForManualCaptchaSolve waits for the user to manually solve a captcha
func (ch *ChallengeHandler) waitForManualCaptchaSolve(page *rod.Page, challengeType ChallengeType) error {
	ch.logger.Printf("Waiting for manual intervention to solve %s challenge", challengeType)

	// Create a timeout context
	ctx, cancel := context.WithTimeout(page.GetContext(), ch.challengeTimeout)
	defer cancel()

	// Poll until the challenge is no longer detected
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for manual captcha solve")
		default:
			// Check if the challenge is still present
			currentChallenge, err := ch.DetectChallenge(page)
			if err != nil {
				ch.logger.Printf("Error checking challenge status: %v", err)
				time.Sleep(ch.delayBetweenTries)
				continue
			}

			// If the challenge is no longer detected, we're done
			if currentChallenge == ChallengeUnknown {
				ch.logger.Println("Challenge appears to be solved")
				return nil
			}

			// If a different challenge is detected, handle it
			if currentChallenge != challengeType {
				ch.logger.Printf("Challenge type changed from %s to %s", challengeType, currentChallenge)
				return ch.HandleChallenge(page, currentChallenge)
			}

			// Wait before checking again
			time.Sleep(2 * time.Second)
		}
	}
}

// GetTimeout returns the challenge timeout duration
func (ch *ChallengeHandler) GetTimeout() time.Duration {
	return ch.challengeTimeout
}

// solveWithExternalService attempts to solve a captcha using an external service
func (ch *ChallengeHandler) solveWithExternalService(page *rod.Page, challengeType ChallengeType) error {
	ch.logger.Printf("Attempting to solve %s challenge using external service: %s", challengeType, ch.captchaSolvingService)

	// Check if captcha solver is initialized
	if ch.captchaSolver == nil {
		ch.captchaSolver = NewCaptchaSolver(ch.captchaSolvingService, ch.captchaAPIKey, ch.logger)
	}

	// Solve the captcha based on its type
	var solution string
	var err error

	switch challengeType {
	case ChallengeReCaptcha:
		solution, err = ch.captchaSolver.SolveReCaptcha(page)
	case ChallengeHCaptcha:
		solution, err = ch.captchaSolver.SolveHCaptcha(page)
	default:
		return fmt.Errorf("unsupported captcha type for external solving: %s", challengeType)
	}

	if err != nil {
		return fmt.Errorf("failed to solve captcha: %w", err)
	}

	ch.logger.Printf("Captcha solved successfully: %s", solution[:10]+"...")
	return nil
}

// MonitorForChallenges continuously monitors for challenges and attempts to solve them
func (ch *ChallengeHandler) MonitorForChallenges(page *rod.Page) {
	go func() {
		for {
			// Check if the page context is still valid
			select {
			case <-page.GetContext().Done():
				return
			default:
				// Continue monitoring
			}

			// Detect challenges
			challengeType, err := ch.DetectChallenge(page)
			if err != nil {
				ch.logger.Printf("Error detecting challenges: %v", err)
				time.Sleep(ch.delayBetweenTries)
				continue
			}

			// Handle detected challenges
			if challengeType != ChallengeUnknown {
				ch.logger.Printf("Detected challenge: %s", challengeType)
				err := ch.HandleChallenge(page, challengeType)
				if err != nil {
					ch.logger.Printf("Error handling challenge: %v", err)
				}
			}

			// Wait before checking again
			time.Sleep(ch.delayBetweenTries)
		}
	}()
}
