package rod

import (
	"log"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestNewCaptchaSolver tests the creation of a new captcha solver
func TestNewCaptchaSolver(t *testing.T) {
	logger := log.New(os.Stdout, "[Test] ", log.LstdFlags)

	// Test with valid parameters
	solver := NewCaptchaSolver("2captcha", "test-api-key", logger)
	assert.NotNil(t, solver, "Captcha solver should not be nil")
	assert.Equal(t, "2captcha", solver.service, "Service should be set correctly")
	assert.Equal(t, "test-api-key", solver.api<PERSON>ey, "API key should be set correctly")
	assert.Equal(t, logger, solver.logger, "Logger should be set correctly")
}

// TestExtractReCaptchaSiteKey tests the extraction of reCAPTCHA site key
func TestExtractReCaptchaSiteKey(t *testing.T) {
	t.Skip("Skipping test that requires a browser instance")

	// This test would require a browser instance and a page with a reCAPTCHA
	// In a real test, we would:
	// 1. Create a browser instance
	// 2. Navigate to a page with a reCAPTCHA
	// 3. Extract the site key
	// 4. Verify that it matches the expected value
}

// TestExtractHCaptchaSiteKey tests the extraction of hCaptcha site key
func TestExtractHCaptchaSiteKey(t *testing.T) {
	t.Skip("Skipping test that requires a browser instance")

	// This test would require a browser instance and a page with an hCaptcha
	// In a real test, we would:
	// 1. Create a browser instance
	// 2. Navigate to a page with an hCaptcha
	// 3. Extract the site key
	// 4. Verify that it matches the expected value
}

// TestSubmit2CaptchaReCaptchaTask tests the submission of a reCAPTCHA task to 2Captcha
func TestSubmit2CaptchaReCaptchaTask(t *testing.T) {
	t.Skip("Skipping test that makes external API calls")

	// This test would make an actual API call to 2Captcha
	// In a real test, we would:
	// 1. Create a captcha solver with a valid API key
	// 2. Submit a reCAPTCHA task with a known site key and page URL
	// 3. Verify that the task ID is returned
}

// TestWait2CaptchaSolution tests waiting for a 2Captcha solution
func TestWait2CaptchaSolution(t *testing.T) {
	t.Skip("Skipping test that makes external API calls")

	// This test would make an actual API call to 2Captcha
	// In a real test, we would:
	// 1. Create a captcha solver with a valid API key
	// 2. Submit a reCAPTCHA task and get a task ID
	// 3. Wait for the solution
	// 4. Verify that the solution is returned
}

// TestApplyReCaptchaSolution tests applying a reCAPTCHA solution to a page
func TestApplyReCaptchaSolution(t *testing.T) {
	t.Skip("Skipping test that requires a browser instance")

	// This test would require a browser instance and a page with a reCAPTCHA
	// In a real test, we would:
	// 1. Create a browser instance
	// 2. Navigate to a page with a reCAPTCHA
	// 3. Apply a known solution
	// 4. Verify that the solution was applied correctly
}

// MockLogger is a mock implementation of the Logger interface for testing
type MockLogger struct {
	PrintfCalled  bool
	PrintlnCalled bool
	LastFormat    string
	LastArgs      []interface{}
}

func (m *MockLogger) Printf(format string, v ...interface{}) {
	m.PrintfCalled = true
	m.LastFormat = format
	m.LastArgs = v
}

func (m *MockLogger) Println(v ...interface{}) {
	m.PrintlnCalled = true
	m.LastArgs = v
}

// TestCaptchaSolverWithMockLogger tests the captcha solver with a mock logger
func TestCaptchaSolverWithMockLogger(t *testing.T) {
	mockLogger := &MockLogger{}

	solver := NewCaptchaSolver("2captcha", "test-api-key", mockLogger)
	assert.NotNil(t, solver, "Captcha solver should not be nil")

	// Test that the logger is used
	solver.logger.Printf("Test format %s", "arg")
	assert.True(t, mockLogger.PrintfCalled, "Printf should be called")
	assert.Equal(t, "Test format %s", mockLogger.LastFormat, "Format should be correct")
	assert.Equal(t, []interface{}{"arg"}, mockLogger.LastArgs, "Args should be correct")

	solver.logger.Println("Test message")
	assert.True(t, mockLogger.PrintlnCalled, "Println should be called")
	assert.Equal(t, []interface{}{"Test message"}, mockLogger.LastArgs, "Args should be correct")
}
