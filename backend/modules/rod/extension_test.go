package rod

import (
	"log"
	"os"
	"path/filepath"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestExtractExtensionID tests the extractExtensionID function
func TestExtractExtensionID(t *testing.T) {
	testCases := []struct {
		name     string
		url      string
		expected string
	}{
		{
			name:     "URL with name and ID",
			url:      "https://chrome.google.com/webstore/detail/ublock-origin/cjpalhdlnbpafiamejdnhcphjbkeiagm",
			expected: "cjpalhdlnbpafiamejdnhcphjbkeiagm",
		},
		{
			name:     "URL with ID only",
			url:      "https://chrome.google.com/webstore/detail/cjpalhdlnbpafiamejdnhcphjbkeiagm",
			expected: "cjpalhdlnbpafiamejdnhcphjbkeiagm",
		},
		{
			name:     "Invalid URL",
			url:      "https://chrome.google.com/webstore/category/extensions",
			expected: "",
		},
		{
			name:     "Empty URL",
			url:      "",
			expected: "",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := extractExtensionID(tc.url)
			assert.Equal(t, tc.expected, result)
		})
	}
}

// TestInstallExtensionFromLocalFile tests installing an extension from a local file
func TestInstallExtensionFromLocalFile(t *testing.T) {
	// Create a temporary directory for the test
	tempDir, err := os.MkdirTemp("", "rod-extension-test-*")
	require.NoError(t, err, "Failed to create temp directory")
	defer os.RemoveAll(tempDir)

	// Create a dummy extension file
	dummyExtPath := filepath.Join(tempDir, "dummy-extension.zip")
	err = os.WriteFile(dummyExtPath, []byte("dummy extension content"), 0644)
	require.NoError(t, err, "Failed to create dummy extension file")

	// Create a new executor
	logger := newTestLogger()
	executor := NewExecutor(logger)

	// Create a task to install the extension
	task := Task{
		Type:  TaskInstallExtension,
		Value: dummyExtPath,
	}

	// Execute the task
	err = executor.executeInstallExtension(task)
	assert.NoError(t, err, "Extension installation should succeed")

	// Verify that the extension was added to the settings
	assert.Contains(t, executor.settings.Extensions, dummyExtPath, "Extension should be added to settings")
}

// TestInstallExtensionFromNonExistentFile tests installing an extension from a non-existent file
func TestInstallExtensionFromNonExistentFile(t *testing.T) {
	// Create a new executor
	logger := newTestLogger()
	executor := NewExecutor(logger)

	// Create a task to install a non-existent extension
	task := Task{
		Type:  TaskInstallExtension,
		Value: "/path/to/nonexistent/extension.zip",
	}

	// Execute the task
	err := executor.executeInstallExtension(task)
	assert.Error(t, err, "Extension installation should fail for non-existent file")
	assert.Contains(t, err.Error(), "does not exist", "Error should mention that the file does not exist")
}

// Helper function to create a test logger
func newTestLogger() *log.Logger {
	return log.New(os.Stdout, "[Test] ", log.LstdFlags)
}
