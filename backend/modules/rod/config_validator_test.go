package rod

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

// TestValidateChallengeConfig tests the ValidateChallengeConfig function
func TestValidateChallengeConfig(t *testing.T) {
	// Test valid configuration
	t.Run("ValidConfig", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		result := ValidateChallengeConfig(config)
		assert.True(t, result.Valid, "Valid config should be valid")
		assert.Empty(t, result.Errors, "Valid config should have no errors")
	})

	// Test invalid timeout
	t.Run("InvalidTimeout", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		config.ChallengeTimeoutStr = "invalid"
		result := ValidateChallengeConfig(config)
		assert.False(t, result.Valid, "Config with invalid timeout should be invalid")
		assert.NotEmpty(t, result.Errors, "Config with invalid timeout should have errors")
		assert.Contains(t, result.Errors[0], "Invalid challenge timeout", "Error should mention invalid timeout")
	})

	// Test invalid retry delay
	t.Run("InvalidRetryDelay", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		config.ChallengeRetryDelayStr = "invalid"
		result := ValidateChallengeConfig(config)
		assert.False(t, result.Valid, "Config with invalid retry delay should be invalid")
		assert.NotEmpty(t, result.Errors, "Config with invalid retry delay should have errors")
		assert.Contains(t, result.Errors[0], "Invalid challenge retry delay", "Error should mention invalid retry delay")
	})

	// Test warning for short timeout
	t.Run("ShortTimeout", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		config.ChallengeTimeoutStr = "500ms"
		result := ValidateChallengeConfig(config)
		assert.True(t, result.Valid, "Config with short timeout should be valid")
		assert.NotEmpty(t, result.Warnings, "Config with short timeout should have warnings")
		assert.Contains(t, result.Warnings[0], "Challenge timeout is very short", "Warning should mention short timeout")
	})

	// Test warning for long timeout
	t.Run("LongTimeout", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		config.ChallengeTimeoutStr = "10m"
		result := ValidateChallengeConfig(config)
		assert.True(t, result.Valid, "Config with long timeout should be valid")
		assert.NotEmpty(t, result.Warnings, "Config with long timeout should have warnings")
		assert.Contains(t, result.Warnings[0], "Challenge timeout is very long", "Warning should mention long timeout")
	})

	// Test warning for unknown captcha solving service
	t.Run("UnknownCaptchaSolvingService", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		config.AutoSolveCaptchas = true
		config.CaptchaSolvingService = "unknown"
		result := ValidateChallengeConfig(config)
		assert.True(t, result.Valid, "Config with unknown captcha solving service should be valid")
		assert.NotEmpty(t, result.Warnings, "Config with unknown captcha solving service should have warnings")
		assert.Contains(t, result.Warnings[0], "Unknown captcha solving service", "Warning should mention unknown service")
	})

	// Test warning for missing API key
	t.Run("MissingAPIKey", func(t *testing.T) {
		config := NewDefaultChallengeConfig()
		config.AutoSolveCaptchas = true
		config.CaptchaSolvingService = "2captcha"
		config.CaptchaAPIKey = ""
		result := ValidateChallengeConfig(config)
		assert.True(t, result.Valid, "Config with missing API key should be valid")
		assert.NotEmpty(t, result.Warnings, "Config with missing API key should have warnings")
		assert.Contains(t, result.Warnings[0], "API key is empty", "Warning should mention empty API key")
	})
}

// TestValidateTaskSettings tests the ValidateTaskSettings function
func TestValidateTaskSettings(t *testing.T) {
	// Test valid settings
	t.Run("ValidSettings", func(t *testing.T) {
		settings := &TaskSettings{
			Timeout:        "30s",
			DefaultWait:    "5s",
			ViewportWidth:  1280,
			ViewportHeight: 720,
		}
		result := ValidateTaskSettings(settings)
		assert.True(t, result.Valid, "Valid settings should be valid")
		assert.Empty(t, result.Errors, "Valid settings should have no errors")
	})

	// Test invalid timeout
	t.Run("InvalidTimeout", func(t *testing.T) {
		settings := &TaskSettings{
			Timeout: "invalid",
		}
		result := ValidateTaskSettings(settings)
		assert.False(t, result.Valid, "Settings with invalid timeout should be invalid")
		assert.NotEmpty(t, result.Errors, "Settings with invalid timeout should have errors")
		assert.Contains(t, result.Errors[0], "Invalid timeout", "Error should mention invalid timeout")
	})

	// Test invalid default wait
	t.Run("InvalidDefaultWait", func(t *testing.T) {
		settings := &TaskSettings{
			DefaultWait: "invalid",
		}
		result := ValidateTaskSettings(settings)
		assert.False(t, result.Valid, "Settings with invalid default wait should be invalid")
		assert.NotEmpty(t, result.Errors, "Settings with invalid default wait should have errors")
		assert.Contains(t, result.Errors[0], "Invalid default wait", "Error should mention invalid default wait")
	})

	// Test warning for small viewport
	t.Run("SmallViewport", func(t *testing.T) {
		settings := &TaskSettings{
			ViewportWidth:  200,
			ViewportHeight: 150,
		}
		result := ValidateTaskSettings(settings)
		assert.True(t, result.Valid, "Settings with small viewport should be valid")
		assert.NotEmpty(t, result.Warnings, "Settings with small viewport should have warnings")
		assert.Contains(t, result.Warnings[0], "Viewport width is very small", "Warning should mention small width")
		assert.Contains(t, result.Warnings[1], "Viewport height is very small", "Warning should mention small height")
	})

	// Test validation of challenge settings
	t.Run("ChallengeSettings", func(t *testing.T) {
		settings := &TaskSettings{
			HandleChallenges: true,
			ChallengeTimeout: "invalid",
		}
		result := ValidateTaskSettings(settings)
		assert.False(t, result.Valid, "Settings with invalid challenge timeout should be invalid")
		assert.NotEmpty(t, result.Errors, "Settings with invalid challenge timeout should have errors")
		assert.Contains(t, result.Errors[0], "Failed to extract challenge config", "Error should mention extraction failure")
	})
}

// TestConfigValidationResultString tests the String method of ConfigValidationResult
func TestConfigValidationResultString(t *testing.T) {
	// Test valid result
	t.Run("ValidResult", func(t *testing.T) {
		result := &ConfigValidationResult{
			Valid: true,
		}
		str := result.String()
		assert.Contains(t, str, "Configuration is valid", "String should indicate valid configuration")
	})

	// Test invalid result with errors
	t.Run("InvalidResultWithErrors", func(t *testing.T) {
		result := &ConfigValidationResult{
			Valid:  false,
			Errors: []string{"Error 1", "Error 2"},
		}
		str := result.String()
		assert.Contains(t, str, "Configuration is invalid", "String should indicate invalid configuration")
		assert.Contains(t, str, "Errors:", "String should list errors")
		assert.Contains(t, str, "1. Error 1", "String should include first error")
		assert.Contains(t, str, "2. Error 2", "String should include second error")
	})

	// Test result with warnings
	t.Run("ResultWithWarnings", func(t *testing.T) {
		result := &ConfigValidationResult{
			Valid:    true,
			Warnings: []string{"Warning 1", "Warning 2"},
		}
		str := result.String()
		assert.Contains(t, str, "Configuration is valid", "String should indicate valid configuration")
		assert.Contains(t, str, "Warnings:", "String should list warnings")
		assert.Contains(t, str, "1. Warning 1", "String should include first warning")
		assert.Contains(t, str, "2. Warning 2", "String should include second warning")
	})
}
