package rod

import (
	"log"
	"os"
)

// BrowserAutomation is the main API for browser automation
type BrowserAutomation struct {
	parser           *Parser
	executor         *Executor
	logger           *log.Logger
	extensionManager *ExtensionManager
	resourceManager  *ResourceManager
}

// NewBrowserAutomation creates a new browser automation instance
// If userDataDir is provided, it will be used as the Chrome user data directory
func NewBrowserAutomation(userDataDir ...string) *BrowserAutomation {
	logger := log.New(os.Stdout, "[Rod] ", log.LstdFlags)
	executor := NewExecutor(logger)

	// Set user data directory if provided
	var userDir string
	if len(userDataDir) > 0 && userDataDir[0] != "" {
		userDir = userDataDir[0]
		executor.settings.UserDataDir = userDir
	}

	// Create extension manager
	extensionManager := NewExtensionManager(logger, userDir)

	// Create resource manager
	resourceManager := NewResourceManager(logger, nil)

	return &BrowserAutomation{
		parser:           NewParser(),
		executor:         executor,
		logger:           logger,
		extensionManager: extensionManager,
		resourceManager:  resourceManager,
	}
}

// WithLogger sets a custom logger
func (ba *BrowserAutomation) WithLogger(logger *log.Logger) *BrowserAutomation {
	ba.logger = logger
	ba.executor = NewExecutor(logger)

	// Update extension manager with the new logger
	userDataDir := ba.executor.settings.UserDataDir
	ba.extensionManager = NewExtensionManager(logger, userDataDir)

	// Update resource manager with the new logger
	ba.resourceManager = NewResourceManager(logger, nil)

	return ba
}

// WithUserDataDir sets a custom user data directory for Chrome
func (ba *BrowserAutomation) WithUserDataDir(userDataDir string) *BrowserAutomation {
	ba.executor.settings.UserDataDir = userDataDir

	// Update extension manager with the new user data directory
	ba.extensionManager = NewExtensionManager(ba.logger, userDataDir)

	return ba
}

// ExecuteFile executes a task plan from a YAML file
func (ba *BrowserAutomation) ExecuteFile(filePath string) error {
	// Parse the task file
	plan, err := ba.parser.ParseFile(filePath)
	if err != nil {
		return NewError(ErrCodeTaskParsing, "Failed to parse task file", err).
			WithContext("file_path", filePath)
	}

	// Validate the plan
	if err := ba.validatePlan(plan); err != nil {
		return err
	}

	// Execute the plan
	return ba.executor.ExecutePlan(plan)
}

// ExecuteYAML executes a task plan from a YAML string
func (ba *BrowserAutomation) ExecuteYAML(yamlContent string) error {
	// Parse the YAML content
	plan, err := ba.parser.ParseBytes([]byte(yamlContent))
	if err != nil {
		return NewError(ErrCodeTaskParsing, "Failed to parse YAML content", err)
	}

	// Validate the plan
	if err := ba.validatePlan(plan); err != nil {
		return err
	}

	// Execute the plan
	return ba.executor.ExecutePlan(plan)
}

// ExecutePlan executes a task plan
func (ba *BrowserAutomation) ExecutePlan(plan *TaskPlan) error {
	// Validate the plan
	if err := ba.validatePlan(plan); err != nil {
		return err
	}

	return ba.executor.ExecutePlan(plan)
}

// validatePlan validates a task plan
func (ba *BrowserAutomation) validatePlan(plan *TaskPlan) error {
	if plan == nil {
		return NewError(ErrCodeConfigInvalid, "Task plan is nil", nil)
	}

	// Validate plan name
	if plan.Name == "" {
		return NewError(ErrCodeConfigInvalid, "Task plan name is required", nil)
	}

	// Validate tasks
	if len(plan.Tasks) == 0 {
		return NewError(ErrCodeConfigInvalid, "Task plan must contain at least one task", nil)
	}

	// Validate task settings
	validationResult := ValidateTaskSettings(&plan.Settings)
	if !validationResult.Valid {
		return NewConfigValidationError("Task settings validation failed", validationResult.Errors)
	}

	return nil
}

// CreateNewPlan creates a new task plan
func (ba *BrowserAutomation) CreateNewPlan(name, description string) *TaskPlan {
	return &TaskPlan{
		Name:        name,
		Description: description,
		Variables:   make(map[string]any),
		Settings: TaskSettings{
			Headless:       true,
			Timeout:        "30s",
			ViewportWidth:  1366,
			ViewportHeight: 768,
		},
		Tasks: []Task{},
	}
}

// AddNavigateTask adds a navigate task to a plan with validation
func (ba *BrowserAutomation) AddNavigateTask(plan *TaskPlan, url, description string) error {
	if url == "" {
		return NewError(ErrCodeConfigInvalid, "URL cannot be empty", nil)
	}

	task := Task{
		Type:        TaskNavigate,
		URL:         url,
		Description: description,
	}
	plan.Tasks = append(plan.Tasks, task)
	return nil
}

// AddClickTask adds a click task to a plan with validation
func (ba *BrowserAutomation) AddClickTask(plan *TaskPlan, selector string, selectorType SelectorType, description string) error {
	if selector == "" {
		return NewError(ErrCodeConfigInvalid, "Selector cannot be empty", nil)
	}

	task := Task{
		Type:         TaskClick,
		Selector:     selector,
		SelectorType: selectorType,
		Description:  description,
	}
	plan.Tasks = append(plan.Tasks, task)
	return nil
}

// AddTypeTask adds a type task to a plan
func (ba *BrowserAutomation) AddTypeTask(plan *TaskPlan, selector string, selectorType SelectorType, value, description string) {
	task := Task{
		Type:         TaskTypeText,
		Selector:     selector,
		SelectorType: selectorType,
		Value:        value,
		Description:  description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddWaitVisibleTask adds a wait visible task to a plan
func (ba *BrowserAutomation) AddWaitVisibleTask(plan *TaskPlan, selector string, selectorType SelectorType, timeout, description string) {
	task := Task{
		Type:         TaskWaitVisible,
		Selector:     selector,
		SelectorType: selectorType,
		Timeout:      timeout,
		Description:  description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddScreenshotTask adds a screenshot task to a plan
func (ba *BrowserAutomation) AddScreenshotTask(plan *TaskPlan, selector string, selectorType SelectorType, fileName, description string) {
	task := Task{
		Type:         TaskScreenshot,
		Selector:     selector,
		SelectorType: selectorType,
		FileName:     fileName,
		Description:  description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddFullScreenshotTask adds a full screenshot task to a plan
func (ba *BrowserAutomation) AddFullScreenshotTask(plan *TaskPlan, fileName, description string) {
	task := Task{
		Type:        TaskFullScreenshot,
		FileName:    fileName,
		Description: description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddGetTextTask adds a get text task to a plan
func (ba *BrowserAutomation) AddGetTextTask(plan *TaskPlan, selector string, selectorType SelectorType, variable, description string) {
	task := Task{
		Type:         TaskGetText,
		Selector:     selector,
		SelectorType: selectorType,
		Variable:     variable,
		Description:  description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddEvaluateTask adds a JavaScript evaluation task to a plan
func (ba *BrowserAutomation) AddEvaluateTask(plan *TaskPlan, javascript, variable, description string) {
	task := Task{
		Type:        TaskEvaluate,
		JavaScript:  javascript,
		Variable:    variable,
		Description: description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddInstallExtensionTask adds a task to install a Chrome extension
// extensionSource can be either a local file path or a Chrome Web Store URL
func (ba *BrowserAutomation) AddInstallExtensionTask(plan *TaskPlan, extensionSource, description string) {
	task := Task{
		Type:        TaskInstallExtension,
		Value:       extensionSource,
		Description: description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// AddSleepTask adds a sleep task to a plan
func (ba *BrowserAutomation) AddSleepTask(plan *TaskPlan, duration, description string) {
	task := Task{
		Type:        TaskSleep,
		WaitTime:    duration,
		Description: description,
	}
	plan.Tasks = append(plan.Tasks, task)
}

// WithExtensions adds extensions to be loaded at browser startup
func (ba *BrowserAutomation) WithExtensions(extensions []string, loadType string) *BrowserAutomation {
	ba.executor.settings.Extensions = extensions

	// Set load type (local, store, or auto)
	if loadType == "" {
		loadType = "auto" // Default to auto-detect
	}
	ba.executor.settings.ExtensionLoadType = loadType

	return ba
}

// SavePlan saves a task plan to a file
func (ba *BrowserAutomation) SavePlan(plan *TaskPlan, filePath string) error {
	return SaveTaskPlan(plan, filePath)
}

// GetTaskResults returns the task results
func (ba *BrowserAutomation) GetTaskResults() map[string]TaskResult {
	if ba.executor != nil && ba.executor.taskCtx != nil {
		return ba.executor.taskCtx.Results
	}
	return make(map[string]TaskResult)
}

// GetVariables returns the current variables
func (ba *BrowserAutomation) GetVariables() map[string]interface{} {
	if ba.executor != nil && ba.executor.taskCtx != nil {
		return ba.executor.taskCtx.Variables
	}
	return make(map[string]interface{})
}

// CloseBrowser closes the browser gracefully
func (ba *BrowserAutomation) CloseBrowser() error {
	if ba.executor != nil {
		ba.executor.Close()
	}
	return nil
}

// Shutdown gracefully shuts down the browser automation system
func (ba *BrowserAutomation) Shutdown() error {
	// Close executor
	if ba.executor != nil {
		ba.executor.Close()
	}

	// No need to explicitly shutdown resource manager as executor handles it
	return nil
}

// ListInstalledExtensions returns a list of installed extensions
func (ba *BrowserAutomation) ListInstalledExtensions() ([]ExtensionInfo, error) {
	return ba.extensionManager.ListInstalledExtensions()
}

// EnableExtension enables an extension
func (ba *BrowserAutomation) EnableExtension(extensionID string) error {
	return ba.extensionManager.EnableExtension(extensionID)
}

// DisableExtension disables an extension
func (ba *BrowserAutomation) DisableExtension(extensionID string) error {
	return ba.extensionManager.DisableExtension(extensionID)
}

// RemoveExtension removes an extension
func (ba *BrowserAutomation) RemoveExtension(extensionID string) error {
	return ba.extensionManager.RemoveExtension(extensionID)
}

// InstallExtension installs an extension from a local file
func (ba *BrowserAutomation) InstallExtension(extensionPath string) error {
	return ba.extensionManager.InstallExtension(extensionPath)
}
