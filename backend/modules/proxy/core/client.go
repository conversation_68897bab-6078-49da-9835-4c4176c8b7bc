package core

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strings"
	"time"

	"a8.tools/backend/utils/file"
)

// SSLocalInfo 包含 sslocal 的详细信息
type SSLocalInfo struct {
	Path         string    `json:"path"`          // 可执行文件路径
	Version      string    `json:"version"`       // 版本号
	IsInstalled  bool      `json:"is_installed"`  // 是否已安装
	IsExecutable bool      `json:"is_executable"` // 是否可执行
	FileSize     int64     `json:"file_size"`     // 文件大小
	ModTime      time.Time `json:"mod_time"`      // 修改时间
	Source       string    `json:"source"`        // 来源（local/system）
}

// V2RayInfo 包含 v2ray 的详细信息
type V2RayInfo struct {
	Path         string    `json:"path"`          // 可执行文件路径
	Version      string    `json:"version"`       // 版本号
	IsInstalled  bool      `json:"is_installed"`  // 是否已安装
	IsExecutable bool      `json:"is_executable"` // 是否可执行
	FileSize     int64     `json:"file_size"`     // 文件大小
	ModTime      time.Time `json:"mod_time"`      // 修改时间
	Source       string    `json:"source"`        // 来源（local/system）
}

// ClientDetector 客户端检测器接口
type ClientDetector interface {
	IsSSLocalInstalled() string
	GetSSLocalInfo() *SSLocalInfo
	IsV2RayInstalled() string
	GetV2RayInfo() *V2RayInfo
	ValidateSSLocalVersion(version string, minVersion string) (bool, error)
}

// clientDetector 客户端检测器实现
type clientDetector struct{}

// 确保clientDetector实现了shared.ClientDetector接口
var _ interface {
	IsSSLocalInstalled() string
	GetSSLocalInfo() *SSLocalInfo
	IsV2RayInstalled() string
	GetV2RayInfo() *V2RayInfo
	ValidateSSLocalVersion(version string, minVersion string) (bool, error)
} = (*clientDetector)(nil)

// NewClientDetector 创建新的客户端检测器
func NewClientDetector() ClientDetector {
	return &clientDetector{}
}

// IsSSLocalInstalled 检测本机是否安装了sslocal，返回sslocal路径，若不存在则返回空字符串
func (d *clientDetector) IsSSLocalInstalled() string {
	info := d.GetSSLocalInfo()
	if info.IsInstalled {
		return info.Path
	}
	return ""
}

// GetSSLocalInfo 获取 sslocal 的详细信息
func (d *clientDetector) GetSSLocalInfo() *SSLocalInfo {
	info := &SSLocalInfo{
		IsInstalled:  false,
		IsExecutable: false,
	}

	// 先检查本地 bin 目录
	if localInfo := d.checkLocalSSLocal(); localInfo != nil {
		*info = *localInfo
		info.Source = "local"
		return info
	}

	// 再检查系统环境变量
	if systemInfo := d.checkSystemSSLocal(); systemInfo != nil {
		*info = *systemInfo
		info.Source = "system"
		return info
	}

	return info
}

// checkLocalSSLocal 检查本地 bin 目录下的 sslocal
func (d *clientDetector) checkLocalSSLocal() *SSLocalInfo {
	proxyDir, err := file.GetProxyBinDir()
	if err != nil {
		return nil
	}

	var sslocal string
	if runtime.GOOS == "windows" {
		sslocal = filepath.Join(proxyDir, "sslocal", "sslocal.exe")
	} else {
		sslocal = filepath.Join(proxyDir, "sslocal", "sslocal")
	}

	return d.checkSSLocalBinary(sslocal)
}

// checkSystemSSLocal 检查系统环境变量中的 sslocal
func (d *clientDetector) checkSystemSSLocal() *SSLocalInfo {
	var cmd string
	if runtime.GOOS == "windows" {
		cmd = "sslocal.exe"
	} else {
		cmd = "sslocal"
	}

	path, err := exec.LookPath(cmd)
	if err != nil {
		return nil
	}

	return d.checkSSLocalBinary(path)
}

// checkSSLocalBinary 检查指定路径的 sslocal 二进制文件
func (d *clientDetector) checkSSLocalBinary(path string) *SSLocalInfo {
	info := &SSLocalInfo{
		Path: path,
	}

	// 检查文件是否存在
	stat, err := os.Stat(path)
	if err != nil {
		return nil
	}

	info.IsInstalled = true
	info.FileSize = stat.Size()
	info.ModTime = stat.ModTime()

	// 检查是否可执行
	if d.isExecutable(path) {
		info.IsExecutable = true

		// 获取版本信息
		if version := d.getSSLocalVersion(path); version != "" {
			info.Version = version
		}
	}

	return info
}

// getSSLocalVersion 获取 sslocal 版本信息
func (d *clientDetector) getSSLocalVersion(path string) string {
	// 尝试不同的版本命令
	versionCommands := [][]string{
		{path, "--version"},
		{path, "-V"},
		{path, "version"},
	}

	for _, cmd := range versionCommands {
		if version := d.executeVersionCommand(cmd); version != "" {
			return version
		}
	}

	return ""
}

// IsV2RayInstalled 检测本机是否安装了v2ray，返回v2ray路径，若不存在则返回空字符串
func (d *clientDetector) IsV2RayInstalled() string {
	info := d.GetV2RayInfo()
	if info.IsInstalled {
		return info.Path
	}
	return ""
}

// GetV2RayInfo 获取 v2ray 的详细信息
func (d *clientDetector) GetV2RayInfo() *V2RayInfo {
	info := &V2RayInfo{
		IsInstalled:  false,
		IsExecutable: false,
	}

	// 先检查本地 bin 目录
	if localInfo := d.checkLocalV2Ray(); localInfo != nil {
		*info = *localInfo
		info.Source = "local"
		return info
	}

	// 再检查系统环境变量
	if systemInfo := d.checkSystemV2Ray(); systemInfo != nil {
		*info = *systemInfo
		info.Source = "system"
		return info
	}

	return info
}

// checkLocalV2Ray 检查本地 bin 目录下的 v2ray
func (d *clientDetector) checkLocalV2Ray() *V2RayInfo {
	proxyDir, err := file.GetProxyBinDir()
	if err != nil {
		return nil
	}

	var v2ray string
	if runtime.GOOS == "windows" {
		v2ray = filepath.Join(proxyDir, "v2ray", "v2ray.exe")
	} else {
		v2ray = filepath.Join(proxyDir, "v2ray", "v2ray")
	}

	return d.checkV2RayBinary(v2ray)
}

// checkSystemV2Ray 检查系统环境变量中的 v2ray
func (d *clientDetector) checkSystemV2Ray() *V2RayInfo {
	var cmd string
	if runtime.GOOS == "windows" {
		cmd = "v2ray.exe"
	} else {
		cmd = "v2ray"
	}

	path, err := exec.LookPath(cmd)
	if err != nil {
		return nil
	}

	return d.checkV2RayBinary(path)
}

// checkV2RayBinary 检查指定路径的 v2ray 二进制文件
func (d *clientDetector) checkV2RayBinary(path string) *V2RayInfo {
	info := &V2RayInfo{
		Path: path,
	}

	// 检查文件是否存在
	stat, err := os.Stat(path)
	if err != nil {
		return nil
	}

	info.IsInstalled = true
	info.FileSize = stat.Size()
	info.ModTime = stat.ModTime()

	// 检查是否可执行
	if d.isExecutable(path) {
		info.IsExecutable = true

		// 获取版本信息
		if version := d.getV2RayVersion(path); version != "" {
			info.Version = version
		}
	}

	return info
}

// getV2RayVersion 获取 v2ray 版本信息
func (d *clientDetector) getV2RayVersion(path string) string {
	// 尝试不同的版本命令
	versionCommands := [][]string{
		{path, "--version"},
		{path, "-version"},
		{path, "version"},
	}

	for _, cmd := range versionCommands {
		if version := d.executeVersionCommand(cmd); version != "" {
			return version
		}
	}

	return ""
}

// executeVersionCommand 执行版本命令并解析输出
func (d *clientDetector) executeVersionCommand(cmdArgs []string) string {
	if len(cmdArgs) == 0 {
		return ""
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, cmdArgs[0], cmdArgs[1:]...)
	output, err := cmd.CombinedOutput()
	if err != nil {
		return ""
	}

	return d.parseVersionFromOutput(string(output))
}

// parseVersionFromOutput 从命令输出中解析版本号
func (d *clientDetector) parseVersionFromOutput(output string) string {
	// 常见的版本号格式正则表达式
	versionPatterns := []*regexp.Regexp{
		regexp.MustCompile(`(?i)version\s+([0-9]+\.[0-9]+\.[0-9]+(?:-[a-zA-Z0-9]+)?)`),
		regexp.MustCompile(`(?i)v?([0-9]+\.[0-9]+\.[0-9]+(?:-[a-zA-Z0-9]+)?)`),
		regexp.MustCompile(`([0-9]+\.[0-9]+\.[0-9]+)`),
	}

	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		for _, pattern := range versionPatterns {
			matches := pattern.FindStringSubmatch(line)
			if len(matches) > 1 {
				return strings.TrimSpace(matches[1])
			}
		}
	}

	return ""
}

// isExecutable 检查文件是否可执行
func (d *clientDetector) isExecutable(path string) bool {
	info, err := os.Stat(path)
	if err != nil {
		return false
	}

	// Windows 系统检查文件扩展名
	if runtime.GOOS == "windows" {
		return strings.HasSuffix(strings.ToLower(path), ".exe")
	}

	// Unix 系统检查执行权限
	mode := info.Mode()
	return mode&0111 != 0
}

// ValidateSSLocalVersion 验证 sslocal 版本是否满足最低要求
func (d *clientDetector) ValidateSSLocalVersion(version string, minVersion string) (bool, error) {
	if version == "" {
		return false, fmt.Errorf("version is empty")
	}

	if minVersion == "" {
		return true, nil // 如果没有最低版本要求，则认为有效
	}

	// 解析版本号
	versionParts, err := d.parseVersion(version)
	if err != nil {
		return false, fmt.Errorf("invalid version format: %s", version)
	}

	minVersionParts, err := d.parseVersion(minVersion)
	if err != nil {
		return false, fmt.Errorf("invalid minimum version format: %s", minVersion)
	}

	// 比较版本号
	return d.compareVersions(versionParts, minVersionParts) >= 0, nil
}

// parseVersion 解析版本号字符串为数字数组
func (d *clientDetector) parseVersion(version string) ([]int, error) {
	// 移除 v 前缀
	version = strings.TrimPrefix(version, "v")
	version = strings.TrimPrefix(version, "V")

	// 分割版本号
	parts := strings.Split(version, ".")
	if len(parts) < 2 {
		return nil, fmt.Errorf("invalid version format: %s", version)
	}

	var result []int
	for _, part := range parts {
		// 移除预发布标识符（如 -alpha, -beta）
		if idx := strings.Index(part, "-"); idx != -1 {
			part = part[:idx]
		}

		num := 0
		for _, r := range part {
			if r >= '0' && r <= '9' {
				num = num*10 + int(r-'0')
			} else {
				break
			}
		}
		result = append(result, num)
	}

	return result, nil
}

// compareVersions 比较两个版本号数组
// 返回值：1 表示 v1 > v2，0 表示 v1 == v2，-1 表示 v1 < v2
func (d *clientDetector) compareVersions(v1, v2 []int) int {
	maxLen := len(v1)
	if len(v2) > maxLen {
		maxLen = len(v2)
	}

	for i := 0; i < maxLen; i++ {
		val1 := 0
		val2 := 0

		if i < len(v1) {
			val1 = v1[i]
		}
		if i < len(v2) {
			val2 = v2[i]
		}

		if val1 > val2 {
			return 1
		} else if val1 < val2 {
			return -1
		}
	}

	return 0
}

// 全局实例，保持向后兼容
var defaultDetector = NewClientDetector()

// IsSSLocalInstalled 向后兼容的函数
func IsSSLocalInstalled() string {
	return defaultDetector.IsSSLocalInstalled()
}

// GetSSLocalInfo 向后兼容的函数
func GetSSLocalInfo() *SSLocalInfo {
	return defaultDetector.GetSSLocalInfo()
}

// IsV2RayInstalled 向后兼容的函数
func IsV2RayInstalled() string {
	return defaultDetector.IsV2RayInstalled()
}

// GetV2RayInfo 向后兼容的函数
func GetV2RayInfo() *V2RayInfo {
	return defaultDetector.GetV2RayInfo()
}

// ValidateSSLocalVersion 向后兼容的函数
func ValidateSSLocalVersion(version string, minVersion string) (bool, error) {
	return defaultDetector.ValidateSSLocalVersion(version, minVersion)
}
