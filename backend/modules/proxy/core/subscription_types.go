package core

import (
	"fmt"
	"time"
)

// AddSubscriptionRequest 添加订阅源请求
type AddSubscriptionRequest struct {
	Name        string `json:"name"`                  // 订阅源名称
	URL         string `json:"url"`                   // 订阅链接
	Description string `json:"description,omitempty"` // 描述信息
	UpdatedBy   string `json:"updated_by,omitempty"`  // 更新者
}

// AddSubscriptionResponse 添加订阅源响应
type AddSubscriptionResponse struct {
	ID        string    `json:"id"`         // 生成的订阅源ID
	Name      string    `json:"name"`       // 订阅源名称
	URL       string    `json:"url"`        // 订阅链接
	Type      string    `json:"type"`       // 检测到的订阅类型
	CreatedAt time.Time `json:"created_at"` // 创建时间
	Status    string    `json:"status"`     // 状态
}

// ListSubscriptionsRequest 列表查询请求
type ListSubscriptionsRequest struct {
	Page      int        `json:"page,omitempty"`       // 页码，从1开始
	PageSize  int        `json:"page_size,omitempty"`  // 每页大小，默认10
	Search    string     `json:"search,omitempty"`     // 搜索关键词
	Status    []string   `json:"status,omitempty"`     // 状态过滤
	Type      []string   `json:"type,omitempty"`       // 类型过滤
	SortBy    string     `json:"sort_by,omitempty"`    // 排序字段
	SortOrder string     `json:"sort_order,omitempty"` // 排序方向
	UpdatedBy string     `json:"updated_by,omitempty"` // 更新者过滤
	DateRange *DateRange `json:"date_range,omitempty"` // 日期范围过滤
}

// DateRange 日期范围
type DateRange struct {
	StartDate *time.Time `json:"start_date,omitempty"` // 开始日期
	EndDate   *time.Time `json:"end_date,omitempty"`   // 结束日期
}

// ListSubscriptionsResponse 列表查询响应
type ListSubscriptionsResponse struct {
	Items      []SubscriptionListItem `json:"items"`       // 订阅列表
	TotalCount int                    `json:"total_count"` // 总数
	Page       int                    `json:"page"`        // 当前页
	PageSize   int                    `json:"page_size"`   // 每页大小
	TotalPages int                    `json:"total_pages"` // 总页数
}

// SubscriptionListItem 订阅列表项
type SubscriptionListItem struct {
	ID          string     `json:"id"`                    // 订阅ID
	Name        string     `json:"name"`                  // 订阅名称
	URL         string     `json:"url"`                   // 订阅链接
	Type        string     `json:"type"`                  // 订阅类型
	Status      string     `json:"status"`                // 状态
	NodeCount   int        `json:"node_count"`            // 节点数量
	CreatedAt   time.Time  `json:"created_at"`            // 创建时间
	UpdatedAt   time.Time  `json:"updated_at"`            // 更新时间
	LastUpdate  *time.Time `json:"last_update,omitempty"` // 最后更新时间
	Description string     `json:"description"`           // 描述
	UpdatedBy   string     `json:"updated_by"`            // 更新者
}

// DeleteSubscriptionRequest 删除订阅请求
type DeleteSubscriptionRequest struct {
	ID                string `json:"id"`                           // 订阅ID
	Force             bool   `json:"force,omitempty"`              // 是否强制删除
	ConfirmationToken string `json:"confirmation_token,omitempty"` // 确认令牌
}

// DeleteSubscriptionResponse 删除订阅响应
type DeleteSubscriptionResponse struct {
	ID                string   `json:"id"`                           // 删除的订阅ID
	Success           bool     `json:"success"`                      // 是否成功
	Message           string   `json:"message"`                      // 操作消息
	Dependencies      []string `json:"dependencies,omitempty"`       // 存在的依赖（如果有）
	ConfirmationToken string   `json:"confirmation_token,omitempty"` // 确认令牌（需要确认时）
}

// UpdateSubscriptionRequest 更新订阅请求
type UpdateSubscriptionRequest struct {
	ID          string `json:"id"`                    // 订阅ID
	Name        string `json:"name,omitempty"`        // 新名称
	Description string `json:"description,omitempty"` // 新描述
	UpdatedBy   string `json:"updated_by,omitempty"`  // 更新者
}

// UpdateSubscriptionResponse 更新订阅响应
type UpdateSubscriptionResponse struct {
	ID        string    `json:"id"`         // 订阅ID
	Name      string    `json:"name"`       // 更新后的名称
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
	Success   bool      `json:"success"`    // 是否成功
	Message   string    `json:"message"`    // 操作消息
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`   // 错误字段
	Message string `json:"message"` // 错误信息
}

func (e ValidationError) Error() string {
	return fmt.Sprintf("%s: %s", e.Field, e.Message)
}
