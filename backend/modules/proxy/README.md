# 代理模块

A8Tools 的代理模块是一个功能完整的代理管理系统，支持 Shadowsocks、V2Ray、SSR 等多种代理协议的统一管理。

## 功能特性

### 🚀 核心功能
- **订阅源管理**: 支持多种订阅格式的自动解析和管理
- **代理进程管理**: 完整的进程生命周期管理，包括启动、停止、监控
- **智能端口分配**: 自动端口分配和冲突检测
- **配置文件管理**: 自动生成和管理代理配置文件
- **健康检查**: HTTP/TCP 健康检查和异常恢复
- **性能监控**: 实时性能指标收集和分析

### 🛡️ 高级特性
- **错误恢复**: 智能的进程异常检测和自动重启机制
- **批量操作**: 支持批量启动、停止、重启代理
- **日志记录**: 结构化日志记录和错误追踪
- **备份恢复**: 配置文件备份和恢复功能
- **跨平台支持**: Windows、macOS、Linux 全平台支持

### 📊 监控与统计
- **实时监控**: 进程状态、性能指标、健康状态
- **统计分析**: 运行时间、错误率、资源使用情况
- **事件系统**: 完整的事件通知和订阅机制

## 快速开始

### 安装依赖

```bash
# 安装 Go 依赖
go mod tidy

# 确保系统中有 sslocal 或 v2ray 客户端
# 模块会自动检测并下载所需的客户端
```

### 基本使用

```go
package main

import (
    "context"
    "log"
    
    "a8.tools/backend/pkg/proxy"
)

func main() {
    // 创建代理服务
    service, err := proxy.NewDefaultProxyService(&proxy.DefaultServiceOptions{})
    if err != nil {
        log.Fatal("Failed to create proxy service:", err)
    }
    defer service.Close()

    // 添加订阅源
    ctx := context.Background()
    subscription, err := service.AddSubscription(ctx, &proxy.AddSubscriptionRequest{
        Name: "My Subscription",
        URL:  "https://example.com/subscription",
        Description: "示例订阅源",
    })
    if err != nil {
        log.Fatal("Failed to add subscription:", err)
    }

    log.Printf("Added subscription: %s", subscription.Name)

    // 获取代理节点
    nodes, err := service.ListProxyNodes(ctx, &proxy.ListProxyNodesRequest{
		SubscriptionID: subscription.ID,
	})
    if err != nil {
        log.Fatal("Failed to get proxy nodes:", err)
    }

    if len(nodes) > 0 {
        // 启动第一个节点的代理
        instance, err := service.StartProxy(ctx, &proxy.StartProxyRequest{
            NodeID: nodes[0].ID,
        })
        if err != nil {
            log.Fatal("Failed to start proxy:", err)
        }

        log.Printf("Proxy started: %s (Port: %d)", instance.Name, instance.LocalPort)
    }
}
```

## 接口文档

### `IProxyService`

代理服务的核心接口，定义了所有代理相关操作。

#### 订阅管理

- `AddSubscription(context.Context, *AddSubscriptionRequest) (*models.Subscription, error)`: 添加一个新的订阅源。
- `DeleteSubscription(context.Context, string) error`: 删除一个订阅源。
- `UpdateSubscription(context.Context, *UpdateSubscriptionRequest) error`: 更新订阅源信息。
- `GetSubscription(context.Context, string) (*models.Subscription, error)`: 获取订阅源信息。
- `ListSubscriptions(context.Context) ([]*models.Subscription, error)`: 列出所有订阅源。
- `SyncSubscription(context.Context, string) error`: 同步订阅源，获取最新的代理节点。

#### 代理节点

- `GetProxyNode(context.Context, string) (*models.ProxyNode, error)`: 获取代理节点信息。
- `ListProxyNodes(context.Context, *ListProxyNodesRequest) ([]*models.ProxyNode, error)`: 列出所有代理节点。
- `UpdateProxyNode(context.Context, *UpdateProxyNodeRequest) error`: 更新代理节点信息。

#### 代理实例

- `StartProxy(context.Context, *StartProxyRequest) (*models.ProxyInstance, error)`: 启动一个代理实例。
- `StopProxy(context.Context, string) error`: 停止一个代理实例。
- `GetProxyInstance(context.Context, string) (*models.ProxyInstance, error)`: 获取代理实例信息。
- `ListProxyInstances(context.Context) ([]*models.ProxyInstance, error)`: 列出所有代理实例。

#### 健康检查

- `HealthCheck(context.Context, string) (*models.HealthCheckResult, error)`: 手动触发一次健康检查。

## 许可证

本项目遵循 MIT 许可证。
