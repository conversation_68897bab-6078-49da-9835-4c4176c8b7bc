package common

import (
	"crypto/md5"
	"fmt"
	"net"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

// FileUtils 文件操作工具
type FileUtils struct{}

// EnsureDir 确保目录存在
func (fu *FileUtils) EnsureDir(dir string) error {
	return os.MkdirAll(dir, 0755)
}

// FileExists 检查文件是否存在
func (fu *FileUtils) FileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// CalculateChecksum 计算文件校验和
func (fu *FileUtils) CalculateChecksum(filePath string) (string, error) {
	content, err := os.ReadFile(filePath)
	if err != nil {
		return "", err
	}
	return fmt.Sprintf("%x", md5.Sum(content)), nil
}

// GetTempConfigPath 获取临时配置文件路径
func (fu *FileUtils) GetTempConfigPath(id string) string {
	tempDir := filepath.Join(os.TempDir(), ConfigDirName)
	return filepath.Join(tempDir, fmt.Sprintf("%s%s.json", TempConfigPrefix, id))
}

// ValidationUtils 验证工具
type ValidationUtils struct{}

// IsValidIPAddress 验证IP地址
func (vu *ValidationUtils) IsValidIPAddress(ip string) bool {
	return net.ParseIP(ip) != nil
}

// IsValidPort 验证端口号
func (vu *ValidationUtils) IsValidPort(port int) bool {
	return port > 0 && port <= 65535
}

// IsValidDomainName 验证域名
func (vu *ValidationUtils) IsValidDomainName(domain string) bool {
	if len(domain) == 0 || len(domain) > 253 {
		return false
	}

	domainRegex := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$`)
	return domainRegex.MatchString(domain)
}

// IsValidServerAddress 验证服务器地址
func (vu *ValidationUtils) IsValidServerAddress(address string) bool {
	return vu.IsValidIPAddress(address) || vu.IsValidDomainName(address)
}

// IsValidEncryptionMethod 验证加密方法
func (vu *ValidationUtils) IsValidEncryptionMethod(method string) bool {
	validMethods := []string{
		"aes-128-gcm", "aes-192-gcm", "aes-256-gcm",
		"aes-128-cfb", "aes-192-cfb", "aes-256-cfb",
		"aes-128-ctr", "aes-192-ctr", "aes-256-ctr",
		"chacha20-ietf", "chacha20-ietf-poly1305",
		"xchacha20-ietf-poly1305",
	}

	for _, valid := range validMethods {
		if method == valid {
			return true
		}
	}
	return false
}

// StringUtils 字符串工具
type StringUtils struct{}

// ParsePort 解析端口号（支持字符串和数字）
func (su *StringUtils) ParsePort(port interface{}) (int, error) {
	switch p := port.(type) {
	case int:
		return p, nil
	case float64:
		return int(p), nil
	case string:
		return strconv.Atoi(p)
	default:
		return 0, fmt.Errorf("unsupported port type: %T", port)
	}
}

// FormatMemorySize 格式化内存大小
func (su *StringUtils) FormatMemorySize(bytes int64) string {
	const unit = 1024
	if bytes < unit {
		return fmt.Sprintf("%d B", bytes)
	}
	div, exp := int64(unit), 0
	for n := bytes / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(bytes)/float64(div), "KMGTPE"[exp])
}

// SanitizeFilename 清理文件名
func (su *StringUtils) SanitizeFilename(filename string) string {
	// 移除或替换不安全的字符
	unsafe := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|"}
	sanitized := filename
	for _, char := range unsafe {
		sanitized = strings.ReplaceAll(sanitized, char, "_")
	}
	return sanitized
}

// NetworkUtils 网络工具
type NetworkUtils struct{}

// IsPortAvailable 检查端口是否可用
func (nu *NetworkUtils) IsPortAvailable(address string, port int) bool {
	addr := fmt.Sprintf("%s:%d", address, port)
	conn, err := net.Listen("tcp", addr)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// GetAvailablePort 获取可用端口
func (nu *NetworkUtils) GetAvailablePort(address string, startPort int) (int, error) {
	for port := startPort; port <= 65535; port++ {
		if nu.IsPortAvailable(address, port) {
			return port, nil
		}
	}
	return 0, fmt.Errorf("no available port found starting from %d", startPort)
}

// 全局工具实例
var (
	FileUtil       = &FileUtils{}
	ValidationUtil = &ValidationUtils{}
	StringUtil     = &StringUtils{}
	NetworkUtil    = &NetworkUtils{}
)
