package shared

import (
	"context"
	"time"

	"a8.tools/backend/modules/proxy/core"
	"a8.tools/backend/modules/proxy/infrastructure/port"
	"a8.tools/backend/modules/proxy/types"
)

// ClientDetector defines the interface for detecting proxy clients
type ClientDetector interface {
	// IsSSLocalInstalled checks if sslocal is installed
	IsSSLocalInstalled() string

	// GetSSLocalInfo returns detailed information about sslocal
	GetSSLocalInfo() *core.SSLocalInfo

	// IsV2RayInstalled checks if v2ray is installed
	IsV2RayInstalled() string

	// GetV2RayInfo returns detailed information about v2ray
	GetV2RayInfo() *core.V2RayInfo

	// ValidateSSLocalVersion validates if the sslocal version meets minimum requirements
	ValidateSSLocalVersion(version string, minVersion string) (bool, error)
}

// SubscriptionManager defines the interface for managing subscriptions
type SubscriptionManager interface {
	// AddSubscription adds a new subscription
	AddSubscription(req core.AddSubscriptionRequest) (*core.AddSubscriptionResponse, error)

	// GetSubscription retrieves a subscription by ID
	GetSubscription(id string) (*core.SubscriptionData, error)

	// GetAllSubscriptions lists all subscriptions
	GetAllSubscriptions() []core.SubscriptionData

	// UpdateSubscription updates an existing subscription
	UpdateSubscription(req core.UpdateSubscriptionRequest) (*core.UpdateSubscriptionResponse, error)

	// DeleteSubscription deletes a subscription
	DeleteSubscription(req core.DeleteSubscriptionRequest) (*core.DeleteSubscriptionResponse, error)

	// ListSubscriptions lists subscriptions with filtering and pagination
	ListSubscriptions(req core.ListSubscriptionsRequest) (*core.ListSubscriptionsResponse, error)
}

// ConfigManager defines the interface for configuration management
type ConfigManager interface {
	// GenerateConfigFile generates configuration file for a proxy node
	GenerateConfigFile(node *types.Node, localAddress string, localPort int, processID string) (string, error)

	// ValidateConfig validates a configuration file
	ValidateConfig(configPath string, configType string) (*core.ConfigValidationResult, error)

	// DeleteConfigFile deletes a configuration file and its metadata
	DeleteConfigFile(processID string) error
}

// ProcessManager defines the interface for managing proxy processes
type ProcessManager interface {
	// Process lifecycle management
	StartProcess(config *core.ProxyProcessConfig) error
	StopProcess(processID string) error
	RestartProcess(processID string) error

	// Process information
	GetProcess(processID string) (*core.ProxyProcessInfo, error)
	GetAllProcesses() []*core.ProxyProcessInfo
	GetProcessesByFilter(filter *core.ProcessFilter) []*core.ProxyProcessInfo
	GetProcessesByStatus(status core.ProcessStatus) []*core.ProxyProcessInfo

	// Batch operations
	StartBatch(processIDs []string) (*core.BatchOperation, error)
	StopBatch(processIDs []string) (*core.BatchOperation, error)
	RestartBatch(processIDs []string) (*core.BatchOperation, error)

	// Health monitoring
	CheckHealth(processID string) (*core.ProcessHealthInfo, error)
	CheckAllHealth() ([]*core.ProcessHealthInfo, error)

	// Performance monitoring
	GetPerformanceMetrics(processID string) (*core.PerformanceMetrics, error)
	GetAllPerformanceMetrics() ([]*core.PerformanceMetrics, error)

	// Event management
	Subscribe(subscriber core.EventSubscriber) string
	Unsubscribe(subscriptionID string) error

	// Utility methods
	IsProcessRunning(processID string) bool
	GetProcessCount() int
	GetRunningProcessCount() int

	// Cleanup and shutdown
	Shutdown(ctx context.Context) error
	GracefulShutdown(timeout time.Duration) error
}

// PortAllocator defines the interface for port allocation
type PortAllocator interface {
	// AllocatePort allocates an available port
	AllocatePort(req port.AllocationRequest) (*port.AllocationResult, error)

	// ReleasePort releases an allocated port
	ReleasePort(port int) error

	// GetStats returns current allocation statistics
	GetStats() *port.PortAllocatorStats
}

// HealthChecker defines the interface for health checking
type HealthChecker interface {
	// CheckHealth performs a health check on a specific target
	CheckHealth(target string) (*HealthCheckResult, error)

	// CheckAllHealth performs health checks on all registered targets
	CheckAllHealth() ([]*HealthCheckResult, error)

	// RegisterTarget registers a target for health checking
	RegisterTarget(target string, config *HealthCheckConfig) error

	// UnregisterTarget removes a target from health checking
	UnregisterTarget(target string) error
}

// HealthCheckResult represents the result of a health check
type HealthCheckResult struct {
	Target       string        `json:"target"`
	Healthy      bool          `json:"healthy"`
	ResponseTime time.Duration `json:"response_time"`
	Error        string        `json:"error,omitempty"`
	Timestamp    time.Time     `json:"timestamp"`
}

// HealthCheckConfig represents configuration for health checks
type HealthCheckConfig struct {
	Type     string        `json:"type"`     // "http", "tcp", "process"
	Target   string        `json:"target"`   // URL, address, or process ID
	Interval time.Duration `json:"interval"` // Check interval
	Timeout  time.Duration `json:"timeout"`  // Check timeout
	Retries  int           `json:"retries"`  // Number of retries
}

// StorageManager defines the interface for data storage operations
type StorageManager interface {
	// Subscription storage
	SaveSubscription(subscription *core.SubscriptionData) error
	LoadSubscription(id string) (*core.SubscriptionData, error)
	DeleteSubscription(id string) error
	ListSubscriptions() ([]*core.SubscriptionData, error)

	// Configuration storage
	SaveConfig(config *core.ConfigFileMetadata) error
	LoadConfig(id string) (*core.ConfigFileMetadata, error)
	DeleteConfig(id string) error

	// Process storage
	SaveProcessInfo(info *core.ProxyProcessInfo) error
	LoadProcessInfo(id string) (*core.ProxyProcessInfo, error)
	DeleteProcessInfo(id string) error
}

// EventManager defines the interface for event management
type EventManager interface {
	// Publish publishes an event
	Publish(event *Event) error

	// Subscribe subscribes to events of a specific type
	Subscribe(eventType string, handler EventHandler) (string, error)

	// Unsubscribe removes an event subscription
	Unsubscribe(subscriptionID string) error

	// GetEventHistory returns recent events
	GetEventHistory(limit int) ([]*Event, error)
}

// Event represents a system event
type Event struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
}

// EventHandler defines a function type for handling events
type EventHandler func(event *Event) error

// Logger defines the interface for logging
type Logger interface {
	Debug(msg string, fields ...interface{})
	Info(msg string, fields ...interface{})
	Warn(msg string, fields ...interface{})
	Error(msg string, fields ...interface{})
	Fatal(msg string, fields ...interface{})
}

// Metrics defines the interface for metrics collection
type Metrics interface {
	// Counter metrics
	IncrementCounter(name string, labels map[string]string)
	AddToCounter(name string, value float64, labels map[string]string)

	// Gauge metrics
	SetGauge(name string, value float64, labels map[string]string)

	// Histogram metrics
	RecordHistogram(name string, value float64, labels map[string]string)

	// Timer metrics
	StartTimer(name string, labels map[string]string) TimerFunc
}

// TimerFunc represents a function to stop a timer
type TimerFunc func()

// Validator defines the interface for validation operations
type Validator interface {
	// ValidateSubscriptionRequest validates a subscription request
	ValidateSubscriptionRequest(req *core.AddSubscriptionRequest) error

	// ValidateProcessConfig validates a process configuration
	ValidateProcessConfig(config *core.ProxyProcessConfig) error

	// ValidatePortRange validates a port range
	ValidatePortRange(portRange *port.PortRange) error
}
