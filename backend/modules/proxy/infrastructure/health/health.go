package health

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"strings"
	"time"

	"a8.tools/backend/modules/proxy/common"
)

// HealthStatus 健康状态枚举
type HealthStatus string

const (
	StatusUnknown   HealthStatus = "unknown"
	StatusHealthy   HealthStatus = "healthy"
	StatusUnhealthy HealthStatus = "unhealthy"
	StatusChecking  HealthStatus = "checking"
)

// CheckType 检查类型
type CheckType string

const (
	CheckTypeHTTP    CheckType = "http"
	CheckTypeTCP     CheckType = "tcp"
	CheckTypeProcess CheckType = "process"
	CheckTypeCustom  CheckType = "custom"
)

// HealthCheckConfig 健康检查配置
type HealthCheckConfig struct {
	Enabled          bool          `json:"enabled"`
	Type             CheckType     `json:"type"`
	Interval         time.Duration `json:"interval"`
	Timeout          time.Duration `json:"timeout"`
	FailureThreshold int           `json:"failure_threshold"`
	SuccessThreshold int           `json:"success_threshold"`
	HTTPCheck        *HTTPCheck    `json:"http_check,omitempty"`
	TCPCheck         *TCPCheck     `json:"tcp_check,omitempty"`
	ProcessCheck     *ProcessCheck `json:"process_check,omitempty"`
	CustomCheck      *CustomCheck  `json:"custom_check,omitempty"`
}

// HTTPCheck HTTP 健康检查配置
type HTTPCheck struct {
	URL             string            `json:"url"`
	Method          string            `json:"method"`
	Headers         map[string]string `json:"headers"`
	ExpectedStatus  int               `json:"expected_status"`
	ExpectedBody    string            `json:"expected_body"`
	FollowRedirects bool              `json:"follow_redirects"`
}

// TCPCheck TCP 健康检查配置
type TCPCheck struct {
	Address string        `json:"address"`
	Port    int           `json:"port"`
	Timeout time.Duration `json:"timeout"`
}

// ProcessCheck 进程健康检查配置
type ProcessCheck struct {
	PID         int    `json:"pid"`
	ProcessName string `json:"process_name,omitempty"`
}

// CustomCheck 自定义健康检查配置
type CustomCheck struct {
	Command []string          `json:"command"`
	Env     map[string]string `json:"env,omitempty"`
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	Status       HealthStatus  `json:"status"`
	CheckType    CheckType     `json:"check_type"`
	Timestamp    time.Time     `json:"timestamp"`
	Duration     time.Duration `json:"duration"`
	Message      string        `json:"message,omitempty"`
	Error        string        `json:"error,omitempty"`
	ResponseCode int           `json:"response_code,omitempty"`
	ResponseTime time.Duration `json:"response_time,omitempty"`
}

// HealthChecker 健康检查器接口
type HealthChecker interface {
	Check(ctx context.Context, config *HealthCheckConfig) *HealthCheckResult
	GetType() CheckType
}

// HTTPHealthChecker HTTP 健康检查器
type HTTPHealthChecker struct{}

// Check 执行 HTTP 健康检查
func (hc *HTTPHealthChecker) Check(ctx context.Context, config *HealthCheckConfig) *HealthCheckResult {
	start := time.Now()
	result := &HealthCheckResult{
		CheckType: CheckTypeHTTP,
		Timestamp: start,
		Status:    StatusChecking,
	}

	if config.HTTPCheck == nil {
		result.Status = StatusUnhealthy
		result.Error = "HTTP check configuration is missing"
		result.Duration = time.Since(start)
		return result
	}

	httpCheck := config.HTTPCheck

	// 创建 HTTP 客户端
	client := &http.Client{
		Timeout: config.Timeout,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if !httpCheck.FollowRedirects {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	// 准备请求
	method := httpCheck.Method
	if method == "" {
		method = "GET"
	}

	req, err := http.NewRequestWithContext(ctx, method, httpCheck.URL, nil)
	if err != nil {
		result.Status = StatusUnhealthy
		result.Error = fmt.Sprintf("failed to create request: %v", err)
		result.Duration = time.Since(start)
		return result
	}

	// 添加自定义头
	for key, value := range httpCheck.Headers {
		req.Header.Set(key, value)
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		result.Status = StatusUnhealthy
		result.Error = fmt.Sprintf("HTTP request failed: %v", err)
		result.Duration = time.Since(start)
		return result
	}
	defer resp.Body.Close()

	result.ResponseCode = resp.StatusCode
	result.ResponseTime = time.Since(start)

	// 检查状态码
	expectedStatus := httpCheck.ExpectedStatus
	if expectedStatus == 0 {
		expectedStatus = common.DefaultHTTPExpectedStatus
	}

	if resp.StatusCode != expectedStatus {
		result.Status = StatusUnhealthy
		result.Error = fmt.Sprintf("unexpected status code: %d, expected: %d", resp.StatusCode, expectedStatus)
		result.Duration = time.Since(start)
		return result
	}

	// 检查响应体
	if httpCheck.ExpectedBody != "" {
		buffer := make([]byte, len(httpCheck.ExpectedBody)*2)
		n, err := resp.Body.Read(buffer)
		if err != nil && n == 0 {
			result.Status = StatusUnhealthy
			result.Error = fmt.Sprintf("failed to read response body: %v", err)
			result.Duration = time.Since(start)
			return result
		}

		bodyContent := string(buffer[:n])
		if !strings.Contains(bodyContent, httpCheck.ExpectedBody) {
			result.Status = StatusUnhealthy
			result.Error = fmt.Sprintf("response body does not contain expected content: %s", httpCheck.ExpectedBody)
			result.Duration = time.Since(start)
			return result
		}
	}

	result.Status = StatusHealthy
	result.Message = "HTTP check passed"
	result.Duration = time.Since(start)
	return result
}

// GetType 返回检查器类型
func (hc *HTTPHealthChecker) GetType() CheckType {
	return CheckTypeHTTP
}

// TCPHealthChecker TCP 健康检查器
type TCPHealthChecker struct{}

// Check 执行 TCP 健康检查
func (tc *TCPHealthChecker) Check(ctx context.Context, config *HealthCheckConfig) *HealthCheckResult {
	start := time.Now()
	result := &HealthCheckResult{
		CheckType: CheckTypeTCP,
		Timestamp: start,
		Status:    StatusChecking,
	}

	if config.TCPCheck == nil {
		result.Status = StatusUnhealthy
		result.Error = "TCP check configuration is missing"
		result.Duration = time.Since(start)
		return result
	}

	tcpCheck := config.TCPCheck
	timeout := tcpCheck.Timeout
	if timeout == 0 {
		timeout = common.DefaultTCPTimeout
	}

	// 创建 TCP 连接
	address := fmt.Sprintf("%s:%d", tcpCheck.Address, tcpCheck.Port)

	dialer := &net.Dialer{
		Timeout: timeout,
	}

	conn, err := dialer.DialContext(ctx, "tcp", address)
	if err != nil {
		result.Status = StatusUnhealthy
		result.Error = fmt.Sprintf("TCP connection failed: %v", err)
		result.Duration = time.Since(start)
		return result
	}
	defer conn.Close()

	result.Status = StatusHealthy
	result.Message = "TCP connection successful"
	result.Duration = time.Since(start)
	return result
}

// GetType 返回检查器类型
func (tc *TCPHealthChecker) GetType() CheckType {
	return CheckTypeTCP
}

// ProcessHealthChecker 进程健康检查器
type ProcessHealthChecker struct{}

// Check 执行进程健康检查
func (pc *ProcessHealthChecker) Check(ctx context.Context, config *HealthCheckConfig) *HealthCheckResult {
	start := time.Now()
	result := &HealthCheckResult{
		CheckType: CheckTypeProcess,
		Timestamp: start,
		Status:    StatusChecking,
	}

	if config.ProcessCheck == nil {
		result.Status = StatusUnhealthy
		result.Error = "Process check configuration is missing"
		result.Duration = time.Since(start)
		return result
	}

	processCheck := config.ProcessCheck

	// 检查进程是否存在
	if processCheck.PID > 0 {
		if !pc.isProcessRunning(processCheck.PID) {
			result.Status = StatusUnhealthy
			result.Error = fmt.Sprintf("process with PID %d is not running", processCheck.PID)
			result.Duration = time.Since(start)
			return result
		}
	}

	result.Status = StatusHealthy
	result.Message = "Process is running"
	result.Duration = time.Since(start)
	return result
}

// isProcessRunning 检查进程是否运行
func (pc *ProcessHealthChecker) isProcessRunning(pid int) bool {
	// 这里应该根据操作系统实现不同的逻辑
	// 简化实现：尝试向进程发送信号 0
	// 在实际项目中应该使用更可靠的方法
	return true // 简化实现
}

// GetType 返回检查器类型
func (pc *ProcessHealthChecker) GetType() CheckType {
	return CheckTypeProcess
}

// HealthCheckManager 健康检查管理器
type HealthCheckManager struct {
	checkers map[CheckType]HealthChecker
}

// NewHealthCheckManager 创建健康检查管理器
func NewHealthCheckManager() *HealthCheckManager {
	return &HealthCheckManager{
		checkers: map[CheckType]HealthChecker{
			CheckTypeHTTP:    &HTTPHealthChecker{},
			CheckTypeTCP:     &TCPHealthChecker{},
			CheckTypeProcess: &ProcessHealthChecker{},
		},
	}
}

// RegisterChecker 注册自定义检查器
func (hm *HealthCheckManager) RegisterChecker(checker HealthChecker) {
	hm.checkers[checker.GetType()] = checker
}

// PerformCheck 执行健康检查
func (hm *HealthCheckManager) PerformCheck(ctx context.Context, config *HealthCheckConfig) *HealthCheckResult {
	if !config.Enabled {
		return &HealthCheckResult{
			Status:    StatusUnknown,
			CheckType: config.Type,
			Timestamp: time.Now(),
			Message:   "Health check is disabled",
		}
	}

	checker, exists := hm.checkers[config.Type]
	if !exists {
		return &HealthCheckResult{
			Status:    StatusUnhealthy,
			CheckType: config.Type,
			Timestamp: time.Now(),
			Error:     fmt.Sprintf("unsupported check type: %s", config.Type),
		}
	}

	// 设置超时上下文
	if config.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, config.Timeout)
		defer cancel()
	}

	return checker.Check(ctx, config)
}

// DefaultHealthCheckConfig 返回默认健康检查配置
func DefaultHealthCheckConfig() *HealthCheckConfig {
	return &HealthCheckConfig{
		Enabled:          true,
		Type:             CheckTypeHTTP,
		Interval:         common.DefaultHealthCheckInterval,
		Timeout:          common.DefaultHealthCheckTimeout,
		FailureThreshold: common.DefaultFailureThreshold,
		SuccessThreshold: common.DefaultSuccessThreshold,
	}
}

// DefaultHTTPCheckConfig 返回默认 HTTP 检查配置
func DefaultHTTPCheckConfig(url string) *HealthCheckConfig {
	config := DefaultHealthCheckConfig()
	config.Type = CheckTypeHTTP
	config.HTTPCheck = &HTTPCheck{
		URL:             url,
		Method:          "GET",
		Headers:         make(map[string]string),
		ExpectedStatus:  common.DefaultHTTPExpectedStatus,
		FollowRedirects: true,
	}
	return config
}

// DefaultTCPCheckConfig 返回默认 TCP 检查配置
func DefaultTCPCheckConfig(address string, port int) *HealthCheckConfig {
	config := DefaultHealthCheckConfig()
	config.Type = CheckTypeTCP
	config.TCPCheck = &TCPCheck{
		Address: address,
		Port:    port,
		Timeout: common.DefaultTCPTimeout,
	}
	return config
}

// DefaultProcessCheckConfig 返回默认进程检查配置
func DefaultProcessCheckConfig(pid int) *HealthCheckConfig {
	config := DefaultHealthCheckConfig()
	config.Type = CheckTypeProcess
	config.ProcessCheck = &ProcessCheck{
		PID: pid,
	}
	return config
}
