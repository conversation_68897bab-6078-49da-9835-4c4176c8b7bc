package parser

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"a8.tools/backend/modules/proxy/types"
)

// VMess configuration structure
type vmessConfig struct {
	V    string      `json:"v"`
	PS   string      `json:"ps"`
	Add  string      `json:"add"`
	Port interface{} `json:"port"` // Can be int or string
	ID   string      `json:"id"`
	Aid  interface{} `json:"aid"` // Can be int or string
	Net  string      `json:"net"`
	Type string      `json:"type"`
	Host string      `json:"host"`
	Path string      `json:"path"`
	TLS  string      `json:"tls"`
}

// parseVMess parses VMess subscription content
func parseVMess(content []byte) ([]types.Node, error) {
	contentStr := string(content)

	// Try to decode base64 if needed
	decodedContent := tryBase64Decode(contentStr)
	if decodedContent != contentStr {
		contentStr = decodedContent
	}

	// Split by newline to get individual server entries
	lines := strings.Split(contentStr, "\n")

	var nodes []types.Node

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if !strings.HasPrefix(line, "vmess://") {
			continue
		}

		// Remove the vmess:// prefix
		encodedStr := line[8:]

		// Decode the base64 string
		decodedBytes, err := base64.StdEncoding.DecodeString(encodedStr)
		if err != nil {
			// Try with padding
			if len(encodedStr)%4 != 0 {
				encodedStr = encodedStr + strings.Repeat("=", 4-len(encodedStr)%4)
				decodedBytes, err = base64.StdEncoding.DecodeString(encodedStr)
				if err != nil {
					continue
				}
			} else {
				continue
			}
		}

		// Parse the JSON configuration
		var config vmessConfig
		err = json.Unmarshal(decodedBytes, &config)
		if err != nil {
			continue
		}

		// Handle port which can be int or string
		var port int
		switch p := config.Port.(type) {
		case float64:
			port = int(p)
		case int:
			port = p
		case string:
			portVal, err := strconv.Atoi(p)
			if err != nil {
				fmt.Printf("Error converting port to int: %v\n", err)
				continue
			}
			port = portVal
		default:
			fmt.Printf("Unknown port type: %T\n", config.Port)
			continue
		}

		// Handle alterID which can be int or string
		var alterID int
		switch aid := config.Aid.(type) {
		case float64:
			alterID = int(aid)
		case int:
			alterID = aid
		case string:
			aidVal, err := strconv.Atoi(aid)
			if err != nil {
				// Default to 0 if conversion fails
				alterID = 0
			} else {
				alterID = aidVal
			}
		default:
			// Default to 0 if type is unknown
			alterID = 0
		}

		// Create a new node
		node := types.Node{
			Type:    "vmess",
			Name:    config.PS,
			Server:  config.Add,
			Port:    port,
			UUID:    config.ID,
			AlterID: alterID,
			Network: config.Net,
			Path:    config.Path,
			Host:    config.Host,
			TLS:     config.TLS == "tls",
			Method:  "auto", // Default encryption method for VMess
			Extra:   make(map[string]string),
		}

		// Add additional parameters
		if config.Type != "" {
			node.Extra["type"] = config.Type
		}

		nodes = append(nodes, node)
	}

	return nodes, nil
}
