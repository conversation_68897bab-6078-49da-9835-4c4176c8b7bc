package parser

import (
	"encoding/base64"
	"strings"
	"testing"

	"a8.tools/backend/modules/proxy/types"
)

// TestDetectSubscriptionType 测试订阅类型检测
func TestDetectSubscriptionType(t *testing.T) {
	tests := []struct {
		name     string
		content  string
		expected types.SubscriptionType
	}{
		{
			name:     "Clash configuration",
			content:  "proxies:\n  - name: test\n    server: example.com",
			expected: types.TypeClash,
		},
		{
			name:     "VMess URL",
			content:  "vmess://eyJ2IjoiMiIsInBzIjoidGVzdCIsImFkZCI6ImV4YW1wbGUuY29tIiwicG9ydCI6IjQ0MyIsInR5cGUiOiJub25lIiwiaWQiOiIxMjM0NTY3OCIsImFpZCI6IjAiLCJuZXQiOiJ3cyIsInBhdGgiOiIvIiwiaG9zdCI6IiIsInRscyI6InRscyJ9",
			expected: types.TypeVMess,
		},
		{
			name:     "SSR URL",
			content:  "ssr://ZXhhbXBsZS5jb206ODM4ODphdXRoX2FlczEyOF9tZDU6YWVzLTI1Ni1jZmI6dGxzMS4yX3RpY2tldF9hdXRoOmNHRnpjM2R2Y21RLz9vYmZzcGFyYW09JnByb3RvcGFyYW09JnJlbWFya3M9ZEdWemRBJmdyb3VwPQ",
			expected: types.TypeSSR,
		},
		{
			name:     "Shadowsocks URL",
			content:  "ss://<EMAIL>:8388#test",
			expected: types.TypeShadowsocks,
		},
		{
			name:     "Mixed content",
			content:  "ss://<EMAIL>:8388#test\nvmess://eyJ2IjoiMiJ9",
			expected: types.TypeMixed,
		},
		{
			name:     "Base64 encoded content",
			content:  base64.StdEncoding.EncodeToString([]byte("ss://<EMAIL>:8388#test")),
			expected: types.TypeShadowsocks,
		},
		{
			name:     "Unknown content",
			content:  "unknown content",
			expected: types.TypeUnknown,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := detectSubscriptionType([]byte(tt.content))
			if result != tt.expected {
				t.Errorf("detectSubscriptionType() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestTryBase64Decode 测试base64解码功能
func TestTryBase64Decode(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Valid base64",
			input:    base64.StdEncoding.EncodeToString([]byte("hello world")),
			expected: "hello world",
		},
		{
			name:     "URL-safe base64",
			input:    base64.URLEncoding.EncodeToString([]byte("hello world")),
			expected: "hello world",
		},
		{
			name:     "Base64 with padding needed",
			input:    strings.TrimRight(base64.StdEncoding.EncodeToString([]byte("hello world")), "="),
			expected: "hello world",
		},
		{
			name:     "Invalid base64",
			input:    "invalid base64!@#",
			expected: "invalid base64!@#",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Whitespace handling",
			input:    "  " + base64.StdEncoding.EncodeToString([]byte("test")) + "  ",
			expected: "test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tryBase64Decode(tt.input)
			if result != tt.expected {
				t.Errorf("tryBase64Decode() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestParseShadowsocks 测试Shadowsocks解析
func TestParseShadowsocks(t *testing.T) {
	tests := []struct {
		name      string
		content   string
		expectErr bool
		expected  int // 期望的节点数量
	}{
		{
			name:      "Valid SS URL",
			content:   "ss://<EMAIL>:8388#test",
			expectErr: false,
			expected:  1,
		},
		{
			name: "Multiple SS URLs",
			content: `ss://<EMAIL>:8388#test1
ss://<EMAIL>:8388#test2`,
			expectErr: false,
			expected:  2,
		},
		{
			name:      "Base64 encoded SS content",
			content:   base64.StdEncoding.EncodeToString([]byte("ss://<EMAIL>:8388#test")),
			expectErr: false,
			expected:  1,
		},
		{
			name:      "Empty content",
			content:   "",
			expectErr: false,
			expected:  0,
		},
		{
			name:      "Invalid SS URL",
			content:   "ss://invalid",
			expectErr: false,
			expected:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodes, err := parseShadowsocks([]byte(tt.content))

			if tt.expectErr && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if len(nodes) != tt.expected {
				t.Errorf("Expected %d nodes, got %d", tt.expected, len(nodes))
			}
		})
	}
}

// TestParseVMess 测试VMess解析
func TestParseVMess(t *testing.T) {
	tests := []struct {
		name      string
		content   string
		expectErr bool
		expected  int
	}{
		{
			name:      "Valid VMess URL",
			content:   "vmess://eyJ2IjoiMiIsInBzIjoidGVzdCIsImFkZCI6ImV4YW1wbGUuY29tIiwicG9ydCI6IjQ0MyIsInR5cGUiOiJub25lIiwiaWQiOiIxMjM0NTY3OCIsImFpZCI6IjAiLCJuZXQiOiJ3cyIsInBhdGgiOiIvIiwiaG9zdCI6IiIsInRscyI6InRscyJ9",
			expectErr: false,
			expected:  1,
		},
		{
			name:      "Empty content",
			content:   "",
			expectErr: false,
			expected:  0,
		},
		{
			name:      "Invalid VMess URL",
			content:   "vmess://invalid",
			expectErr: false,
			expected:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodes, err := parseVMess([]byte(tt.content))

			if tt.expectErr && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if len(nodes) != tt.expected {
				t.Errorf("Expected %d nodes, got %d", tt.expected, len(nodes))
			}
		})
	}
}

// TestParseSSR 测试SSR解析
func TestParseSSR(t *testing.T) {
	tests := []struct {
		name      string
		content   string
		expectErr bool
		expected  int
	}{
		{
			name:      "Valid SSR URL",
			content:   "ssr://ZXhhbXBsZS5jb206ODM4ODphdXRoX2FlczEyOF9tZDU6YWVzLTI1Ni1jZmI6dGxzMS4yX3RpY2tldF9hdXRoOmNHRnpjM2R2Y21RLz9vYmZzcGFyYW09JnByb3RvcGFyYW09JnJlbWFya3M9ZEdWemRBJmdyb3VwPQ",
			expectErr: false,
			expected:  1,
		},
		{
			name:      "Empty content",
			content:   "",
			expectErr: false,
			expected:  0,
		},
		{
			name:      "Invalid SSR URL",
			content:   "ssr://invalid",
			expectErr: false,
			expected:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodes, err := parseSSR([]byte(tt.content))

			if tt.expectErr && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if len(nodes) != tt.expected {
				t.Errorf("Expected %d nodes, got %d", tt.expected, len(nodes))
			}
		})
	}
}

// TestParseClash 测试Clash解析
func TestParseClash(t *testing.T) {
	tests := []struct {
		name      string
		content   string
		expectErr bool
		expected  int
	}{
		{
			name: "Valid Clash config",
			content: `proxies:
  - name: "test"
    type: ss
    server: example.com
    port: 8388
    cipher: aes-256-gcm
    password: "password"`,
			expectErr: false,
			expected:  1,
		},
		{
			name:      "Empty content",
			content:   "",
			expectErr: false,
			expected:  0,
		},
		{
			name:      "Invalid Clash config",
			content:   "invalid yaml content",
			expectErr: true,
			expected:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			nodes, err := parseClash([]byte(tt.content))

			if tt.expectErr && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if len(nodes) != tt.expected {
				t.Errorf("Expected %d nodes, got %d", tt.expected, len(nodes))
			}
		})
	}
}

// TestParseSubscription 测试主解析函数
func TestParseSubscription(t *testing.T) {
	tests := []struct {
		name         string
		content      string
		expectErr    bool
		expectedType types.SubscriptionType
		expectedNum  int
	}{
		{
			name:         "Shadowsocks subscription",
			content:      "ss://<EMAIL>:8388#test",
			expectErr:    false,
			expectedType: types.TypeShadowsocks,
			expectedNum:  1,
		},
		{
			name:         "VMess subscription",
			content:      "vmess://eyJ2IjoiMiIsInBzIjoidGVzdCIsImFkZCI6ImV4YW1wbGUuY29tIiwicG9ydCI6IjQ0MyIsInR5cGUiOiJub25lIiwiaWQiOiIxMjM0NTY3OCIsImFpZCI6IjAiLCJuZXQiOiJ3cyIsInBhdGgiOiIvIiwiaG9zdCI6IiIsInRscyI6InRscyJ9",
			expectErr:    false,
			expectedType: types.TypeVMess,
			expectedNum:  1,
		},
		{
			name: "Mixed subscription",
			content: `ss://<EMAIL>:8388#test
vmess://eyJ2IjoiMiJ9`,
			expectErr:    false,
			expectedType: types.TypeMixed,
			expectedNum:  1, // 只有一个有效的SS节点
		},
		{
			name: "Clash subscription",
			content: `proxies:
  - name: "test"
    type: ss
    server: example.com
    port: 8388
    cipher: aes-256-gcm
    password: "password"`,
			expectErr:    false,
			expectedType: types.TypeClash,
			expectedNum:  1,
		},
		{
			name:         "Unknown subscription",
			content:      "unknown content",
			expectErr:    true,
			expectedType: types.TypeUnknown,
			expectedNum:  0,
		},
		{
			name:         "Empty content",
			content:      "",
			expectErr:    true,
			expectedType: types.TypeUnknown,
			expectedNum:  0,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			subType, nodes, err := ParseSubscription([]byte(tt.content))

			if tt.expectErr && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectErr && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			if subType != tt.expectedType {
				t.Errorf("Expected type %v, got %v", tt.expectedType, subType)
			}

			if len(nodes) != tt.expectedNum {
				t.Errorf("Expected %d nodes, got %d", tt.expectedNum, len(nodes))
			}
		})
	}
}

// TestParseSubscriptionWithRealData 测试真实数据解析
func TestParseSubscriptionWithRealData(t *testing.T) {
	// 这里可以添加一些真实的订阅数据测试
	t.Run("Real Shadowsocks URL", func(t *testing.T) {
		// 真实的Shadowsocks URL格式
		content := "ss://YWVzLTI1Ni1nY206cGFzc3dvcmQ@***********:8388#My%20Server"

		subType, nodes, err := ParseSubscription([]byte(content))
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if subType != types.TypeShadowsocks {
			t.Errorf("Expected Shadowsocks type, got %v", subType)
		}

		if len(nodes) != 1 {
			t.Errorf("Expected 1 node, got %d", len(nodes))
		}

		if len(nodes) > 0 {
			node := nodes[0]
			if node.Type != "shadowsocks" {
				t.Errorf("Expected node type 'shadowsocks', got '%s'", node.Type)
			}
			if node.Server != "***********" {
				t.Errorf("Expected server '***********', got '%s'", node.Server)
			}
			if node.Port != 8388 {
				t.Errorf("Expected port 8388, got %d", node.Port)
			}
		}
	})
}

// BenchmarkParseSubscription 基准测试订阅解析
func BenchmarkParseSubscription(b *testing.B) {
	content := []byte("ss://<EMAIL>:8388#test")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _, err := ParseSubscription(content)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkDetectSubscriptionType 基准测试类型检测
func BenchmarkDetectSubscriptionType(b *testing.B) {
	content := []byte("ss://<EMAIL>:8388#test")

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = detectSubscriptionType(content)
	}
}

// BenchmarkTryBase64Decode 基准测试base64解码
func BenchmarkTryBase64Decode(b *testing.B) {
	encoded := base64.StdEncoding.EncodeToString([]byte("hello world"))

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = tryBase64Decode(encoded)
	}
}
