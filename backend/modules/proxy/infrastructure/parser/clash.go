package parser

import (
	"strconv"

	"a8.tools/backend/modules/proxy/types"

	"gopkg.in/yaml.v3"
)

// ClashConfig represents the Clash configuration structure
type ClashConfig struct {
	Proxies []ClashProxy `yaml:"proxies"`
}

// ClashProxy represents a proxy in Clash configuration
type ClashProxy struct {
	Name          string                 `yaml:"name"`
	Type          string                 `yaml:"type"`
	Server        string                 `yaml:"server"`
	Port          interface{}            `yaml:"port"` // Can be int or string
	Password      string                 `yaml:"password"`
	Cipher        string                 `yaml:"cipher"`
	UUID          string                 `yaml:"uuid"`
	AlterID       interface{}            `yaml:"alterId"` // Can be int or string
	TLS           bool                   `yaml:"tls"`
	Network       string                 `yaml:"network"`
	WSPath        string                 `yaml:"ws-path"`
	WSHeaders     map[string]string      `yaml:"ws-headers"`
	Plugin        string                 `yaml:"plugin"`
	PluginOpts    map[string]interface{} `yaml:"plugin-opts"`
	Protocol      string                 `yaml:"protocol"`
	ProtocolParam string                 `yaml:"protocol-param"` // Changed to string
	Obfs          string                 `yaml:"obfs"`
	ObfsParam     string                 `yaml:"obfs-param"` // Changed to string
}

// parseClash parses Clash subscription content
func parseClash(content []byte) ([]types.Node, error) {
	var config ClashConfig
	err := yaml.Unmarshal(content, &config)
	if err != nil {
		return nil, err
	}

	var nodes []types.Node

	for _, proxy := range config.Proxies {
		node := types.Node{
			Type:     proxy.Type,
			Name:     proxy.Name,
			Server:   proxy.Server,
			Password: proxy.Password,
			Method:   proxy.Cipher,
			Extra:    make(map[string]string),
		}

		// Handle port which can be int or string
		switch p := proxy.Port.(type) {
		case int:
			node.Port = p
		case float64:
			node.Port = int(p)
		case string:
			port, err := strconv.Atoi(p)
			if err == nil {
				node.Port = port
			}
		}

		// Handle different proxy types
		switch proxy.Type {
		case "ss", "shadowsocks":
			// Handle Shadowsocks plugin if present
			if proxy.Plugin != "" {
				node.Extra["plugin"] = proxy.Plugin
				for k, v := range proxy.PluginOpts {
					switch val := v.(type) {
					case string:
						node.Extra["plugin_"+k] = val
					case bool:
						node.Extra["plugin_"+k] = strconv.FormatBool(val)
					case int:
						node.Extra["plugin_"+k] = strconv.Itoa(val)
					case float64:
						node.Extra["plugin_"+k] = strconv.FormatFloat(val, 'f', -1, 64)
					}
				}
			}

		case "ssr":
			node.Protocol = proxy.Protocol
			node.Obfs = proxy.Obfs

			// Handle protocol param
			if proxy.ProtocolParam != "" {
				node.ProtocolArg = proxy.ProtocolParam
			}

			// Handle obfs param
			if proxy.ObfsParam != "" {
				node.ObfsArg = proxy.ObfsParam
			}

		case "vmess":
			node.UUID = proxy.UUID

			// Handle alterID which can be int or string
			switch aid := proxy.AlterID.(type) {
			case int:
				node.AlterID = aid
			case float64:
				node.AlterID = int(aid)
			case string:
				alterID, err := strconv.Atoi(aid)
				if err == nil {
					node.AlterID = alterID
				}
			}

			node.Network = proxy.Network
			node.TLS = proxy.TLS

			// Handle WebSocket settings
			if proxy.Network == "ws" {
				node.Path = proxy.WSPath
				if host, ok := proxy.WSHeaders["Host"]; ok {
					node.Host = host
				}
			}
		}

		nodes = append(nodes, node)
	}

	return nodes, nil
}
