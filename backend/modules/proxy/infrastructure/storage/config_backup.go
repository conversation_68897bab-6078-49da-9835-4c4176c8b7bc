package storage

import (
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"a8.tools/backend/modules/proxy/core"
)

// ConfigBackupInfo represents information about a configuration backup
type ConfigBackupInfo struct {
	ID           string                 `json:"id"`
	OriginalID   string                 `json:"original_id"`
	Name         string                 `json:"name"`
	FilePath     string                 `json:"file_path"`
	BackupPath   string                 `json:"backup_path"`
	CreatedAt    time.Time              `json:"created_at"`
	Size         int64                  `json:"size"`
	Checksum     string                 `json:"checksum"`
	Format       core.ConfigFormat      `json:"format"`
	Description  string                 `json:"description,omitempty"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	IsAutomatic  bool                   `json:"is_automatic"`  // Whether backup was created automatically
	RestoreCount int                    `json:"restore_count"` // Number of times this backup has been restored
}

// ConfigBackupOptions represents options for creating configuration backups
type ConfigBackupOptions struct {
	Description string                 `json:"description,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	IsAutomatic bool                   `json:"is_automatic"`
	Compress    bool                   `json:"compress"` // Whether to compress the backup
	Validate    bool                   `json:"validate"` // Whether to validate before backup
}

// ConfigRestoreOptions represents options for restoring from configuration backups
type ConfigRestoreOptions struct {
	Overwrite    bool   `json:"overwrite"`          // Whether to overwrite existing config
	CreateBackup bool   `json:"create_backup"`      // Whether to backup current config before restore
	Validate     bool   `json:"validate"`           // Whether to validate restored config
	SetActive    bool   `json:"set_active"`         // Whether to set restored config as active
	NewID        string `json:"new_id,omitempty"`   // New ID for restored config (if not overwriting)
	NewName      string `json:"new_name,omitempty"` // New name for restored config
}

// ConfigBackupListOptions represents options for listing backups
type ConfigBackupListOptions struct {
	OriginalID    string     `json:"original_id,omitempty"`    // Filter by original config ID
	CreatedAfter  *time.Time `json:"created_after,omitempty"`  // Filter by creation time
	CreatedBefore *time.Time `json:"created_before,omitempty"` // Filter by creation time
	IsAutomatic   *bool      `json:"is_automatic,omitempty"`   // Filter by backup type
	SortBy        string     `json:"sort_by,omitempty"`        // Sort field: "created_at", "size", "name"
	SortDesc      bool       `json:"sort_desc"`                // Sort in descending order
	Limit         int        `json:"limit,omitempty"`          // Maximum number of results
	Offset        int        `json:"offset,omitempty"`         // Offset for pagination
}

// ConfigBackupManager manages configuration backups
type ConfigBackupManager struct {
	backupDir string
	backups   map[string]*ConfigBackupInfo
}

// NewConfigBackupManager creates a new configuration backup manager
func NewConfigBackupManager(backupDir string) (*ConfigBackupManager, error) {
	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create backup directory: %w", err)
	}

	return &ConfigBackupManager{
		backupDir: backupDir,
		backups:   make(map[string]*ConfigBackupInfo),
	}, nil
}

// DefaultConfigBackupOptions returns default backup options
func DefaultConfigBackupOptions() *ConfigBackupOptions {
	return &ConfigBackupOptions{
		IsAutomatic: false,
		Compress:    false,
		Validate:    true,
	}
}

// DefaultConfigRestoreOptions returns default restore options
func DefaultConfigRestoreOptions() *ConfigRestoreOptions {
	return &ConfigRestoreOptions{
		Overwrite:    false,
		CreateBackup: true,
		Validate:     true,
		SetActive:    false,
	}
}

// CreateConfigBackup creates a backup of the specified configuration
func (cbm *ConfigBackupManager) CreateConfigBackup(metadata *core.ConfigFileMetadata, options *ConfigBackupOptions) (*ConfigBackupInfo, error) {
	if options == nil {
		options = DefaultConfigBackupOptions()
	}

	// Generate backup ID and path
	backupID := fmt.Sprintf("%s_%d", metadata.ID, time.Now().Unix())
	backupFileName := fmt.Sprintf("backup_%s.json", backupID)
	backupPath := filepath.Join(cbm.backupDir, backupFileName)

	// Copy the original file to backup location
	if err := cbm.copyFile(metadata.FilePath, backupPath); err != nil {
		return nil, fmt.Errorf("failed to copy config file to backup location: %w", err)
	}

	// Get file info
	stat, err := os.Stat(backupPath)
	if err != nil {
		return nil, fmt.Errorf("failed to get backup file info: %w", err)
	}

	// Create backup info
	backupInfo := &ConfigBackupInfo{
		ID:           backupID,
		OriginalID:   metadata.ID,
		Name:         backupFileName,
		FilePath:     metadata.FilePath,
		BackupPath:   backupPath,
		CreatedAt:    time.Now(),
		Size:         stat.Size(),
		Checksum:     metadata.Checksum,
		Format:       metadata.Format,
		Description:  options.Description,
		Metadata:     options.Metadata,
		IsAutomatic:  options.IsAutomatic,
		RestoreCount: 0,
	}

	// Store backup info
	cbm.backups[backupID] = backupInfo

	// Save backup metadata
	if err := cbm.saveBackupMetadata(backupInfo); err != nil {
		return nil, fmt.Errorf("failed to save backup metadata: %w", err)
	}

	return backupInfo, nil
}

// RestoreConfigBackup restores a configuration from backup
func (cbm *ConfigBackupManager) RestoreConfigBackup(backupID string, options *ConfigRestoreOptions) error {
	if options == nil {
		options = DefaultConfigRestoreOptions()
	}

	// Get backup info
	backupInfo, exists := cbm.backups[backupID]
	if !exists {
		return fmt.Errorf("backup with ID %s not found", backupID)
	}

	// Check if backup file exists
	if _, err := os.Stat(backupInfo.BackupPath); os.IsNotExist(err) {
		return fmt.Errorf("backup file does not exist: %s", backupInfo.BackupPath)
	}

	// Determine target path
	targetPath := backupInfo.FilePath
	if options.NewID != "" {
		dir := filepath.Dir(targetPath)
		ext := filepath.Ext(targetPath)
		targetPath = filepath.Join(dir, fmt.Sprintf("%s%s", options.NewID, ext))
	}

	// Create backup of current config if requested
	if options.CreateBackup && options.Overwrite {
		if _, err := os.Stat(targetPath); err == nil {
			backupOptions := &ConfigBackupOptions{
				Description: "Auto-backup before restore",
				IsAutomatic: true,
			}
			// Note: This would need the original metadata, simplified for now
			_ = backupOptions
		}
	}

	// Copy backup file to target location
	if err := cbm.copyFile(backupInfo.BackupPath, targetPath); err != nil {
		return fmt.Errorf("failed to restore config from backup: %w", err)
	}

	// Update restore count
	backupInfo.RestoreCount++
	cbm.saveBackupMetadata(backupInfo)

	return nil
}

// ListConfigBackups lists configuration backups with optional filtering
func (cbm *ConfigBackupManager) ListConfigBackups(options *ConfigBackupListOptions) ([]*ConfigBackupInfo, error) {
	var result []*ConfigBackupInfo

	for _, backup := range cbm.backups {
		// Apply filters
		if options != nil {
			if options.OriginalID != "" && backup.OriginalID != options.OriginalID {
				continue
			}
			if options.CreatedAfter != nil && backup.CreatedAt.Before(*options.CreatedAfter) {
				continue
			}
			if options.CreatedBefore != nil && backup.CreatedAt.After(*options.CreatedBefore) {
				continue
			}
			if options.IsAutomatic != nil && backup.IsAutomatic != *options.IsAutomatic {
				continue
			}
		}

		result = append(result, backup)
	}

	// Apply sorting and pagination
	if options != nil {
		result = cbm.sortBackups(result, options.SortBy, options.SortDesc)
		result = cbm.paginateBackups(result, options.Offset, options.Limit)
	}

	return result, nil
}

// DeleteConfigBackup deletes a configuration backup
func (cbm *ConfigBackupManager) DeleteConfigBackup(backupID string) error {
	backupInfo, exists := cbm.backups[backupID]
	if !exists {
		return fmt.Errorf("backup with ID %s not found", backupID)
	}

	// Delete backup file
	if err := os.Remove(backupInfo.BackupPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete backup file: %w", err)
	}

	// Delete metadata file
	metadataPath := cbm.getBackupMetadataPath(backupID)
	if err := os.Remove(metadataPath); err != nil && !os.IsNotExist(err) {
		return fmt.Errorf("failed to delete backup metadata: %w", err)
	}

	// Remove from memory
	delete(cbm.backups, backupID)

	return nil
}

// GetConfigBackup gets backup information by ID
func (cbm *ConfigBackupManager) GetConfigBackup(backupID string) (*ConfigBackupInfo, error) {
	backupInfo, exists := cbm.backups[backupID]
	if !exists {
		return nil, fmt.Errorf("backup with ID %s not found", backupID)
	}
	return backupInfo, nil
}

// copyFile copies a file from source to destination
func (cbm *ConfigBackupManager) copyFile(src, dst string) error {
	sourceData, err := os.ReadFile(src)
	if err != nil {
		return err
	}

	return os.WriteFile(dst, sourceData, 0644)
}

// saveBackupMetadata saves backup metadata to disk
func (cbm *ConfigBackupManager) saveBackupMetadata(backup *ConfigBackupInfo) error {
	metadataPath := cbm.getBackupMetadataPath(backup.ID)

	data, err := json.MarshalIndent(backup, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(metadataPath, data, 0644)
}

// getBackupMetadataPath returns the path for backup metadata file
func (cbm *ConfigBackupManager) getBackupMetadataPath(backupID string) string {
	return filepath.Join(cbm.backupDir, fmt.Sprintf("backup_%s.meta", backupID))
}

// sortBackups sorts backups based on specified criteria
func (cbm *ConfigBackupManager) sortBackups(backups []*ConfigBackupInfo, sortBy string, sortDesc bool) []*ConfigBackupInfo {
	// Implementation for sorting would go here
	// For now, just return as-is
	return backups
}

// paginateBackups applies pagination to backup list
func (cbm *ConfigBackupManager) paginateBackups(backups []*ConfigBackupInfo, offset, limit int) []*ConfigBackupInfo {
	if offset >= len(backups) {
		return []*ConfigBackupInfo{}
	}

	end := offset + limit
	if limit <= 0 || end > len(backups) {
		end = len(backups)
	}

	return backups[offset:end]
}
