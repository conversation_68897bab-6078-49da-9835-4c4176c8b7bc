package storage

import (
	"a8.tools/backend/modules/proxy/core"
	"a8.tools/backend/modules/proxy/types"
)

// SubscriptionStorageAdapter 适配器，让 SubscriptionStorage 实现 core.SubscriptionStorage 接口
type SubscriptionStorageAdapter struct {
	storage *SubscriptionStorage
}

// NewSubscriptionStorageAdapter 创建存储适配器
func NewSubscriptionStorageAdapter(dataDir string) (*SubscriptionStorageAdapter, error) {
	storage, err := NewSubscriptionStorage(dataDir)
	if err != nil {
		return nil, err
	}

	return &SubscriptionStorageAdapter{
		storage: storage,
	}, nil
}

// Add 添加订阅数据，适配接口
func (a *SubscriptionStorageAdapter) Add(subscription core.SubscriptionData) error {
	// 转换数据结构
	storageData := a.convertCoreToStorage(subscription)
	return a.storage.Add(storageData)
}

// GetByID 根据ID获取订阅数据，适配接口
func (a *SubscriptionStorageAdapter) GetByID(id string) (*core.SubscriptionData, bool) {
	storageData, exists := a.storage.GetByID(id)
	if !exists {
		return nil, false
	}

	// 转换数据结构
	coreData := a.convertStorageToCore(*storageData)
	return &coreData, true
}

// GetAll 获取所有订阅数据，适配接口
func (a *SubscriptionStorageAdapter) GetAll() []core.SubscriptionData {
	storageDataList := a.storage.GetAll()
	coreDataList := make([]core.SubscriptionData, len(storageDataList))

	for i, storageData := range storageDataList {
		coreDataList[i] = a.convertStorageToCore(storageData)
	}

	return coreDataList
}

// Update 更新订阅数据，适配接口
func (a *SubscriptionStorageAdapter) Update(subscription core.SubscriptionData) error {
	// 转换数据结构
	storageData := a.convertCoreToStorage(subscription)
	return a.storage.Update(storageData)
}

// Delete 删除订阅数据，适配接口
func (a *SubscriptionStorageAdapter) Delete(id string) error {
	return a.storage.Delete(id)
}

// convertCoreToStorage 将 core.SubscriptionData 转换为 storage.SubscriptionData
func (a *SubscriptionStorageAdapter) convertCoreToStorage(coreData core.SubscriptionData) SubscriptionData {
	// 转换节点数据
	storageNodes := make([]NodeData, len(coreData.Nodes))
	for i, coreNode := range coreData.Nodes {
		storageNodes[i] = NodeData{
			ID:     coreNode.ID,
			Name:   coreNode.Name,
			Server: coreNode.Server,
			Port:   coreNode.Port,
			Type:   coreNode.Type,
		}
	}

	return SubscriptionData{
		ID:          coreData.ID,
		Name:        coreData.Name,
		URL:         coreData.URL,
		Type:        types.SubscriptionType(coreData.Type),
		Status:      string(coreData.Status),
		Description: coreData.Description,
		CreatedAt:   coreData.CreatedAt,
		UpdatedAt:   coreData.UpdatedAt,
		UpdatedBy:   coreData.UpdatedBy,
		Nodes:       storageNodes,
		NodeCount:   coreData.NodeCount,
	}
}

// convertStorageToCore 将 storage.SubscriptionData 转换为 core.SubscriptionData
func (a *SubscriptionStorageAdapter) convertStorageToCore(storageData SubscriptionData) core.SubscriptionData {
	// 转换节点数据
	coreNodes := make([]core.NodeData, len(storageData.Nodes))
	for i, storageNode := range storageData.Nodes {
		coreNodes[i] = core.NodeData{
			ID:       storageNode.ID,
			Name:     storageNode.Name,
			Server:   storageNode.Server,
			Port:     storageNode.Port,
			Type:     storageNode.Type,
			Method:   storageNode.RawData.Method,
			Password: storageNode.RawData.Password,
			Protocol: storageNode.RawData.Protocol,
		}
	}

	return core.SubscriptionData{
		ID:          storageData.ID,
		Name:        storageData.Name,
		URL:         storageData.URL,
		Type:        string(storageData.Type),
		Status:      core.SubscriptionStatus(storageData.Status),
		Description: storageData.Description,
		CreatedAt:   storageData.CreatedAt,
		UpdatedAt:   storageData.UpdatedAt,
		UpdatedBy:   storageData.UpdatedBy,
		Nodes:       coreNodes,
		NodeCount:   storageData.NodeCount,
	}
}

// GetStorage 获取底层存储实例（用于直接访问存储功能）
func (a *SubscriptionStorageAdapter) GetStorage() *SubscriptionStorage {
	return a.storage
}
