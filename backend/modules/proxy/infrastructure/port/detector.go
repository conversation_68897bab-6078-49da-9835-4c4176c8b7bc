package port

import (
	"context"
	"fmt"
	"net"
	"sort"
	"time"
)

// NewPortAvailabilityChecker creates a new port availability checker
func NewPortAvailabilityChecker(timeout time.Duration) *PortAvailabilityChecker {
	return &PortAvailabilityChecker{
		timeout: timeout,
	}
}

// IsPortAvailable checks if a port is available for binding
func (c *PortAvailabilityChecker) IsPortAvailable(port int, address string) bool {
	addr := fmt.Sprintf("%s:%d", address, port)

	// Try to bind to the port
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		return false
	}

	// Close the listener immediately
	listener.Close()
	return true
}

// IsPortAvailableWithTimeout checks port availability with a timeout
func (c *PortAvailabilityChecker) IsPortAvailableWithTimeout(port int, address string) bool {
	ctx, cancel := context.WithTimeout(context.Background(), c.timeout)
	defer cancel()

	done := make(chan bool, 1)
	go func() {
		done <- c.IsPortAvailable(port, address)
	}()

	select {
	case available := <-done:
		return available
	case <-ctx.Done():
		return false
	}
}

// findAvailablePort finds an available port within the specified range
func (a *AdvancedPortAllocator) findAvailablePort(preferredRange *PortRange) (int, error) {
	var ports []int

	if preferredRange != nil {
		// Use preferred range
		for port := preferredRange.Start; port <= preferredRange.End; port++ {
			if a.portSet[port] && a.allocations[port] == nil {
				ports = append(ports, port)
			}
		}
	} else {
		// Use all available ports
		for port := range a.portSet {
			if a.allocations[port] == nil {
				ports = append(ports, port)
			}
		}
	}

	if len(ports) == 0 {
		return 0, fmt.Errorf("no available ports")
	}

	// Sort ports for consistent allocation
	sort.Ints(ports)

	// Try to find the first available port
	for _, port := range ports {
		return port, nil
	}

	return 0, fmt.Errorf("no available ports")
}

// checkPortAvailabilityWithRetries checks port availability with retry logic
func (a *AdvancedPortAllocator) checkPortAvailabilityWithRetries(port int, address string, timeout time.Duration) (bool, []*PortConflictInfo) {
	var conflicts []*PortConflictInfo

	for attempt := 0; attempt <= a.config.RetryAttempts; attempt++ {
		if a.availabilityChecker.IsPortAvailableWithTimeout(port, address) {
			return true, nil
		}

		// Record conflict
		conflict := &PortConflictInfo{
			Port:           port,
			ConflictCount:  1,
			LastConflictAt: time.Now(),
			ConflictType:   "bind_failed",
		}
		conflicts = append(conflicts, conflict)

		// Wait before retry
		if attempt < a.config.RetryAttempts {
			time.Sleep(a.config.RetryInterval)
		}
	}

	return false, conflicts
}

// recordPortConflict records a port conflict in the history
func (a *AdvancedPortAllocator) recordPortConflict(port int, conflictType string) {
	conflict, exists := a.conflictHistory[port]
	if !exists {
		conflict = &PortConflictInfo{
			Port:         port,
			ConflictType: conflictType,
		}
		a.conflictHistory[port] = conflict
	}

	conflict.ConflictCount++
	conflict.LastConflictAt = time.Now()
	a.stats.conflictCount++
}

// calculateRangeStats calculates statistics for a specific port range
func (a *AdvancedPortAllocator) calculateRangeStats(portRange PortRange) *RangeStats {
	stats := &RangeStats{
		Range: portRange,
	}

	// Count ports in this range
	for port := portRange.Start; port <= portRange.End; port++ {
		if a.portSet[port] {
			stats.TotalPorts++
			if a.allocations[port] != nil {
				stats.AllocatedPorts++
			}
		}
	}

	stats.AvailablePorts = stats.TotalPorts - stats.AllocatedPorts

	// Count allocations in this range
	for port, allocation := range a.allocations {
		if port >= portRange.Start && port <= portRange.End {
			if allocation != nil {
				stats.AllocationCount++
			}
		}
	}

	return stats
}
