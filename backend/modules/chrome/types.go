package chrome

import (
	"time"
)

// ProfileStatus represents the status of a Chrome profile
type ProfileStatus string

const (
	// ProfileStatusStopped indicates the profile is not running
	ProfileStatusStopped ProfileStatus = "stopped"
	// ProfileStatusRunning indicates the profile is currently running
	ProfileStatusRunning ProfileStatus = "running"
	// ProfileStatusStarting indicates the profile is in the process of starting
	ProfileStatusStarting ProfileStatus = "starting"
	// ProfileStatusStopping indicates the profile is in the process of stopping
	ProfileStatusStopping ProfileStatus = "stopping"
)

// Profile represents a Chrome profile configuration
type Profile struct {
	ID           string            `json:"id"`
	Name         string            `json:"name"`
	Description  string            `json:"description,omitempty"`
	IconPath     string            `json:"icon_path,omitempty"`
	UserDataDir  string            `json:"user_data_dir"`
	LaunchParams map[string]string `json:"launch_params,omitempty"`
	ProxyConfig  *ProxyConfig      `json:"proxy_config,omitempty"`
	WindowConfig *WindowConfig     `json:"window_config,omitempty"`
	CreatedAt    time.Time         `json:"created_at"`
	UpdatedAt    time.Time         `json:"updated_at"`
	LastUsed     *time.Time        `json:"last_used,omitempty"`
	Status       ProfileStatus     `json:"status"`
	ProcessID    int               `json:"process_id,omitempty"`
}

// ProxyConfig represents proxy configuration for a profile
type ProxyConfig struct {
	Type     string `json:"type"` // http, https, socks4, socks5
	Host     string `json:"host"`
	Port     int    `json:"port"`
	Username string `json:"username,omitempty"`
	Password string `json:"password,omitempty"`
}

// WindowConfig represents window configuration for a profile
type WindowConfig struct {
	Width      int  `json:"width,omitempty"`
	Height     int  `json:"height,omitempty"`
	X          int  `json:"x,omitempty"`
	Y          int  `json:"y,omitempty"`
	Maximized  bool `json:"maximized,omitempty"`
	Fullscreen bool `json:"fullscreen,omitempty"`
}

// LaunchOptions represents options for launching a Chrome instance
type LaunchOptions struct {
	Profile            *Profile          `json:"profile"`
	CustomArgs         []string          `json:"custom_args,omitempty"`
	Headless           bool              `json:"headless,omitempty"`
	DebugPort          int               `json:"debug_port,omitempty"`
	Environment        map[string]string `json:"environment,omitempty"`
	Timeout            time.Duration     `json:"timeout,omitempty"`
	NoSandbox          bool              `json:"no_sandbox,omitempty"`
	DisableWebSecurity bool              `json:"disable_web_security,omitempty"`
}

// ProcessInfo represents information about a running Chrome process
type ProcessInfo struct {
	PID         int           `json:"pid"`
	ProfileID   string        `json:"profile_id"`
	StartTime   time.Time     `json:"start_time"`
	Status      ProfileStatus `json:"status"`
	DebugPort   int           `json:"debug_port,omitempty"`
	UserDataDir string        `json:"user_data_dir"`
}

// ExportData represents exported profile data
type ExportData struct {
	Version    string            `json:"version"`
	Profile    *Profile          `json:"profile"`
	Metadata   map[string]string `json:"metadata,omitempty"`
	ExportedAt time.Time         `json:"exported_at"`
}

// ImportOptions represents options for importing a profile
type ImportOptions struct {
	OverwriteExisting bool              `json:"overwrite_existing"`
	NewProfileID      string            `json:"new_profile_id,omitempty"`
	Metadata          map[string]string `json:"metadata,omitempty"`
}

// Manager represents the Chrome profile manager interface
type Manager interface {
	// Profile management
	CreateProfile(name, description string) (*Profile, error)
	DeleteProfile(profileID string) error
	ListProfiles() ([]*Profile, error)
	GetProfile(profileID string) (*Profile, error)
	RenameProfile(profileID, newName string) error
	SetProfileIcon(profileID, iconPath string) error

	// Instance management
	LaunchProfile(profileID string, options *LaunchOptions) (*ProcessInfo, error)
	StopProfile(profileID string) error
	GetProfileStatus(profileID string) (ProfileStatus, error)
	ListRunningProfiles() ([]*ProcessInfo, error)

	// Import/Export
	ExportProfile(profileID string) (*ExportData, error)
	ImportProfile(data *ExportData, options *ImportOptions) (*Profile, error)
	BackupProfile(profileID, backupPath string) error

	// Cleanup
	CleanupUnusedProfiles() error
	CleanupTempFiles() error
}

// Constants
const (
	// DefaultChromeTimeout is the default timeout for Chrome operations
	DefaultChromeTimeout = 30 * time.Second

	// DefaultDebugPortStart is the starting port for Chrome debug ports
	DefaultDebugPortStart = 9222

	// MaxProfileNameLength is the maximum length for profile names
	MaxProfileNameLength = 100

	// ProfileConfigFileName is the name of the profile configuration file
	ProfileConfigFileName = "profile.json"

	// ChromeExecutableWindows is the Chrome executable name on Windows
	ChromeExecutableWindows = "chrome.exe"

	// ChromeExecutableMacOS is the Chrome executable path on macOS
	ChromeExecutableMacOS = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

	// ChromeExecutableLinux is the Chrome executable name on Linux
	ChromeExecutableLinux = "google-chrome"

	// ChromeDataDirName is the name of the Chrome data directory
	ChromeDataDirName = "ChromeProfiles"

	// ExportDataVersion is the current version of export data format
	ExportDataVersion = "1.0"
)

// Chrome launch arguments
const (
	ArgUserDataDir           = "--user-data-dir"
	ArgRemoteDebuggingPort   = "--remote-debugging-port"
	ArgNoFirstRun            = "--no-first-run"
	ArgNoDefaultBrowserCheck = "--no-default-browser-check"
	ArgDisableBackgroundMode = "--disable-background-mode"
	ArgDisableSync           = "--disable-sync"
	ArgProxyServer           = "--proxy-server"
	ArgWindowSize            = "--window-size"
	ArgWindowPosition        = "--window-position"
	ArgStartMaximized        = "--start-maximized"
	ArgStartFullscreen       = "--start-fullscreen"
	ArgHeadless              = "--headless"
	ArgNoSandbox             = "--no-sandbox"
	ArgDisableWebSecurity    = "--disable-web-security"
	ArgDisableGPU            = "--disable-gpu"
)
