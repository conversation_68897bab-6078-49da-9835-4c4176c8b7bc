package chrome

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

func TestNewProfileManager(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	if pm.GetDataDirectory() != tempDir {
		t.Errorf("Expected data directory %s, got %s", tempDir, pm.GetDataDirectory())
	}

	// Verify directories were created
	if _, err := os.Stat(tempDir); os.IsNotExist(err) {
		t.Error("Data directory should exist")
	}
}

func TestProfileManager_CreateProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Test creating profile
	profile, err := pm.CreateProfile("Test Profile", "A test profile description")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	if profile.Name != "Test Profile" {
		t.Errorf("Expected name 'Test Profile', got '%s'", profile.Name)
	}
	if profile.Description != "A test profile description" {
		t.Errorf("Expected description 'A test profile description', got '%s'", profile.Description)
	}
	if profile.Status != ProfileStatusStopped {
		t.Errorf("Expected status %s, got %s", ProfileStatusStopped, profile.Status)
	}
	if profile.ID == "" {
		t.Error("Profile ID should not be empty")
	}

	// Verify profile data directory was created
	if _, err := os.Stat(profile.UserDataDir); os.IsNotExist(err) {
		t.Error("Profile data directory should exist")
	}
}

func TestProfileManager_CreateProfile_EmptyName(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	_, err = pm.CreateProfile("", "Description")
	if err == nil {
		t.Fatal("Expected error when creating profile with empty name")
	}
}

func TestProfileManager_CreateProfile_NameTooLong(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	longName := strings.Repeat("a", MaxProfileNameLength+1)
	_, err = pm.CreateProfile(longName, "Description")
	if err == nil {
		t.Fatal("Expected error when creating profile with name too long")
	}
}

func TestProfileManager_CreateProfile_UniqueIDs(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create two profiles with the same name
	profile1, err := pm.CreateProfile("Same Name", "First profile")
	if err != nil {
		t.Fatalf("Failed to create first profile: %v", err)
	}

	// Small delay to ensure different timestamps
	time.Sleep(1 * time.Millisecond)

	profile2, err := pm.CreateProfile("Same Name", "Second profile")
	if err != nil {
		t.Fatalf("Failed to create second profile: %v", err)
	}

	if profile1.ID == profile2.ID {
		t.Error("Profile IDs should be unique even with same names")
	}
}

func TestProfileManager_GetProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	createdProfile, err := pm.CreateProfile("Get Test", "Profile for get testing")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Get the profile
	retrievedProfile, err := pm.GetProfile(createdProfile.ID)
	if err != nil {
		t.Fatalf("Failed to get profile: %v", err)
	}

	if retrievedProfile.ID != createdProfile.ID {
		t.Errorf("Expected ID %s, got %s", createdProfile.ID, retrievedProfile.ID)
	}
	if retrievedProfile.Name != createdProfile.Name {
		t.Errorf("Expected name %s, got %s", createdProfile.Name, retrievedProfile.Name)
	}
}

func TestProfileManager_GetProfile_NotFound(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	_, err = pm.GetProfile("non-existent")
	if err == nil {
		t.Fatal("Expected error when getting non-existent profile")
	}
}

func TestProfileManager_ListProfiles(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Initially should have no profiles
	profiles, err := pm.ListProfiles()
	if err != nil {
		t.Fatalf("Failed to list profiles: %v", err)
	}
	if len(profiles) != 0 {
		t.Errorf("Expected 0 profiles, got %d", len(profiles))
	}

	// Create multiple profiles
	profile1, err := pm.CreateProfile("Profile 1", "First profile")
	if err != nil {
		t.Fatalf("Failed to create profile 1: %v", err)
	}

	time.Sleep(1 * time.Millisecond) // Ensure different timestamps

	profile2, err := pm.CreateProfile("Profile 2", "Second profile")
	if err != nil {
		t.Fatalf("Failed to create profile 2: %v", err)
	}

	// List profiles
	profiles, err = pm.ListProfiles()
	if err != nil {
		t.Fatalf("Failed to list profiles: %v", err)
	}
	if len(profiles) != 2 {
		t.Errorf("Expected 2 profiles, got %d", len(profiles))
	}

	// Verify profile IDs are present
	profileIDs := make(map[string]bool)
	for _, profile := range profiles {
		profileIDs[profile.ID] = true
	}

	if !profileIDs[profile1.ID] {
		t.Error("Profile 1 not found in list")
	}
	if !profileIDs[profile2.ID] {
		t.Error("Profile 2 not found in list")
	}
}

func TestProfileManager_RenameProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	profile, err := pm.CreateProfile("Original Name", "Test profile")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Rename the profile
	newName := "New Name"
	err = pm.RenameProfile(profile.ID, newName)
	if err != nil {
		t.Fatalf("Failed to rename profile: %v", err)
	}

	// Verify the rename
	updatedProfile, err := pm.GetProfile(profile.ID)
	if err != nil {
		t.Fatalf("Failed to get updated profile: %v", err)
	}

	if updatedProfile.Name != newName {
		t.Errorf("Expected name %s, got %s", newName, updatedProfile.Name)
	}
}

func TestProfileManager_RenameProfile_EmptyName(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	profile, err := pm.CreateProfile("Test Profile", "Test profile")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Try to rename with empty name
	err = pm.RenameProfile(profile.ID, "")
	if err == nil {
		t.Fatal("Expected error when renaming with empty name")
	}
}

func TestProfileManager_SetProfileIcon(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	profile, err := pm.CreateProfile("Icon Test", "Test profile")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Set icon path
	iconPath := "/path/to/icon.png"
	err = pm.SetProfileIcon(profile.ID, iconPath)
	if err != nil {
		t.Fatalf("Failed to set profile icon: %v", err)
	}

	// Verify the icon was set
	updatedProfile, err := pm.GetProfile(profile.ID)
	if err != nil {
		t.Fatalf("Failed to get updated profile: %v", err)
	}

	if updatedProfile.IconPath != iconPath {
		t.Errorf("Expected icon path %s, got %s", iconPath, updatedProfile.IconPath)
	}
}

func TestProfileManager_DeleteProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	profile, err := pm.CreateProfile("Delete Test", "Profile to be deleted")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Verify profile exists
	_, err = pm.GetProfile(profile.ID)
	if err != nil {
		t.Fatalf("Profile should exist before deletion: %v", err)
	}

	// Delete the profile
	err = pm.DeleteProfile(profile.ID)
	if err != nil {
		t.Fatalf("Failed to delete profile: %v", err)
	}

	// Verify profile no longer exists
	_, err = pm.GetProfile(profile.ID)
	if err == nil {
		t.Fatal("Profile should not exist after deletion")
	}

	// Verify data directory was removed
	if _, err := os.Stat(profile.UserDataDir); !os.IsNotExist(err) {
		t.Error("Profile data directory should be removed")
	}
}

func TestProfileManager_DeleteProfile_NotFound(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	err = pm.DeleteProfile("non-existent")
	if err == nil {
		t.Fatal("Expected error when deleting non-existent profile")
	}
}

func TestProfileManager_GetProfileStatus(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	profile, err := pm.CreateProfile("Status Test", "Profile for status testing")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Get profile status
	status, err := pm.GetProfileStatus(profile.ID)
	if err != nil {
		t.Fatalf("Failed to get profile status: %v", err)
	}

	if status != ProfileStatusStopped {
		t.Errorf("Expected status %s, got %s", ProfileStatusStopped, status)
	}
}

func TestProfileManager_ExportProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create a profile
	profile, err := pm.CreateProfile("Export Test", "Profile for export testing")
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	// Export the profile
	exportData, err := pm.ExportProfile(profile.ID)
	if err != nil {
		t.Fatalf("Failed to export profile: %v", err)
	}

	if exportData.Version != ExportDataVersion {
		t.Errorf("Expected version %s, got %s", ExportDataVersion, exportData.Version)
	}
	if exportData.Profile.ID != profile.ID {
		t.Errorf("Expected profile ID %s, got %s", profile.ID, exportData.Profile.ID)
	}
	if exportData.Metadata == nil {
		t.Error("Export metadata should not be nil")
	}
}

func TestProfileManager_ImportProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	// Create export data
	originalProfile := &Profile{
		ID:          "import-test",
		Name:        "Import Test Profile",
		Description: "Profile for import testing",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	exportData := &ExportData{
		Version:    ExportDataVersion,
		Profile:    originalProfile,
		ExportedAt: time.Now(),
	}

	// Import the profile
	options := &ImportOptions{
		OverwriteExisting: true,
	}

	importedProfile, err := pm.ImportProfile(exportData, options)
	if err != nil {
		t.Fatalf("Failed to import profile: %v", err)
	}

	if importedProfile.Name != originalProfile.Name {
		t.Errorf("Expected name %s, got %s", originalProfile.Name, importedProfile.Name)
	}
	if importedProfile.Description != originalProfile.Description {
		t.Errorf("Expected description %s, got %s", originalProfile.Description, importedProfile.Description)
	}

	// Verify profile was saved and can be retrieved
	retrievedProfile, err := pm.GetProfile(importedProfile.ID)
	if err != nil {
		t.Fatalf("Failed to retrieve imported profile: %v", err)
	}

	if retrievedProfile.ID != importedProfile.ID {
		t.Errorf("Expected ID %s, got %s", importedProfile.ID, retrievedProfile.ID)
	}
}

func TestGenerateProfileID(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "chrome_test_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建configs子目录
	configDir := filepath.Join(tempDir, "configs")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		t.Fatalf("Failed to create config dir: %v", err)
	}

	// 重置全局变量
	profileIDInitialized = false
	globalProfileIDCounter = 0
	profileIDFile = ""

	// 初始化ID计数器
	if err := initializeProfileIDCounter(tempDir); err != nil {
		t.Fatalf("Failed to initialize profile ID counter: %v", err)
	}

	// 测试ID生成
	testCases := []struct {
		name     string
		expected string
	}{
		{"Test Profile", "Test_Profile_1"},
		{"Another Profile", "Another_Profile_2"},
		{"Profile with-symbols_123", "Profile_with_symbols_123_3"},
		{"", "profile_4"},
		{"中文测试", "profile_5"}, // 非ASCII字符应该被过滤
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result := generateProfileID(tc.name)
			if result != tc.expected {
				t.Errorf("generateProfileID(%q) = %q, expected %q", tc.name, result, tc.expected)
			}
		})
	}
}

func TestProfileIDPersistence(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "chrome_test_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建configs子目录
	configDir := filepath.Join(tempDir, "configs")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		t.Fatalf("Failed to create config dir: %v", err)
	}

	// 重置全局变量
	profileIDInitialized = false
	globalProfileIDCounter = 0
	profileIDFile = ""

	// 第一次初始化
	if err := initializeProfileIDCounter(tempDir); err != nil {
		t.Fatalf("Failed to initialize profile ID counter: %v", err)
	}

	// 生成几个ID
	id1 := generateProfileID("test1")
	id2 := generateProfileID("test2")

	// 重置全局变量模拟重启
	profileIDInitialized = false
	globalProfileIDCounter = 0
	profileIDFile = ""

	// 重新初始化
	if err := initializeProfileIDCounter(tempDir); err != nil {
		t.Fatalf("Failed to re-initialize profile ID counter: %v", err)
	}

	// 生成新的ID，应该从上次停止的地方继续
	id3 := generateProfileID("test3")

	// 验证ID的连续性
	if id1 != "test1_1" {
		t.Errorf("Expected test1_1, got %s", id1)
	}
	if id2 != "test2_2" {
		t.Errorf("Expected test2_2, got %s", id2)
	}
	if id3 != "test3_3" {
		t.Errorf("Expected test3_3, got %s", id3)
	}
}

func TestProfileIDCounterFile(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "chrome_test_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建configs子目录
	configDir := filepath.Join(tempDir, "configs")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		t.Fatalf("Failed to create config dir: %v", err)
	}

	// 重置全局变量
	profileIDInitialized = false
	globalProfileIDCounter = 0
	profileIDFile = ""

	// 初始化ID计数器
	if err := initializeProfileIDCounter(tempDir); err != nil {
		t.Fatalf("Failed to initialize profile ID counter: %v", err)
	}

	// 生成一个ID
	generateProfileID("test")

	// 检查文件是否存在
	expectedFile := filepath.Join(tempDir, "configs", "profile_id_counter.txt")
	if _, err := os.Stat(expectedFile); os.IsNotExist(err) {
		t.Errorf("Profile ID counter file should exist at %s", expectedFile)
	}

	// 读取文件内容
	content, err := os.ReadFile(expectedFile)
	if err != nil {
		t.Fatalf("Failed to read counter file: %v", err)
	}

	// 验证内容
	if string(content) != "2" {
		t.Errorf("Expected counter file content to be '2', got '%s'", string(content))
	}
}

func TestConcurrentProfileIDGeneration(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "chrome_test_")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建configs子目录
	configDir := filepath.Join(tempDir, "configs")
	if err := os.MkdirAll(configDir, 0755); err != nil {
		t.Fatalf("Failed to create config dir: %v", err)
	}

	// 重置全局变量
	profileIDInitialized = false
	globalProfileIDCounter = 0
	profileIDFile = ""

	// 初始化ID计数器
	if err := initializeProfileIDCounter(tempDir); err != nil {
		t.Fatalf("Failed to initialize profile ID counter: %v", err)
	}

	// 并发生成ID
	const numGoroutines = 10
	results := make(chan string, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(index int) {
			id := generateProfileID("concurrent")
			results <- id
		}(i)
	}

	// 收集结果
	ids := make([]string, 0, numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		select {
		case id := <-results:
			ids = append(ids, id)
		case <-time.After(5 * time.Second):
			t.Fatal("Timeout waiting for goroutines to complete")
		}
	}

	// 验证所有ID都是唯一的
	idSet := make(map[string]bool)
	for _, id := range ids {
		if idSet[id] {
			t.Errorf("Duplicate ID found: %s", id)
		}
		idSet[id] = true
	}

	// 验证ID数量
	if len(ids) != numGoroutines {
		t.Errorf("Expected %d unique IDs, got %d", numGoroutines, len(ids))
	}
}
