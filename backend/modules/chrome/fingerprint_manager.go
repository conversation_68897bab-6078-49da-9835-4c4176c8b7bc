package chrome

import (
	"crypto/rand"
	"encoding/json"
	"fmt"
	"math/big"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
)

// FingerprintManager 指纹管理器
type FingerprintManager struct {
	profileManager *ProfileManager
	fingerprintDB  map[string]*FingerprintConfig
	templatesDB    map[string]*FingerprintTemplate
	dataDir        string
	stats          *FingerprintStats
}

// NewFingerprintManager 创建指纹管理器
func NewFingerprintManager(pm *ProfileManager) *FingerprintManager {
	dataDir := filepath.Join(pm.GetDataDirectory(), "fingerprints")
	EnsureDirectoryExists(dataDir)

	fm := &FingerprintManager{
		profileManager: pm,
		fingerprintDB:  make(map[string]*FingerprintConfig),
		templatesDB:    make(map[string]*FingerprintTemplate),
		dataDir:        dataDir,
		stats: &FingerprintStats{
			PlatformDistrib: make(map[string]int),
			UsageCount:      make(map[string]int),
			LastUsed:        make(map[string]time.Time),
			DetectionEvents: []DetectionEvent{},
		},
	}

	// 加载现有指纹和模板
	fm.loadFingerprintsFromDisk()
	fm.loadTemplatesFromDisk()
	fm.initializeDefaultTemplates()

	return fm
}

// GenerateRandomFingerprint 生成随机指纹
func (fm *FingerprintManager) GenerateRandomFingerprint(platformHint string) (*FingerprintConfig, error) {
	platform := platformHint
	if platform == "" {
		platform = runtime.GOOS
	}

	now := time.Now()
	fingerprint := &FingerprintConfig{
		UserAgent:        fm.generateRandomUserAgent(platform),
		Language:         fm.generateRandomLanguage(),
		Timezone:         fm.generateRandomTimezone(),
		ScreenResolution: fm.generateRandomScreen(),
		WebRTC:           fm.generateRandomWebRTC(),
		Canvas:           fm.generateRandomCanvas(),
		WebGL:            fm.generateRandomWebGL(),
		Fonts:            fm.generateRandomFonts(),
		Hardware:         fm.generateRandomHardware(platform),
		Network:          fm.generateRandomNetwork(),
		AntiDetect:       fm.generateAntiDetectConfig(),
		CreatedAt:        now,
		UpdatedAt:        now,
		Version:          "1.0",
	}

	// 验证生成的指纹
	if err := fingerprint.Validate(); err != nil {
		return nil, fmt.Errorf("generated fingerprint validation failed: %w", err)
	}

	return fingerprint, nil
}

// CreateFingerprintProfile 创建指纹配置文件
func (fm *FingerprintManager) CreateFingerprintProfile(name, description string, fingerprint *FingerprintConfig) (*FingerprintProfile, error) {
	// 创建基础配置文件
	baseProfile, err := fm.profileManager.CreateProfile(name, description)
	if err != nil {
		return nil, fmt.Errorf("failed to create base profile: %w", err)
	}

	// 如果没有提供指纹，生成随机指纹
	if fingerprint == nil {
		fingerprint, err = fm.GenerateRandomFingerprint("")
		if err != nil {
			// 清理已创建的基础配置文件
			fm.profileManager.DeleteProfile(baseProfile.ID)
			return nil, fmt.Errorf("failed to generate fingerprint: %w", err)
		}
	}

	// 创建指纹配置文件
	now := time.Now()
	fpProfile := &FingerprintProfile{
		Profile:           baseProfile,
		Fingerprint:       fingerprint,
		AutomationEnabled: true,
		LastFingerprint:   &now,
		FingerprintHash:   fingerprint.GetHash(),
	}

	// 应用指纹到启动参数
	if err := fm.applyFingerprintToProfile(fpProfile); err != nil {
		fm.profileManager.DeleteProfile(baseProfile.ID)
		return nil, fmt.Errorf("failed to apply fingerprint: %w", err)
	}

	// 保存扩展配置
	if err := fm.SaveFingerprintProfile(fpProfile); err != nil {
		fm.profileManager.DeleteProfile(baseProfile.ID)
		return nil, fmt.Errorf("failed to save fingerprint profile: %w", err)
	}

	// 更新统计信息
	fm.updateStats(fpProfile)

	return fpProfile, nil
}

// LoadFingerprintProfile 加载指纹配置文件
func (fm *FingerprintManager) LoadFingerprintProfile(profileID string) (*FingerprintProfile, error) {
	// 加载基础配置文件
	baseProfile, err := fm.profileManager.GetProfile(profileID)
	if err != nil {
		return nil, fmt.Errorf("failed to load base profile: %w", err)
	}

	// 加载指纹配置
	fingerprintPath := filepath.Join(fm.dataDir, "profiles", profileID+".json")
	data, err := os.ReadFile(fingerprintPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read fingerprint file: %w", err)
	}

	var fpProfile FingerprintProfile
	if err := json.Unmarshal(data, &fpProfile); err != nil {
		return nil, fmt.Errorf("failed to parse fingerprint profile: %w", err)
	}

	// 设置基础配置文件
	fpProfile.Profile = baseProfile

	return &fpProfile, nil
}

// ListFingerprintProfiles 列出所有指纹配置文件
func (fm *FingerprintManager) ListFingerprintProfiles() ([]*FingerprintProfile, error) {
	baseProfiles, err := fm.profileManager.ListProfiles()
	if err != nil {
		return nil, fmt.Errorf("failed to list base profiles: %w", err)
	}

	var fingerprintProfiles []*FingerprintProfile
	for _, profile := range baseProfiles {
		fpProfile, err := fm.LoadFingerprintProfile(profile.ID)
		if err != nil {
			// 如果不是指纹配置文件，跳过
			continue
		}
		fingerprintProfiles = append(fingerprintProfiles, fpProfile)
	}

	return fingerprintProfiles, nil
}

// DeleteFingerprintProfile 删除指纹配置文件
func (fm *FingerprintManager) DeleteFingerprintProfile(profileID string) error {
	// 删除指纹配置文件
	fingerprintPath := filepath.Join(fm.dataDir, "profiles", profileID+".json")
	if _, err := os.Stat(fingerprintPath); err == nil {
		if err := os.Remove(fingerprintPath); err != nil {
			return fmt.Errorf("failed to remove fingerprint file: %w", err)
		}
	}

	// 删除基础配置文件
	return fm.profileManager.DeleteProfile(profileID)
}

// UpdateFingerprint 更新指纹配置
func (fm *FingerprintManager) UpdateFingerprint(profileID string, fingerprint *FingerprintConfig) error {
	fpProfile, err := fm.LoadFingerprintProfile(profileID)
	if err != nil {
		return err
	}

	// 更新指纹
	now := time.Now()
	fingerprint.UpdatedAt = now
	fpProfile.Fingerprint = fingerprint
	fpProfile.FingerprintHash = fingerprint.GetHash()
	fpProfile.LastFingerprint = &now

	// 重新应用指纹到启动参数
	if err := fm.applyFingerprintToProfile(fpProfile); err != nil {
		return fmt.Errorf("failed to apply updated fingerprint: %w", err)
	}

	// 保存更新的配置
	return fm.SaveFingerprintProfile(fpProfile)
}

// GenerateFromTemplate 从模板生成指纹
func (fm *FingerprintManager) GenerateFromTemplate(templateName string) (*FingerprintConfig, error) {
	template, exists := fm.templatesDB[templateName]
	if !exists {
		return nil, fmt.Errorf("template %s not found", templateName)
	}

	// 克隆模板配置
	fingerprint := template.Config.Clone()
	if fingerprint == nil {
		return nil, fmt.Errorf("failed to clone template")
	}

	// 添加一些随机性
	fm.addRandomVariation(fingerprint)

	return fingerprint, nil
}

// GetTemplate 获取指纹模板
func (fm *FingerprintManager) GetTemplate(name string) (*FingerprintTemplate, error) {
	template, exists := fm.templatesDB[name]
	if !exists {
		return nil, fmt.Errorf("template %s not found", name)
	}
	return template, nil
}

// ListTemplates 列出所有模板
func (fm *FingerprintManager) ListTemplates() []*FingerprintTemplate {
	var templates []*FingerprintTemplate
	for _, template := range fm.templatesDB {
		templates = append(templates, template)
	}
	return templates
}

// SaveTemplate 保存指纹模板
func (fm *FingerprintManager) SaveTemplate(template *FingerprintTemplate) error {
	fm.templatesDB[template.Name] = template
	return fm.saveTemplatesToDisk()
}

// GetStats 获取统计信息
func (fm *FingerprintManager) GetStats() *FingerprintStats {
	fm.updateStatsFromProfiles()
	return fm.stats
}

// 私有方法实现

// applyFingerprintToProfile 将指纹配置应用到配置文件
func (fm *FingerprintManager) applyFingerprintToProfile(fpProfile *FingerprintProfile) error {
	// 应用启动参数
	launchParams := make(map[string]string)

	// User Agent
	launchParams["--user-agent"] = fpProfile.Fingerprint.UserAgent

	// 语言设置
	launchParams["--lang"] = fpProfile.Fingerprint.Language
	launchParams["--accept-lang"] = fpProfile.Fingerprint.Network.AcceptLanguage

	// 反检测参数
	if fpProfile.Fingerprint.AntiDetect.DisableWebDriver {
		launchParams["--disable-blink-features"] = "AutomationControlled"
		launchParams["--exclude-switches"] = "enable-automation"
		launchParams["--disable-extensions-except"] = ""
		launchParams["--disable-plugins-discovery"] = ""
		launchParams["--no-first-run"] = ""
		launchParams["--no-service-autorun"] = ""
		launchParams["--password-store"] = "basic"
		launchParams["--use-mock-keychain"] = ""
	}

	if fpProfile.Fingerprint.AntiDetect.DisableHeadlessDetection {
		launchParams["--disable-features"] = "VizDisplayCompositor"
		launchParams["--run-all-compositor-stages-before-draw"] = ""
		launchParams["--disable-background-timer-throttling"] = ""
		launchParams["--disable-renderer-backgrounding"] = ""
		launchParams["--disable-backgrounding-occluded-windows"] = ""
	}

	// WebRTC设置
	switch fpProfile.Fingerprint.WebRTC.Mode {
	case "disabled":
		launchParams["--disable-webrtc"] = ""
	case "public_only":
		launchParams["--force-webrtc-ip-handling-policy"] = "default_public_interface_only"
	}

	// 屏幕和设备设置
	launchParams["--window-size"] = fmt.Sprintf("%d,%d",
		fpProfile.Fingerprint.ScreenResolution.Width,
		fpProfile.Fingerprint.ScreenResolution.Height)

	// 更新配置文件的启动参数
	fpProfile.Profile.LaunchParams = launchParams

	// 设置窗口配置
	if fpProfile.Profile.WindowConfig == nil {
		fpProfile.Profile.WindowConfig = &WindowConfig{}
	}
	fpProfile.Profile.WindowConfig.Width = fpProfile.Fingerprint.ScreenResolution.Width
	fpProfile.Profile.WindowConfig.Height = fpProfile.Fingerprint.ScreenResolution.Height

	// 设置代理配置
	if fpProfile.Fingerprint.Proxy != nil {
		fpProfile.Profile.ProxyConfig = fpProfile.Fingerprint.Proxy
	}

	return nil
}

// SaveFingerprintProfile 保存指纹配置文件
func (fm *FingerprintManager) SaveFingerprintProfile(fpProfile *FingerprintProfile) error {
	// 确保目录存在
	profilesDir := filepath.Join(fm.dataDir, "profiles")
	if err := EnsureDirectoryExists(profilesDir); err != nil {
		return err
	}

	// 序列化指纹配置文件
	data, err := json.MarshalIndent(fpProfile, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal fingerprint profile: %w", err)
	}

	// 保存到文件
	filePath := filepath.Join(profilesDir, fpProfile.Profile.ID+".json")
	if err := os.WriteFile(filePath, data, 0644); err != nil {
		return fmt.Errorf("failed to write fingerprint profile: %w", err)
	}

	return nil
}

// 生成随机配置的私有方法

func (fm *FingerprintManager) generateRandomUserAgent(platform string) string {
	agents := map[string][]string{
		"windows": {
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36 Edg/118.0.2088.76",
		},
		"darwin": {
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.2 Safari/605.1.15",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
		},
		"linux": {
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
			"Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/121.0",
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
		},
	}

	platformAgents := agents[platform]
	if len(platformAgents) == 0 {
		platformAgents = agents["windows"] // 默认Windows
	}

	n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(platformAgents))))
	return platformAgents[n.Int64()]
}

func (fm *FingerprintManager) generateRandomLanguage() string {
	languages := []string{
		"en-US", "en-GB", "es-ES", "fr-FR", "de-DE",
		"it-IT", "pt-BR", "ru-RU", "ja-JP", "zh-CN",
		"ko-KR", "nl-NL", "sv-SE", "da-DK", "no-NO",
	}
	n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(languages))))
	return languages[n.Int64()]
}

func (fm *FingerprintManager) generateRandomTimezone() string {
	timezones := []string{
		"America/New_York", "America/Los_Angeles", "America/Chicago", "America/Denver",
		"Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Rome",
		"Asia/Tokyo", "Asia/Shanghai", "Asia/Seoul", "Asia/Kolkata",
		"Australia/Sydney", "Australia/Melbourne", "Pacific/Auckland",
	}
	n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(timezones))))
	return timezones[n.Int64()]
}

func (fm *FingerprintManager) generateRandomScreen() ScreenConfig {
	screens := []ScreenConfig{
		{Width: 1920, Height: 1080, DeviceScaleFactor: 1.0, ColorDepth: 24, PixelDepth: 24, AvailWidth: 1920, AvailHeight: 1040, Orientation: "landscape"},
		{Width: 1366, Height: 768, DeviceScaleFactor: 1.0, ColorDepth: 24, PixelDepth: 24, AvailWidth: 1366, AvailHeight: 728, Orientation: "landscape"},
		{Width: 1440, Height: 900, DeviceScaleFactor: 1.0, ColorDepth: 24, PixelDepth: 24, AvailWidth: 1440, AvailHeight: 860, Orientation: "landscape"},
		{Width: 2560, Height: 1440, DeviceScaleFactor: 1.0, ColorDepth: 24, PixelDepth: 24, AvailWidth: 2560, AvailHeight: 1400, Orientation: "landscape"},
		{Width: 1536, Height: 864, DeviceScaleFactor: 1.25, ColorDepth: 24, PixelDepth: 24, AvailWidth: 1536, AvailHeight: 824, Orientation: "landscape"},
		{Width: 1280, Height: 720, DeviceScaleFactor: 1.0, ColorDepth: 24, PixelDepth: 24, AvailWidth: 1280, AvailHeight: 680, Orientation: "landscape"},
	}
	n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(screens))))
	return screens[n.Int64()]
}

func (fm *FingerprintManager) generateRandomWebRTC() WebRTCConfig {
	modes := []string{"default", "disabled", "public_only"}
	handlings := []string{"default", "default_public_interface_only", "disable_non_proxied_udp"}

	modeIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(modes))))
	handlingIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(handlings))))

	blockMedia, _ := rand.Int(rand.Reader, big.NewInt(2))

	return WebRTCConfig{
		Mode:              modes[modeIdx.Int64()],
		LocalIPHandling:   handlings[handlingIdx.Int64()],
		BlockMediaDevices: blockMedia.Int64() == 1,
	}
}

func (fm *FingerprintManager) generateRandomCanvas() CanvasConfig {
	noiseEnabled, _ := rand.Int(rand.Reader, big.NewInt(2))
	blockEnabled, _ := rand.Int(rand.Reader, big.NewInt(2))
	randomValues, _ := rand.Int(rand.Reader, big.NewInt(2))
	consistent, _ := rand.Int(rand.Reader, big.NewInt(2))

	noiseLevel, _ := rand.Int(rand.Reader, big.NewInt(10))

	noiseTypes := []string{"uniform", "gaussian"}
	typeIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(noiseTypes))))

	return CanvasConfig{
		NoiseEnabled:    noiseEnabled.Int64() == 1,
		NoiseLevel:      float64(noiseLevel.Int64()) / 100.0,
		BlockEnabled:    blockEnabled.Int64() == 1,
		RandomValues:    randomValues.Int64() == 1,
		ConsistentNoise: consistent.Int64() == 1,
		NoiseType:       noiseTypes[typeIdx.Int64()],
	}
}

func (fm *FingerprintManager) generateRandomWebGL() WebGLConfig {
	vendors := []string{
		"Google Inc. (Intel)", "Google Inc. (NVIDIA)", "Google Inc. (AMD)",
		"Intel Inc.", "NVIDIA Corporation", "Advanced Micro Devices, Inc.",
	}

	renderers := []string{
		"ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)",
		"ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)",
		"ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11)",
		"Intel(R) UHD Graphics 630",
		"NVIDIA GeForce RTX 3070",
		"AMD Radeon RX 6800 XT",
	}

	vendorIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(vendors))))
	rendererIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(renderers))))

	randomize, _ := rand.Int(rand.Reader, big.NewInt(2))
	block, _ := rand.Int(rand.Reader, big.NewInt(2))

	return WebGLConfig{
		Vendor:              vendors[vendorIdx.Int64()],
		Renderer:            renderers[rendererIdx.Int64()],
		UnmaskedVendor:      vendors[vendorIdx.Int64()],
		UnmaskedRenderer:    renderers[rendererIdx.Int64()],
		RandomizeParameters: randomize.Int64() == 1,
		BlockEnabled:        block.Int64() == 1,
	}
}

func (fm *FingerprintManager) generateRandomFonts() FontConfig {
	mask, _ := rand.Int(rand.Reader, big.NewInt(2))
	randomize, _ := rand.Int(rand.Reader, big.NewInt(2))
	fakeMetrics, _ := rand.Int(rand.Reader, big.NewInt(2))

	return FontConfig{
		MaskFonts:       mask.Int64() == 1,
		RandomizeFonts:  randomize.Int64() == 1,
		FakeFontMetrics: fakeMetrics.Int64() == 1,
		CustomFonts:     []string{},
		FontBlacklist:   []string{},
	}
}

func (fm *FingerprintManager) generateRandomHardware(platform string) HardwareConfig {
	concurrency, _ := rand.Int(rand.Reader, big.NewInt(16))
	memory, _ := rand.Int(rand.Reader, big.NewInt(32))
	touches, _ := rand.Int(rand.Reader, big.NewInt(10))

	concurrencyVal := int(concurrency.Int64()) + 1
	memoryVal := int(memory.Int64()) + 1
	touchesVal := int(touches.Int64())

	platformNames := map[string]string{
		"windows": "Win32",
		"darwin":  "MacIntel",
		"linux":   "Linux x86_64",
	}

	platformName := platformNames[platform]
	if platformName == "" {
		platformName = "Win32"
	}

	return HardwareConfig{
		CPUClass:            "x86",
		HardwareConcurrency: concurrencyVal,
		DeviceMemory:        memoryVal,
		Platform:            platformName,
		MaxTouchPoints:      touchesVal,
	}
}

func (fm *FingerprintManager) generateRandomNetwork() NetworkConfig {
	connections := []string{"4g", "3g", "wifi", "ethernet", "slow-2g", "2g"}
	effectiveTypes := []string{"slow-2g", "2g", "3g", "4g"}

	connIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(connections))))
	effectiveIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(effectiveTypes))))

	downSpeed, _ := rand.Int(rand.Reader, big.NewInt(100))
	upSpeed, _ := rand.Int(rand.Reader, big.NewInt(50))
	rtt, _ := rand.Int(rand.Reader, big.NewInt(200))
	dnt, _ := rand.Int(rand.Reader, big.NewInt(2))

	languages := []string{"en-US,en;q=0.9", "en-GB,en;q=0.9", "es-ES,es;q=0.9", "fr-FR,fr;q=0.9"}
	langIdx, _ := rand.Int(rand.Reader, big.NewInt(int64(len(languages))))

	return NetworkConfig{
		ConnectionType: connections[connIdx.Int64()],
		DownloadSpeed:  float64(downSpeed.Int64()) + 1.0,
		UploadSpeed:    float64(upSpeed.Int64()) + 1.0,
		RTT:            int(rtt.Int64()) + 10,
		EffectiveType:  effectiveTypes[effectiveIdx.Int64()],
		AcceptLanguage: languages[langIdx.Int64()],
		AcceptEncoding: "gzip, deflate, br",
		DoNotTrack:     dnt.Int64() == 1,
		CustomHeaders:  make(map[string]string),
		DNSServers:     []string{"*******", "*******"},
	}
}

func (fm *FingerprintManager) generateAntiDetectConfig() AntiDetectConfig {
	stealth, _ := rand.Int(rand.Reader, big.NewInt(2))
	disableWD, _ := rand.Int(rand.Reader, big.NewInt(2))
	disableAuto, _ := rand.Int(rand.Reader, big.NewInt(2))
	maskSignals, _ := rand.Int(rand.Reader, big.NewInt(2))
	randomTiming, _ := rand.Int(rand.Reader, big.NewInt(2))
	simulateHuman, _ := rand.Int(rand.Reader, big.NewInt(2))

	mouseDelay, _ := rand.Int(rand.Reader, big.NewInt(200))
	typeDelay, _ := rand.Int(rand.Reader, big.NewInt(100))
	scrollDelay, _ := rand.Int(rand.Reader, big.NewInt(150))
	clickDelay, _ := rand.Int(rand.Reader, big.NewInt(100))

	return AntiDetectConfig{
		StealthMode:              stealth.Int64() == 1,
		DisableWebDriver:         disableWD.Int64() == 1,
		DisableAutomation:        disableAuto.Int64() == 1,
		MaskAutomationSignals:    maskSignals.Int64() == 1,
		RandomizeTimings:         randomTiming.Int64() == 1,
		SimulateHuman:            simulateHuman.Int64() == 1,
		MaskPlugins:              true,
		MaskMimeTypes:            true,
		MaskPermissions:          true,
		FakeNotifications:        true,
		DisableHeadlessDetection: true,
		MouseMovementDelay:       int(mouseDelay.Int64()) + 50,
		TypingDelay:              int(typeDelay.Int64()) + 30,
		ScrollDelay:              int(scrollDelay.Int64()) + 50,
		ClickDelay:               int(clickDelay.Int64()) + 30,
		HumanLikeMovement:        simulateHuman.Int64() == 1,
	}
}

func (fm *FingerprintManager) addRandomVariation(fingerprint *FingerprintConfig) {
	// 添加轻微的随机变化，但保持指纹的基本特征

	// 屏幕分辨率轻微调整
	widthVar, _ := rand.Int(rand.Reader, big.NewInt(100))
	heightVar, _ := rand.Int(rand.Reader, big.NewInt(100))

	fingerprint.ScreenResolution.Width += int(widthVar.Int64()) - 50
	fingerprint.ScreenResolution.Height += int(heightVar.Int64()) - 50

	// 确保分辨率合理
	if fingerprint.ScreenResolution.Width < 800 {
		fingerprint.ScreenResolution.Width = 800
	}
	if fingerprint.ScreenResolution.Height < 600 {
		fingerprint.ScreenResolution.Height = 600
	}

	// Canvas噪声级别轻微调整
	noiseVar, _ := rand.Int(rand.Reader, big.NewInt(20))
	fingerprint.Canvas.NoiseLevel = float64(noiseVar.Int64()) / 1000.0

	// 更新时间戳
	fingerprint.UpdateTimestamp()
}

// 数据持久化方法

func (fm *FingerprintManager) loadFingerprintsFromDisk() {
	fingerprintsPath := filepath.Join(fm.dataDir, "fingerprints.json")
	if _, err := os.Stat(fingerprintsPath); os.IsNotExist(err) {
		return
	}

	data, err := os.ReadFile(fingerprintsPath)
	if err != nil {
		return
	}

	json.Unmarshal(data, &fm.fingerprintDB)
}

func (fm *FingerprintManager) loadTemplatesFromDisk() {
	templatesPath := filepath.Join(fm.dataDir, "templates.json")
	if _, err := os.Stat(templatesPath); os.IsNotExist(err) {
		return
	}

	data, err := os.ReadFile(templatesPath)
	if err != nil {
		return
	}

	json.Unmarshal(data, &fm.templatesDB)
}

func (fm *FingerprintManager) saveTemplatesToDisk() error {
	data, err := json.MarshalIndent(fm.templatesDB, "", "  ")
	if err != nil {
		return err
	}

	templatesPath := filepath.Join(fm.dataDir, "templates.json")
	return os.WriteFile(templatesPath, data, 0644)
}

func (fm *FingerprintManager) initializeDefaultTemplates() {
	// 如果没有模板，创建一些默认模板
	if len(fm.templatesDB) > 0 {
		return
	}

	// Windows Chrome 模板
	windowsTemplate := &FingerprintTemplate{
		Name:        "Windows Chrome Standard",
		Description: "Standard Windows Chrome fingerprint",
		Platform:    "windows",
		Category:    "standard",
		CreatedAt:   time.Now(),
		Popular:     true,
		Verified:    true,
	}

	windowsFingerprint, _ := fm.GenerateRandomFingerprint("windows")
	windowsTemplate.Config = windowsFingerprint
	fm.templatesDB[windowsTemplate.Name] = windowsTemplate

	// macOS Safari 模板
	macTemplate := &FingerprintTemplate{
		Name:        "macOS Safari Standard",
		Description: "Standard macOS Safari fingerprint",
		Platform:    "darwin",
		Category:    "standard",
		CreatedAt:   time.Now(),
		Popular:     true,
		Verified:    true,
	}

	macFingerprint, _ := fm.GenerateRandomFingerprint("darwin")
	macTemplate.Config = macFingerprint
	fm.templatesDB[macTemplate.Name] = macTemplate

	// 保存默认模板
	fm.saveTemplatesToDisk()
}

func (fm *FingerprintManager) updateStats(fpProfile *FingerprintProfile) {
	fm.stats.TotalProfiles++
	platform := strings.ToLower(fpProfile.Fingerprint.Hardware.Platform)
	fm.stats.PlatformDistrib[platform]++
	fm.stats.UsageCount[fpProfile.Profile.ID] = 0
	fm.stats.LastUsed[fpProfile.Profile.ID] = time.Now()
}

func (fm *FingerprintManager) updateStatsFromProfiles() {
	profiles, err := fm.ListFingerprintProfiles()
	if err != nil {
		return
	}

	fm.stats.TotalProfiles = len(profiles)
	activeCount := 0

	for _, profile := range profiles {
		if profile.Profile.Status == ProfileStatusRunning {
			activeCount++
		}
	}

	fm.stats.ActiveProfiles = activeCount

	if fm.stats.TotalProfiles > 0 {
		fm.stats.SuccessRate = float64(activeCount) / float64(fm.stats.TotalProfiles)
	}
}
