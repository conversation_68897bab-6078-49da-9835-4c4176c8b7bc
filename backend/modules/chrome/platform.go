package chrome

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strings"
)

// Platform represents the operating system platform
type Platform string

const (
	PlatformWindows Platform = "windows"
	PlatformMacOS   Platform = "darwin"
	PlatformLinux   Platform = "linux"
	PlatformUnknown Platform = "unknown"
)

// PlatformInfo contains platform-specific information
type PlatformInfo struct {
	OS               Platform
	ChromeExecutable string
	DefaultDataDir   string
	PathSeparator    string
	SupportsSymlinks bool
}

// GetPlatform returns the current platform
func GetPlatform() Platform {
	switch runtime.GOOS {
	case "windows":
		return PlatformWindows
	case "darwin":
		return PlatformMacOS
	case "linux":
		return PlatformLinux
	default:
		return PlatformUnknown
	}
}

// GetPlatformInfo returns detailed platform information
func GetPlatformInfo() (*PlatformInfo, error) {
	platform := GetPlatform()

	info := &PlatformInfo{
		OS:            platform,
		PathSeparator: string(filepath.Separator),
	}

	switch platform {
	case PlatformWindows:
		return setupWindowsInfo(info)
	case PlatformMacOS:
		return setupMacOSInfo(info)
	case PlatformLinux:
		return setupLinuxInfo(info)
	default:
		return nil, ErrUnsupportedPlatform
	}
}

// setupWindowsInfo configures platform info for Windows
func setupWindowsInfo(info *PlatformInfo) (*PlatformInfo, error) {
	info.SupportsSymlinks = false

	// Try common Chrome installation paths on Windows
	chromePaths := []string{
		`C:\Program Files\Google\Chrome\Application\chrome.exe`,
		`C:\Program Files (x86)\Google\Chrome\Application\chrome.exe`,
		`%USERPROFILE%\AppData\Local\Google\Chrome\Application\chrome.exe`,
		`%PROGRAMFILES%\Google\Chrome\Application\chrome.exe`,
		`%PROGRAMFILES(X86)%\Google\Chrome\Application\chrome.exe`,
	}

	for _, path := range chromePaths {
		expandedPath := os.ExpandEnv(path)
		if fileExists(expandedPath) {
			info.ChromeExecutable = expandedPath
			break
		}
	}

	if info.ChromeExecutable == "" {
		// Try to find Chrome in PATH
		if chromePath, err := exec.LookPath("chrome"); err == nil {
			info.ChromeExecutable = chromePath
		} else if chromePath, err := exec.LookPath("chrome.exe"); err == nil {
			info.ChromeExecutable = chromePath
		} else {
			return nil, ErrChromeNotFound
		}
	}

	// Set default data directory
	appData := os.Getenv("APPDATA")
	if appData != "" {
		info.DefaultDataDir = filepath.Join(appData, ChromeDataDirName)
	} else {
		userProfile := os.Getenv("USERPROFILE")
		if userProfile != "" {
			info.DefaultDataDir = filepath.Join(userProfile, "AppData", "Roaming", ChromeDataDirName)
		} else {
			info.DefaultDataDir = filepath.Join("C:", "Users", "Default", "AppData", "Roaming", ChromeDataDirName)
		}
	}

	return info, nil
}

// setupMacOSInfo configures platform info for macOS
func setupMacOSInfo(info *PlatformInfo) (*PlatformInfo, error) {
	info.SupportsSymlinks = true

	// Try common Chrome installation paths on macOS
	chromePaths := []string{
		"/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
		"/Applications/Google Chrome Canary.app/Contents/MacOS/Google Chrome Canary",
		"/Applications/Chromium.app/Contents/MacOS/Chromium",
		"/System/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
	}

	for _, path := range chromePaths {
		if fileExists(path) {
			info.ChromeExecutable = path
			break
		}
	}

	if info.ChromeExecutable == "" {
		// Try to find Chrome in PATH
		if chromePath, err := exec.LookPath("google-chrome"); err == nil {
			info.ChromeExecutable = chromePath
		} else if chromePath, err := exec.LookPath("chromium"); err == nil {
			info.ChromeExecutable = chromePath
		} else {
			return nil, ErrChromeNotFound
		}
	}

	// Set default data directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, WrapError(err, "failed to get user home directory")
	}

	info.DefaultDataDir = filepath.Join(homeDir, "Library", "Application Support", ChromeDataDirName)

	return info, nil
}

// setupLinuxInfo configures platform info for Linux
func setupLinuxInfo(info *PlatformInfo) (*PlatformInfo, error) {
	info.SupportsSymlinks = true

	// Try common Chrome installation paths on Linux
	chromePaths := []string{
		"/usr/bin/google-chrome",
		"/usr/bin/google-chrome-stable",
		"/usr/bin/chromium",
		"/usr/bin/chromium-browser",
		"/opt/google/chrome/chrome",
		"/snap/bin/chromium",
		"/usr/local/bin/chrome",
		"/usr/local/bin/google-chrome",
	}

	for _, path := range chromePaths {
		if fileExists(path) {
			info.ChromeExecutable = path
			break
		}
	}

	if info.ChromeExecutable == "" {
		// Try to find Chrome in PATH
		if chromePath, err := exec.LookPath("google-chrome"); err == nil {
			info.ChromeExecutable = chromePath
		} else if chromePath, err := exec.LookPath("google-chrome-stable"); err == nil {
			info.ChromeExecutable = chromePath
		} else if chromePath, err := exec.LookPath("chromium"); err == nil {
			info.ChromeExecutable = chromePath
		} else if chromePath, err := exec.LookPath("chromium-browser"); err == nil {
			info.ChromeExecutable = chromePath
		} else {
			return nil, ErrChromeNotFound
		}
	}

	// Set default data directory
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return nil, WrapError(err, "failed to get user home directory")
	}

	// Try XDG_DATA_HOME first, then fallback to ~/.local/share
	xdgDataHome := os.Getenv("XDG_DATA_HOME")
	if xdgDataHome != "" {
		info.DefaultDataDir = filepath.Join(xdgDataHome, ChromeDataDirName)
	} else {
		info.DefaultDataDir = filepath.Join(homeDir, ".local", "share", ChromeDataDirName)
	}

	return info, nil
}

// GetChromeExecutable returns the path to the Chrome executable
func GetChromeExecutable() (string, error) {
	info, err := GetPlatformInfo()
	if err != nil {
		return "", err
	}

	if info.ChromeExecutable == "" {
		return "", ErrChromeNotFound
	}

	return info.ChromeExecutable, nil
}

// GetDefaultDataDir returns the default data directory for Chrome profiles
func GetDefaultDataDir() (string, error) {
	info, err := GetPlatformInfo()
	if err != nil {
		return "", err
	}

	return info.DefaultDataDir, nil
}

// ValidateChromeExecutable checks if the Chrome executable is valid and accessible
func ValidateChromeExecutable(execPath string) error {
	if execPath == "" {
		return ErrChromeNotFound
	}

	// Check if file exists
	if !fileExists(execPath) {
		return NewFileSystemError(execPath, "check existence", ErrChromeNotFound)
	}

	// Check if file is executable
	info, err := os.Stat(execPath)
	if err != nil {
		return NewFileSystemError(execPath, "stat", err)
	}

	// On Unix-like systems, check execute permissions
	if runtime.GOOS != "windows" {
		mode := info.Mode()
		if mode&0111 == 0 {
			return NewFileSystemError(execPath, "check permissions",
				fmt.Errorf("file is not executable"))
		}
	}

	return nil
}

// GetChromeVersion returns the version of the Chrome executable
func GetChromeVersion(execPath string) (string, error) {
	if err := ValidateChromeExecutable(execPath); err != nil {
		return "", err
	}

	cmd := exec.Command(execPath, "--version")
	output, err := cmd.Output()
	if err != nil {
		return "", WrapError(err, "failed to get Chrome version")
	}

	version := strings.TrimSpace(string(output))
	// Extract version number from output like "Google Chrome 108.0.5359.124"
	parts := strings.Fields(version)
	if len(parts) >= 3 {
		return parts[len(parts)-1], nil
	}

	return version, nil
}

// CreatePlatformSpecificArgs creates platform-specific Chrome launch arguments
func CreatePlatformSpecificArgs() []string {
	args := []string{
		ArgNoFirstRun,
		ArgNoDefaultBrowserCheck,
		ArgDisableBackgroundMode,
		ArgDisableSync,
	}

	platform := GetPlatform()

	switch platform {
	case PlatformLinux:
		// Linux-specific arguments
		args = append(args,
			ArgNoSandbox,  // Often needed on Linux
			ArgDisableGPU, // Helps with compatibility
		)
	case PlatformWindows:
		// Windows-specific arguments (none currently needed)
	case PlatformMacOS:
		// macOS-specific arguments (none currently needed)
	}

	return args
}

// EnsureDirectoryExists creates a directory if it doesn't exist
func EnsureDirectoryExists(dirPath string) error {
	if dirPath == "" {
		return ErrInvalidArgument
	}

	// Check if directory already exists
	info, err := os.Stat(dirPath)
	if err == nil {
		if !info.IsDir() {
			return NewFileSystemError(dirPath, "create directory",
				fmt.Errorf("path exists but is not a directory"))
		}
		return nil // Directory already exists
	}

	// Create directory with appropriate permissions
	err = os.MkdirAll(dirPath, 0755)
	if err != nil {
		return NewFileSystemError(dirPath, "create directory", err)
	}

	return nil
}

// GetProcessByPID checks if a process with the given PID exists
func GetProcessByPID(pid int) (*os.Process, error) {
	if pid <= 0 {
		return nil, ErrInvalidArgument
	}

	process, err := os.FindProcess(pid)
	if err != nil {
		return nil, WrapError(err, "failed to find process")
	}

	// On Unix systems, check if process actually exists
	if runtime.GOOS != "windows" {
		err := process.Signal(os.Signal(nil)) // Send null signal to test existence
		if err != nil {
			return nil, ErrChromeProcessNotFound
		}
	}

	return process, nil
}

// IsProcessRunning checks if a process with the given PID is running
func IsProcessRunning(pid int) bool {
	_, err := GetProcessByPID(pid)
	return err == nil
}

// fileExists checks if a file exists
func fileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// GetTempDir returns a platform-appropriate temporary directory
func GetTempDir() string {
	return os.TempDir()
}

// IsValidPath checks if a path is valid for the current platform
func IsValidPath(path string) bool {
	if path == "" {
		return false
	}

	// Basic validation - check for invalid characters
	platform := GetPlatform()

	switch platform {
	case PlatformWindows:
		// Windows path validation
		invalidChars := []string{"<", ">", ":", "\"", "|", "?", "*"}
		for _, char := range invalidChars {
			if strings.Contains(path, char) {
				return false
			}
		}
	case PlatformMacOS, PlatformLinux:
		// Unix-like path validation
		if strings.Contains(path, "\x00") {
			return false
		}
	}

	return true
}
