package chrome

import (
	"fmt"
	"os"
	"strings"
	"testing"
	"time"
)

func TestNewFingerprintManager(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)
	if fm == nil {
		t.Fatal("NewFingerprintManager returned nil")
	}

	if fm.profileManager != pm {
		t.Error("Profile manager not set correctly")
	}

	if fm.fingerprintDB == nil {
		t.<PERSON>rror("Fingerprint DB not initialized")
	}

	if fm.templatesDB == nil {
		t.Error("Templates DB not initialized")
	}

	if fm.stats == nil {
		t.Error("Stats not initialized")
	}

	// Check if default templates were created
	templates := fm.ListTemplates()
	if len(templates) == 0 {
		t.Error("Default templates should be created")
	}
}

func TestFingerprintManager_GenerateRandomFingerprint(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	tests := []struct {
		name     string
		platform string
		wantErr  bool
	}{
		{"Windows platform", "windows", false},
		{"macOS platform", "darwin", false},
		{"Linux platform", "linux", false},
		{"Empty platform", "", false},
		{"Unknown platform", "unknown", false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fingerprint, err := fm.GenerateRandomFingerprint(tt.platform)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateRandomFingerprint() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if fingerprint == nil {
					t.Error("Generated fingerprint should not be nil")
					return
				}

				// Validate generated fingerprint
				if err := fingerprint.Validate(); err != nil {
					t.Errorf("Generated fingerprint is invalid: %v", err)
				}

				// Check that all required fields are set
				if fingerprint.UserAgent == "" {
					t.Error("User agent should not be empty")
				}

				if fingerprint.Language == "" {
					t.Error("Language should not be empty")
				}

				if fingerprint.Timezone == "" {
					t.Error("Timezone should not be empty")
				}

				if fingerprint.ScreenResolution.Width <= 0 {
					t.Error("Screen width should be positive")
				}

				if fingerprint.ScreenResolution.Height <= 0 {
					t.Error("Screen height should be positive")
				}

				if fingerprint.Hardware.HardwareConcurrency <= 0 {
					t.Error("Hardware concurrency should be positive")
				}

				if fingerprint.Version == "" {
					t.Error("Version should not be empty")
				}
			}
		})
	}
}

func TestFingerprintManager_CreateFingerprintProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	tests := []struct {
		name        string
		profileName string
		description string
		fingerprint *FingerprintConfig
		wantErr     bool
	}{
		{
			name:        "Valid profile with fingerprint",
			profileName: "Test Profile",
			description: "Test Description",
			fingerprint: &FingerprintConfig{
				UserAgent:        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
				ScreenResolution: ScreenConfig{Width: 1920, Height: 1080},
				Hardware:         HardwareConfig{HardwareConcurrency: 8},
				Version:          "1.0",
			},
			wantErr: false,
		},
		{
			name:        "Valid profile without fingerprint",
			profileName: "Auto Generated Profile",
			description: "Auto generated fingerprint",
			fingerprint: nil,
			wantErr:     false,
		},
		{
			name:        "Empty profile name",
			profileName: "",
			description: "Test Description",
			fingerprint: nil,
			wantErr:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			profile, err := fm.CreateFingerprintProfile(tt.profileName, tt.description, tt.fingerprint)
			if (err != nil) != tt.wantErr {
				t.Errorf("CreateFingerprintProfile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if profile == nil {
					t.Error("Created profile should not be nil")
					return
				}

				if profile.Profile == nil {
					t.Error("Base profile should not be nil")
				}

				if profile.Fingerprint == nil {
					t.Error("Fingerprint should not be nil")
				}

				if profile.Profile.Name != tt.profileName {
					t.Errorf("Profile name mismatch: got %s, want %s", profile.Profile.Name, tt.profileName)
				}

				if !profile.AutomationEnabled {
					t.Error("Automation should be enabled by default")
				}

				if profile.FingerprintHash == "" {
					t.Error("Fingerprint hash should not be empty")
				}

				if profile.LastFingerprint == nil {
					t.Error("Last fingerprint time should be set")
				}

				// Verify the profile can be loaded back
				loadedProfile, err := fm.LoadFingerprintProfile(profile.Profile.ID)
				if err != nil {
					t.Errorf("Failed to load created profile: %v", err)
				} else if loadedProfile.Profile.ID != profile.Profile.ID {
					t.Error("Loaded profile ID mismatch")
				}
			}
		})
	}
}

func TestFingerprintManager_LoadFingerprintProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Create a profile first
	profile, err := fm.CreateFingerprintProfile("Test Profile", "Test Description", nil)
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	tests := []struct {
		name      string
		profileID string
		wantErr   bool
	}{
		{
			name:      "Valid profile ID",
			profileID: profile.Profile.ID,
			wantErr:   false,
		},
		{
			name:      "Invalid profile ID",
			profileID: "non-existent-id",
			wantErr:   true,
		},
		{
			name:      "Empty profile ID",
			profileID: "",
			wantErr:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			loadedProfile, err := fm.LoadFingerprintProfile(tt.profileID)
			if (err != nil) != tt.wantErr {
				t.Errorf("LoadFingerprintProfile() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if loadedProfile == nil {
					t.Error("Loaded profile should not be nil")
					return
				}

				if loadedProfile.Profile.ID != tt.profileID {
					t.Errorf("Profile ID mismatch: got %s, want %s", loadedProfile.Profile.ID, tt.profileID)
				}

				if loadedProfile.Fingerprint == nil {
					t.Error("Fingerprint should not be nil")
				}
			}
		})
	}
}

func TestFingerprintManager_ListFingerprintProfiles(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Initially should be empty
	profiles, err := fm.ListFingerprintProfiles()
	if err != nil {
		t.Fatalf("Failed to list profiles: %v", err)
	}

	if len(profiles) != 0 {
		t.Errorf("Expected 0 profiles, got %d", len(profiles))
	}

	// Create some profiles
	expectedCount := 3
	for i := 0; i < expectedCount; i++ {
		_, err := fm.CreateFingerprintProfile(
			fmt.Sprintf("Test Profile %d", i+1),
			fmt.Sprintf("Test Description %d", i+1),
			nil,
		)
		if err != nil {
			t.Fatalf("Failed to create profile %d: %v", i+1, err)
		}
	}

	// List profiles again
	profiles, err = fm.ListFingerprintProfiles()
	if err != nil {
		t.Fatalf("Failed to list profiles after creation: %v", err)
	}

	if len(profiles) != expectedCount {
		t.Errorf("Expected %d profiles, got %d", expectedCount, len(profiles))
	}

	// Verify all profiles have required fields
	for i, profile := range profiles {
		if profile.Profile == nil {
			t.Errorf("Profile %d should have base profile", i)
		}

		if profile.Fingerprint == nil {
			t.Errorf("Profile %d should have fingerprint", i)
		}

		if profile.FingerprintHash == "" {
			t.Errorf("Profile %d should have fingerprint hash", i)
		}
	}
}

func TestFingerprintManager_DeleteFingerprintProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Create a profile to delete
	profile, err := fm.CreateFingerprintProfile("Test Profile", "Test Description", nil)
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	profileID := profile.Profile.ID

	// Verify profile exists
	_, err = fm.LoadFingerprintProfile(profileID)
	if err != nil {
		t.Fatalf("Profile should exist before deletion: %v", err)
	}

	// Delete the profile
	err = fm.DeleteFingerprintProfile(profileID)
	if err != nil {
		t.Fatalf("Failed to delete profile: %v", err)
	}

	// Verify profile no longer exists
	_, err = fm.LoadFingerprintProfile(profileID)
	if err == nil {
		t.Error("Profile should not exist after deletion")
	}

	// Test deleting non-existent profile
	err = fm.DeleteFingerprintProfile("non-existent-id")
	if err == nil {
		t.Error("Deleting non-existent profile should return error")
	}
}

func TestFingerprintManager_UpdateFingerprint(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Create a profile
	profile, err := fm.CreateFingerprintProfile("Test Profile", "Test Description", nil)
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	originalHash := profile.FingerprintHash
	originalUpdateTime := profile.Fingerprint.UpdatedAt

	// Create updated fingerprint
	updatedFingerprint, err := fm.GenerateRandomFingerprint("windows")
	if err != nil {
		t.Fatalf("Failed to generate updated fingerprint: %v", err)
	}

	// Wait a bit to ensure timestamp difference
	time.Sleep(10 * time.Millisecond)

	// Update the fingerprint
	err = fm.UpdateFingerprint(profile.Profile.ID, updatedFingerprint)
	if err != nil {
		t.Fatalf("Failed to update fingerprint: %v", err)
	}

	// Load the updated profile
	updatedProfile, err := fm.LoadFingerprintProfile(profile.Profile.ID)
	if err != nil {
		t.Fatalf("Failed to load updated profile: %v", err)
	}

	// Verify the fingerprint was updated
	if updatedProfile.FingerprintHash == originalHash {
		t.Error("Fingerprint hash should be different after update")
	}

	if !updatedProfile.Fingerprint.UpdatedAt.After(originalUpdateTime) {
		t.Error("Update timestamp should be newer")
	}

	if updatedProfile.LastFingerprint == nil {
		t.Error("Last fingerprint time should be set")
	}

	// Test updating non-existent profile
	err = fm.UpdateFingerprint("non-existent-id", updatedFingerprint)
	if err == nil {
		t.Error("Updating non-existent profile should return error")
	}
}

func TestFingerprintManager_GenerateFromTemplate(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Get available templates
	templates := fm.ListTemplates()
	if len(templates) == 0 {
		t.Skip("No templates available for testing")
	}

	template := templates[0]

	// Generate fingerprint from template
	fingerprint, err := fm.GenerateFromTemplate(template.Name)
	if err != nil {
		t.Fatalf("Failed to generate from template: %v", err)
	}

	if fingerprint == nil {
		t.Error("Generated fingerprint should not be nil")
	}

	// Verify it's different from the original template (due to added variation)
	if fingerprint.GetHash() == template.Config.GetHash() {
		t.Error("Generated fingerprint should be different from template due to variation")
	}

	// But basic characteristics should be similar
	if fingerprint.UserAgent == "" {
		t.Error("User agent should not be empty")
	}

	// Test with non-existent template
	_, err = fm.GenerateFromTemplate("non-existent-template")
	if err == nil {
		t.Error("Should return error for non-existent template")
	}
}

func TestFingerprintManager_Templates(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// List initial templates
	templates := fm.ListTemplates()
	initialCount := len(templates)

	if initialCount == 0 {
		t.Error("Should have some default templates")
	}

	// Create a custom template
	customFingerprint, err := fm.GenerateRandomFingerprint("linux")
	if err != nil {
		t.Fatalf("Failed to generate custom fingerprint: %v", err)
	}

	customTemplate := &FingerprintTemplate{
		Name:        "Custom Linux Template",
		Description: "A custom Linux template for testing",
		Platform:    "linux",
		Category:    "custom",
		Config:      customFingerprint,
		CreatedAt:   time.Now(),
		Popular:     false,
		Verified:    false,
	}

	// Save the custom template
	err = fm.SaveTemplate(customTemplate)
	if err != nil {
		t.Fatalf("Failed to save custom template: %v", err)
	}

	// Verify template was saved
	templates = fm.ListTemplates()
	if len(templates) != initialCount+1 {
		t.Errorf("Expected %d templates, got %d", initialCount+1, len(templates))
	}

	// Get the saved template
	savedTemplate, err := fm.GetTemplate(customTemplate.Name)
	if err != nil {
		t.Fatalf("Failed to get saved template: %v", err)
	}

	if savedTemplate.Name != customTemplate.Name {
		t.Errorf("Template name mismatch: got %s, want %s", savedTemplate.Name, customTemplate.Name)
	}

	if savedTemplate.Platform != customTemplate.Platform {
		t.Errorf("Template platform mismatch: got %s, want %s", savedTemplate.Platform, customTemplate.Platform)
	}

	// Test getting non-existent template
	_, err = fm.GetTemplate("non-existent-template")
	if err == nil {
		t.Error("Should return error for non-existent template")
	}
}

func TestFingerprintManager_Stats(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Get initial stats
	stats := fm.GetStats()
	if stats == nil {
		t.Error("Stats should not be nil")
	}

	initialTotal := stats.TotalProfiles

	// Create some profiles
	profileCount := 3
	for i := 0; i < profileCount; i++ {
		_, err := fm.CreateFingerprintProfile(
			fmt.Sprintf("Stats Test Profile %d", i+1),
			"Stats test profile",
			nil,
		)
		if err != nil {
			t.Fatalf("Failed to create profile %d: %v", i+1, err)
		}
	}

	// Get updated stats
	stats = fm.GetStats()
	if stats.TotalProfiles != initialTotal+profileCount {
		t.Errorf("Expected %d total profiles, got %d", initialTotal+profileCount, stats.TotalProfiles)
	}

	if stats.PlatformDistrib == nil {
		t.Error("Platform distribution should be initialized")
	}

	if stats.UsageCount == nil {
		t.Error("Usage count should be initialized")
	}

	if stats.LastUsed == nil {
		t.Error("Last used should be initialized")
	}
}

func TestFingerprintManager_RandomGeneration(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Generate multiple fingerprints and verify they're different
	fingerprints := make([]*FingerprintConfig, 5)
	hashes := make(map[string]bool)

	for i := 0; i < len(fingerprints); i++ {
		fp, err := fm.GenerateRandomFingerprint("windows")
		if err != nil {
			t.Fatalf("Failed to generate fingerprint %d: %v", i, err)
		}

		fingerprints[i] = fp
		hash := fp.GetHash()

		// Check for uniqueness
		if hashes[hash] {
			t.Errorf("Duplicate fingerprint hash detected: %s", hash)
		}
		hashes[hash] = true
	}

	// Verify different aspects of randomization
	userAgents := make(map[string]bool)
	languages := make(map[string]bool)
	timezones := make(map[string]bool)
	screenSizes := make(map[string]bool)

	for _, fp := range fingerprints {
		userAgents[fp.UserAgent] = true
		languages[fp.Language] = true
		timezones[fp.Timezone] = true
		screenSizes[fmt.Sprintf("%dx%d", fp.ScreenResolution.Width, fp.ScreenResolution.Height)] = true
	}

	// We should have some variety (though it's possible to have duplicates by chance)
	if len(userAgents) == 1 && len(fingerprints) > 1 {
		t.Log("Warning: All fingerprints have the same user agent (could be random)")
	}

	if len(languages) == 1 && len(fingerprints) > 1 {
		t.Log("Warning: All fingerprints have the same language (could be random)")
	}
}

func TestFingerprintManager_UserAgentGeneration(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	platforms := []string{"windows", "darwin", "linux"}

	for _, platform := range platforms {
		userAgent := fm.generateRandomUserAgent(platform)

		if userAgent == "" {
			t.Errorf("User agent should not be empty for platform %s", platform)
		}

		// Check that user agent looks reasonable
		if !strings.Contains(userAgent, "Mozilla") {
			t.Errorf("User agent should contain Mozilla for platform %s: %s", platform, userAgent)
		}

		// Platform-specific checks
		switch platform {
		case "windows":
			if !strings.Contains(userAgent, "Windows") {
				t.Errorf("Windows user agent should contain 'Windows': %s", userAgent)
			}
		case "darwin":
			if !strings.Contains(userAgent, "Mac") {
				t.Errorf("macOS user agent should contain 'Mac': %s", userAgent)
			}
		case "linux":
			if !strings.Contains(userAgent, "Linux") {
				t.Errorf("Linux user agent should contain 'Linux': %s", userAgent)
			}
		}
	}
}

func TestFingerprintManager_LanguageGeneration(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Generate multiple languages and check validity
	languages := make(map[string]bool)
	for i := 0; i < 20; i++ {
		lang := fm.generateRandomLanguage()
		languages[lang] = true

		// Check format (should be like "en-US")
		if !strings.Contains(lang, "-") {
			t.Errorf("Language should contain hyphen: %s", lang)
		}

		parts := strings.Split(lang, "-")
		if len(parts) != 2 {
			t.Errorf("Language should have exactly two parts: %s", lang)
		}

		if len(parts[0]) != 2 {
			t.Errorf("Language code should be 2 characters: %s", lang)
		}

		if len(parts[1]) != 2 {
			t.Errorf("Country code should be 2 characters: %s", lang)
		}
	}

	// Should have generated multiple different languages
	if len(languages) < 2 {
		t.Error("Should generate multiple different languages")
	}
}

func TestFingerprintManager_TimezoneGeneration(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	// Generate multiple timezones and check validity
	timezones := make(map[string]bool)
	for i := 0; i < 20; i++ {
		tz := fm.generateRandomTimezone()
		timezones[tz] = true

		// Check format (should be like "America/New_York")
		if !strings.Contains(tz, "/") {
			t.Errorf("Timezone should contain slash: %s", tz)
		}

		parts := strings.Split(tz, "/")
		if len(parts) < 2 {
			t.Errorf("Timezone should have at least two parts: %s", tz)
		}
	}

	// Should have generated multiple different timezones
	if len(timezones) < 2 {
		t.Error("Should generate multiple different timezones")
	}
}

func TestFingerprintManager_HardwareGeneration(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	platforms := []string{"windows", "darwin", "linux"}

	for _, platform := range platforms {
		hardware := fm.generateRandomHardware(platform)

		if hardware.HardwareConcurrency <= 0 {
			t.Errorf("Hardware concurrency should be positive for platform %s", platform)
		}

		if hardware.HardwareConcurrency > 32 {
			t.Errorf("Hardware concurrency seems too high for platform %s: %d", platform, hardware.HardwareConcurrency)
		}

		if hardware.DeviceMemory <= 0 {
			t.Errorf("Device memory should be positive for platform %s", platform)
		}

		if hardware.Platform == "" {
			t.Errorf("Platform should not be empty for %s", platform)
		}

		if hardware.CPUClass == "" {
			t.Errorf("CPU class should not be empty for platform %s", platform)
		}

		// Platform-specific checks
		switch platform {
		case "windows":
			if hardware.Platform != "Win32" {
				t.Errorf("Windows platform should be Win32, got %s", hardware.Platform)
			}
		case "darwin":
			if hardware.Platform != "MacIntel" {
				t.Errorf("macOS platform should be MacIntel, got %s", hardware.Platform)
			}
		case "linux":
			if !strings.Contains(hardware.Platform, "Linux") {
				t.Errorf("Linux platform should contain 'Linux', got %s", hardware.Platform)
			}
		}
	}
}

// Test file persistence
func TestFingerprintManager_Persistence(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Create first manager instance
	pm1, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create profile manager: %v", err)
	}

	fm1 := NewFingerprintManager(pm1)

	// Create a profile
	profile, err := fm1.CreateFingerprintProfile("Persistence Test", "Test persistence", nil)
	if err != nil {
		t.Fatalf("Failed to create profile: %v", err)
	}

	profileID := profile.Profile.ID

	// Create second manager instance (simulating restart)
	pm2, err := NewProfileManager(tempDir)
	if err != nil {
		t.Fatalf("Failed to create second profile manager: %v", err)
	}

	fm2 := NewFingerprintManager(pm2)

	// Try to load the profile with the second manager
	loadedProfile, err := fm2.LoadFingerprintProfile(profileID)
	if err != nil {
		t.Fatalf("Failed to load profile with second manager: %v", err)
	}

	if loadedProfile.Profile.ID != profileID {
		t.Error("Loaded profile ID mismatch")
	}

	if loadedProfile.Profile.Name != "Persistence Test" {
		t.Error("Loaded profile name mismatch")
	}

	// Verify fingerprint data persisted
	if loadedProfile.Fingerprint == nil {
		t.Error("Fingerprint data should persist")
	}

	if loadedProfile.FingerprintHash == "" {
		t.Error("Fingerprint hash should persist")
	}
}

// Benchmark tests
func BenchmarkFingerprintManager_GenerateRandomFingerprint(b *testing.B) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_bench")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		b.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = fm.GenerateRandomFingerprint("windows")
	}
}

func BenchmarkFingerprintManager_CreateFingerprintProfile(b *testing.B) {
	tempDir, err := os.MkdirTemp("", "fingerprint_manager_bench")
	if err != nil {
		b.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	pm, err := NewProfileManager(tempDir)
	if err != nil {
		b.Fatalf("Failed to create profile manager: %v", err)
	}

	fm := NewFingerprintManager(pm)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		profileName := fmt.Sprintf("Bench Profile %d", i)
		_, _ = fm.CreateFingerprintProfile(profileName, "Benchmark profile", nil)
	}
}
