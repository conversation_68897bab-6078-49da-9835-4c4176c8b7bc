package chrome

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestConfigManager_SaveProfile(t *testing.T) {
	// Create temporary directory for testing
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Create test profile
	profile := &Profile{
		ID:          "test-profile",
		Name:        "Test Profile",
		Description: "A test profile",
		UserDataDir: "/test/data",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	// Test saving profile
	err = cm.SaveProfile(profile)
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}

	// Verify file was created
	profileFile := filepath.Join(tempDir, "test-profile.json")
	if _, err := os.Stat(profileFile); os.IsNotExist(err) {
		t.Fatalf("Profile file was not created")
	}

	// Verify file contents
	data, err := os.ReadFile(profileFile)
	if err != nil {
		t.Fatalf("Failed to read profile file: %v", err)
	}

	var savedProfile Profile
	err = json.Unmarshal(data, &savedProfile)
	if err != nil {
		t.Fatalf("Failed to unmarshal profile: %v", err)
	}

	if savedProfile.ID != profile.ID {
		t.Errorf("Expected ID %s, got %s", profile.ID, savedProfile.ID)
	}
	if savedProfile.Name != profile.Name {
		t.Errorf("Expected Name %s, got %s", profile.Name, savedProfile.Name)
	}
}

func TestConfigManager_SaveProfile_NilProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	err = cm.SaveProfile(nil)
	if err == nil {
		t.Fatal("Expected error when saving nil profile")
	}
}

func TestConfigManager_LoadProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Create and save test profile
	originalProfile := &Profile{
		ID:          "test-load",
		Name:        "Load Test Profile",
		Description: "Profile for load testing",
		UserDataDir: "/test/load/data",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	err = cm.SaveProfile(originalProfile)
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}

	// Test loading profile
	loadedProfile, err := cm.LoadProfile("test-load")
	if err != nil {
		t.Fatalf("Failed to load profile: %v", err)
	}

	if loadedProfile.ID != originalProfile.ID {
		t.Errorf("Expected ID %s, got %s", originalProfile.ID, loadedProfile.ID)
	}
	if loadedProfile.Name != originalProfile.Name {
		t.Errorf("Expected Name %s, got %s", originalProfile.Name, loadedProfile.Name)
	}
	if loadedProfile.Description != originalProfile.Description {
		t.Errorf("Expected Description %s, got %s", originalProfile.Description, loadedProfile.Description)
	}
}

func TestConfigManager_LoadProfile_NotFound(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	_, err = cm.LoadProfile("non-existent")
	if err == nil {
		t.Fatal("Expected error when loading non-existent profile")
	}
}

func TestConfigManager_LoadProfile_EmptyID(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	_, err = cm.LoadProfile("")
	if err == nil {
		t.Fatal("Expected error when loading profile with empty ID")
	}
}

func TestConfigManager_LoadAllProfiles(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Create multiple test profiles
	profiles := []*Profile{
		{
			ID:          "profile1",
			Name:        "Profile 1",
			Description: "First profile",
			UserDataDir: "/test/data1",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			Status:      ProfileStatusStopped,
		},
		{
			ID:          "profile2",
			Name:        "Profile 2",
			Description: "Second profile",
			UserDataDir: "/test/data2",
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
			Status:      ProfileStatusStopped,
		},
	}

	// Save all profiles
	for _, profile := range profiles {
		err = cm.SaveProfile(profile)
		if err != nil {
			t.Fatalf("Failed to save profile %s: %v", profile.ID, err)
		}
	}

	// Load all profiles
	loadedProfiles, err := cm.LoadAllProfiles()
	if err != nil {
		t.Fatalf("Failed to load all profiles: %v", err)
	}

	if len(loadedProfiles) != len(profiles) {
		t.Errorf("Expected %d profiles, got %d", len(profiles), len(loadedProfiles))
	}

	// Verify profile IDs are present
	profileIDs := make(map[string]bool)
	for _, profile := range loadedProfiles {
		profileIDs[profile.ID] = true
	}

	for _, originalProfile := range profiles {
		if !profileIDs[originalProfile.ID] {
			t.Errorf("Profile %s not found in loaded profiles", originalProfile.ID)
		}
	}
}

func TestConfigManager_DeleteProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Create and save test profile
	profile := &Profile{
		ID:          "test-delete",
		Name:        "Delete Test Profile",
		UserDataDir: "/test/delete/data",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	err = cm.SaveProfile(profile)
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}

	// Verify profile exists
	if !cm.ProfileExists("test-delete") {
		t.Fatal("Profile should exist before deletion")
	}

	// Delete profile
	err = cm.DeleteProfile("test-delete")
	if err != nil {
		t.Fatalf("Failed to delete profile: %v", err)
	}

	// Verify profile no longer exists
	if cm.ProfileExists("test-delete") {
		t.Fatal("Profile should not exist after deletion")
	}
}

func TestConfigManager_DeleteProfile_NotFound(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	err = cm.DeleteProfile("non-existent")
	if err == nil {
		t.Fatal("Expected error when deleting non-existent profile")
	}
}

func TestConfigManager_ProfileExists(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Test non-existent profile
	if cm.ProfileExists("non-existent") {
		t.Error("ProfileExists should return false for non-existent profile")
	}

	// Test empty ID
	if cm.ProfileExists("") {
		t.Error("ProfileExists should return false for empty ID")
	}

	// Create and save test profile
	profile := &Profile{
		ID:          "test-exists",
		Name:        "Exists Test Profile",
		UserDataDir: "/test/exists/data",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	err = cm.SaveProfile(profile)
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}

	// Test existing profile
	if !cm.ProfileExists("test-exists") {
		t.Error("ProfileExists should return true for existing profile")
	}
}

func TestConfigManager_ExportProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Create and save test profile
	profile := &Profile{
		ID:          "test-export",
		Name:        "Export Test Profile",
		Description: "Profile for export testing",
		UserDataDir: "/test/export/data",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	err = cm.SaveProfile(profile)
	if err != nil {
		t.Fatalf("Failed to save profile: %v", err)
	}

	// Export profile
	exportPath := filepath.Join(tempDir, "export.json")
	err = cm.ExportProfile("test-export", exportPath)
	if err != nil {
		t.Fatalf("Failed to export profile: %v", err)
	}

	// Verify export file exists
	if _, err := os.Stat(exportPath); os.IsNotExist(err) {
		t.Fatal("Export file was not created")
	}

	// Verify export file contents
	data, err := os.ReadFile(exportPath)
	if err != nil {
		t.Fatalf("Failed to read export file: %v", err)
	}

	var exportData ExportData
	err = json.Unmarshal(data, &exportData)
	if err != nil {
		t.Fatalf("Failed to unmarshal export data: %v", err)
	}

	if exportData.Version != ExportDataVersion {
		t.Errorf("Expected version %s, got %s", ExportDataVersion, exportData.Version)
	}
	if exportData.Profile.ID != profile.ID {
		t.Errorf("Expected profile ID %s, got %s", profile.ID, exportData.Profile.ID)
	}
}

func TestConfigManager_ImportProfile(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "chrome_config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	cm := NewConfigManager(tempDir)

	// Create export data
	profile := &Profile{
		ID:          "test-import",
		Name:        "Import Test Profile",
		Description: "Profile for import testing",
		UserDataDir: "/test/import/data",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Status:      ProfileStatusStopped,
	}

	exportData := &ExportData{
		Version:    ExportDataVersion,
		Profile:    profile,
		ExportedAt: time.Now(),
	}

	// Save export data to file
	importPath := filepath.Join(tempDir, "import.json")
	data, err := json.MarshalIndent(exportData, "", "  ")
	if err != nil {
		t.Fatalf("Failed to marshal export data: %v", err)
	}

	err = os.WriteFile(importPath, data, 0644)
	if err != nil {
		t.Fatalf("Failed to write import file: %v", err)
	}

	// Import profile
	options := &ImportOptions{
		OverwriteExisting: true,
	}

	importedProfile, err := cm.ImportProfile(importPath, options)
	if err != nil {
		t.Fatalf("Failed to import profile: %v", err)
	}

	if importedProfile.ID != profile.ID {
		t.Errorf("Expected imported profile ID %s, got %s", profile.ID, importedProfile.ID)
	}
	if importedProfile.Name != profile.Name {
		t.Errorf("Expected imported profile name %s, got %s", profile.Name, importedProfile.Name)
	}

	// Verify profile was saved
	if !cm.ProfileExists(profile.ID) {
		t.Error("Imported profile should exist")
	}
}
