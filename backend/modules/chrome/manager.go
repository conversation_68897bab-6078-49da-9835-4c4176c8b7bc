package chrome

import (
	"fmt"
	"os"
	"path/filepath"
	"sort"
	"strconv"
	"sync"
	"time"
)

var (
	// 全局自增ID计数器
	globalProfileIDCounter uint64
	// 互斥锁保证线程安全
	profileIDMutex sync.Mutex
	// ID文件路径
	profileIDFile string
	// 初始化标志
	profileIDInitialized bool
)

// ProfileManager implements the Manager interface
type ProfileManager struct {
	configManager  *ConfigManager
	processManager *ProcessManager
	dataDir        string
	platform       *PlatformInfo
}

// NewProfileManager creates a new profile manager
func NewProfileManager(customDataDir ...string) (*ProfileManager, error) {
	platform, err := GetPlatformInfo()
	if err != nil {
		return nil, fmt.Errorf("failed to get platform info: %w", err)
	}

	var dataDir string
	if len(customDataDir) > 0 && customDataDir[0] != "" {
		dataDir = customDataDir[0]
	} else {
		dataDir = platform.DefaultDataDir
	}

	// Ensure data directory exists
	if err := EnsureDirectoryExists(dataDir); err != nil {
		return nil, fmt.Errorf("failed to create data directory: %w", err)
	}

	// Create config subdirectory for profile configurations
	configDir := filepath.Join(dataDir, "configs")
	if err := EnsureDirectoryExists(configDir); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %w", err)
	}

	configManager := NewConfigManager(configDir)

	// Create process manager
	processManager, err := NewProcessManager()
	if err != nil {
		return nil, fmt.Errorf("failed to create process manager: %w", err)
	}

	// Initialize profile ID counter
	if err := initializeProfileIDCounter(dataDir); err != nil {
		return nil, fmt.Errorf("failed to initialize profile ID counter: %w", err)
	}

	return &ProfileManager{
		configManager:  configManager,
		processManager: processManager,
		dataDir:        dataDir,
		platform:       platform,
	}, nil
}

// CreateProfile creates a new profile with the given name and description
func (pm *ProfileManager) CreateProfile(name, description string) (*Profile, error) {
	if name == "" {
		return nil, ErrInvalidArgument
	}

	if len(name) > MaxProfileNameLength {
		return nil, NewValidationError("name", name, "max_length", "name too long")
	}

	// Generate unique profile ID
	profileID := generateProfileID(name)

	// Ensure profile ID is unique
	counter := 1
	originalID := profileID
	for pm.configManager.ProfileExists(profileID) {
		profileID = fmt.Sprintf("%s_%d", originalID, counter)
		counter++
	}

	// Create profile data directory
	profileDataDir := filepath.Join(pm.dataDir, "profiles", profileID)
	if err := EnsureDirectoryExists(profileDataDir); err != nil {
		return nil, fmt.Errorf("failed to create profile data directory: %w", err)
	}

	now := time.Now()
	profile := &Profile{
		ID:           profileID,
		Name:         name,
		Description:  description,
		UserDataDir:  profileDataDir,
		LaunchParams: make(map[string]string),
		CreatedAt:    now,
		UpdatedAt:    now,
		Status:       ProfileStatusStopped,
	}

	// Save profile configuration
	if err := pm.configManager.SaveProfile(profile); err != nil {
		// Clean up data directory if profile save fails
		os.RemoveAll(profileDataDir)
		return nil, fmt.Errorf("failed to save profile: %w", err)
	}

	return profile, nil
}

// DeleteProfile deletes a profile and its data
func (pm *ProfileManager) DeleteProfile(profileID string) error {
	if profileID == "" {
		return ErrInvalidArgument
	}

	// Check if profile exists
	profile, err := pm.configManager.LoadProfile(profileID)
	if err != nil {
		return err
	}

	// Check if profile is running
	if profile.Status == ProfileStatusRunning || profile.Status == ProfileStatusStarting {
		return ErrProfileInUse
	}

	// Remove profile data directory
	if profile.UserDataDir != "" && IsValidPath(profile.UserDataDir) {
		if err := os.RemoveAll(profile.UserDataDir); err != nil {
			// Log warning but continue with config deletion
		}
	}

	// Remove profile configuration
	if err := pm.configManager.DeleteProfile(profileID); err != nil {
		return fmt.Errorf("failed to delete profile config: %w", err)
	}

	return nil
}

// ListProfiles returns all profiles sorted by last used date
func (pm *ProfileManager) ListProfiles() ([]*Profile, error) {
	profiles, err := pm.configManager.LoadAllProfiles()
	if err != nil {
		return nil, fmt.Errorf("failed to load profiles: %w", err)
	}

	// Sort profiles by last used date (most recent first)
	sort.Slice(profiles, func(i, j int) bool {
		// Handle nil LastUsed dates
		if profiles[i].LastUsed == nil && profiles[j].LastUsed == nil {
			return profiles[i].UpdatedAt.After(profiles[j].UpdatedAt)
		}
		if profiles[i].LastUsed == nil {
			return false
		}
		if profiles[j].LastUsed == nil {
			return true
		}
		return profiles[i].LastUsed.After(*profiles[j].LastUsed)
	})

	return profiles, nil
}

// GetProfile returns a specific profile by ID
func (pm *ProfileManager) GetProfile(profileID string) (*Profile, error) {
	if profileID == "" {
		return nil, ErrInvalidArgument
	}

	return pm.configManager.LoadProfile(profileID)
}

// RenameProfile renames a profile
func (pm *ProfileManager) RenameProfile(profileID, newName string) error {
	if profileID == "" || newName == "" {
		return ErrInvalidArgument
	}

	if len(newName) > MaxProfileNameLength {
		return NewValidationError("name", newName, "max_length", "name too long")
	}

	profile, err := pm.configManager.LoadProfile(profileID)
	if err != nil {
		return err
	}

	profile.Name = newName
	profile.UpdatedAt = time.Now()

	return pm.configManager.SaveProfile(profile)
}

// SetProfileIcon sets or updates the icon path for a profile
func (pm *ProfileManager) SetProfileIcon(profileID, iconPath string) error {
	if profileID == "" {
		return ErrInvalidArgument
	}

	profile, err := pm.configManager.LoadProfile(profileID)
	if err != nil {
		return err
	}

	// Validate icon path if provided
	if iconPath != "" && !IsValidPath(iconPath) {
		return NewValidationError("iconPath", iconPath, "invalid_path", "invalid icon path")
	}

	profile.IconPath = iconPath
	profile.UpdatedAt = time.Now()

	return pm.configManager.SaveProfile(profile)
}

// LaunchProfile launches a Chrome instance with the specified profile
func (pm *ProfileManager) LaunchProfile(profileID string, options *LaunchOptions) (*ProcessInfo, error) {
	if profileID == "" {
		return nil, ErrInvalidArgument
	}

	// Load the profile
	profile, err := pm.configManager.LoadProfile(profileID)
	if err != nil {
		return nil, err
	}

	// Launch the Chrome instance
	processInfo, err := pm.processManager.LaunchProfile(profile, options)
	if err != nil {
		return nil, err
	}

	// Update profile status and process ID
	profile.Status = ProfileStatusRunning
	profile.ProcessID = processInfo.PID
	profile.LastUsed = &processInfo.StartTime
	profile.UpdatedAt = time.Now()

	// Save updated profile
	if saveErr := pm.configManager.SaveProfile(profile); saveErr != nil {
		// Log the error but don't fail the launch
		// In a production system, we might want to log this properly
	}

	return processInfo, nil
}

// StopProfile stops a running Chrome instance for the specified profile
func (pm *ProfileManager) StopProfile(profileID string) error {
	if profileID == "" {
		return ErrInvalidArgument
	}

	// Stop the Chrome process
	err := pm.processManager.StopProfile(profileID)
	if err != nil {
		return err
	}

	// Update profile status
	profile, loadErr := pm.configManager.LoadProfile(profileID)
	if loadErr == nil {
		profile.Status = ProfileStatusStopped
		profile.ProcessID = 0
		profile.UpdatedAt = time.Now()
		pm.configManager.SaveProfile(profile)
	}

	return nil
}

// GetProfileStatus returns the current status of a profile
func (pm *ProfileManager) GetProfileStatus(profileID string) (ProfileStatus, error) {
	if profileID == "" {
		return ProfileStatusStopped, ErrInvalidArgument
	}

	// Check process manager first for running processes
	status, err := pm.processManager.GetProcessStatus(profileID)
	if err != nil {
		return ProfileStatusStopped, err
	}

	// If not running, check stored profile status
	if status == ProfileStatusStopped {
		profile, err := pm.configManager.LoadProfile(profileID)
		if err != nil {
			return ProfileStatusStopped, err
		}

		// Update profile status if it was marked as running but process is stopped
		if profile.Status != ProfileStatusStopped {
			profile.Status = ProfileStatusStopped
			profile.ProcessID = 0
			profile.UpdatedAt = time.Now()
			pm.configManager.SaveProfile(profile)
		}
	}

	return status, nil
}

// ListRunningProfiles returns information about all running Chrome profiles
func (pm *ProfileManager) ListRunningProfiles() ([]*ProcessInfo, error) {
	return pm.processManager.ListRunningProcesses()
}

// ExportProfile exports a profile configuration to a file
func (pm *ProfileManager) ExportProfile(profileID string) (*ExportData, error) {
	profile, err := pm.configManager.LoadProfile(profileID)
	if err != nil {
		return nil, err
	}

	exportData := &ExportData{
		Version:    ExportDataVersion,
		Profile:    profile,
		ExportedAt: time.Now(),
		Metadata: map[string]string{
			"platform": string(pm.platform.OS),
			"version":  ExportDataVersion,
		},
	}

	return exportData, nil
}

// ImportProfile imports a profile from export data
func (pm *ProfileManager) ImportProfile(data *ExportData, options *ImportOptions) (*Profile, error) {
	if data == nil || data.Profile == nil {
		return nil, ErrInvalidArgument
	}

	profile := data.Profile
	originalID := profile.ID

	// Handle import options
	if options != nil {
		if options.NewProfileID != "" {
			profile.ID = options.NewProfileID
		}

		if !options.OverwriteExisting && pm.configManager.ProfileExists(profile.ID) {
			return nil, fmt.Errorf("profile %s already exists", profile.ID)
		}
	}

	// Ensure unique profile ID
	if profile.ID == originalID {
		counter := 1
		for pm.configManager.ProfileExists(profile.ID) {
			profile.ID = fmt.Sprintf("%s_imported_%d", originalID, counter)
			counter++
		}
	}

	// Create new profile data directory
	profileDataDir := filepath.Join(pm.dataDir, "profiles", profile.ID)
	if err := EnsureDirectoryExists(profileDataDir); err != nil {
		return nil, fmt.Errorf("failed to create profile data directory: %w", err)
	}

	// Update profile paths and timestamps
	profile.UserDataDir = profileDataDir
	profile.UpdatedAt = time.Now()
	profile.Status = ProfileStatusStopped
	profile.ProcessID = 0
	profile.LastUsed = nil

	// Save imported profile
	if err := pm.configManager.SaveProfile(profile); err != nil {
		os.RemoveAll(profileDataDir)
		return nil, fmt.Errorf("failed to save imported profile: %w", err)
	}

	return profile, nil
}

// BackupProfile creates a backup of a profile
func (pm *ProfileManager) BackupProfile(profileID, backupPath string) error {
	if profileID == "" || backupPath == "" {
		return ErrInvalidArgument
	}

	return pm.configManager.BackupProfile(profileID, backupPath)
}

// CleanupUnusedProfiles removes profiles that haven't been used in a long time
func (pm *ProfileManager) CleanupUnusedProfiles() error {
	profiles, err := pm.ListProfiles()
	if err != nil {
		return err
	}

	cutoffTime := time.Now().AddDate(0, -6, 0) // 6 months ago

	for _, profile := range profiles {
		// Skip if profile is running
		if profile.Status == ProfileStatusRunning || profile.Status == ProfileStatusStarting {
			continue
		}

		// Check if profile hasn't been used in 6 months
		var lastActivity time.Time
		if profile.LastUsed != nil {
			lastActivity = *profile.LastUsed
		} else {
			lastActivity = profile.UpdatedAt
		}

		if lastActivity.Before(cutoffTime) {
			// TODO: In a real implementation, we might want to prompt the user
			// or move to a "archived" state instead of deleting
			_ = pm.DeleteProfile(profile.ID)
		}
	}

	return nil
}

// CleanupTempFiles removes temporary files and directories
func (pm *ProfileManager) CleanupTempFiles() error {
	// Clean up any temporary files in the data directory
	tempDir := filepath.Join(pm.dataDir, "temp")
	if _, err := os.Stat(tempDir); err == nil {
		if err := os.RemoveAll(tempDir); err != nil {
			return fmt.Errorf("failed to remove temp directory: %w", err)
		}
	}

	return nil
}

// GetDataDirectory returns the data directory path
func (pm *ProfileManager) GetDataDirectory() string {
	return pm.dataDir
}

// GetPlatformInfo returns platform information
func (pm *ProfileManager) GetPlatformInfo() *PlatformInfo {
	return pm.platform
}

// initializeProfileIDCounter 初始化全局自增ID计数器
func initializeProfileIDCounter(dataDir string) error {
	profileIDMutex.Lock()
	defer profileIDMutex.Unlock()

	if profileIDInitialized {
		return nil
	}

	// 设置ID文件路径
	profileIDFile = filepath.Join(dataDir, "configs", "profile_id_counter")

	// 尝试从文件读取当前计数器值
	if data, err := os.ReadFile(profileIDFile); err == nil {
		if counter, parseErr := strconv.ParseUint(string(data), 10, 64); parseErr == nil {
			globalProfileIDCounter = counter
		}
	}

	// 如果文件不存在或读取失败，从1开始
	if globalProfileIDCounter == 0 {
		globalProfileIDCounter = 1
	}

	profileIDInitialized = true
	return nil
}

// getNextProfileID 获取下一个自增ID并持久化
func getNextProfileID() (uint64, error) {
	profileIDMutex.Lock()
	defer profileIDMutex.Unlock()

	// 获取当前ID
	currentID := globalProfileIDCounter

	// 递增计数器
	globalProfileIDCounter++

	// 持久化到文件
	if err := os.WriteFile(profileIDFile, []byte(strconv.FormatUint(globalProfileIDCounter, 10)), 0644); err != nil {
		// 如果写入失败，回滚计数器
		globalProfileIDCounter = currentID
		return 0, fmt.Errorf("failed to persist profile ID counter: %w", err)
	}

	return currentID, nil
}

// generateProfileID generates a profile ID from a name using global auto-increment ID
func generateProfileID(name string) string {
	// 清理名称，只保留字母、数字和基本符号
	cleanName := ""
	for _, r := range name {
		if (r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9') {
			cleanName += string(r)
		} else if r == ' ' || r == '-' || r == '_' {
			cleanName += "_"
		}
	}

	if cleanName == "" {
		cleanName = "P"
	}

	// 获取自增ID
	id, err := getNextProfileID()
	if err != nil {
		// 如果获取ID失败，回退到使用timestamp
		timestamp := time.Now().Unix()
		return fmt.Sprintf("%s_%d", cleanName, timestamp)
	}

	return fmt.Sprintf("%s_%d", cleanName, id)
}
