package chrome

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"sync"
	"time"
)

// ProcessManager manages Chrome process instances
type ProcessManager struct {
	mu        sync.RWMutex
	processes map[string]*ProcessInfo
	platform  *PlatformInfo
}

// NewProcessManager creates a new process manager
func NewProcessManager() (*ProcessManager, error) {
	platform, err := GetPlatformInfo()
	if err != nil {
		return nil, fmt.Errorf("failed to get platform info: %w", err)
	}

	return &ProcessManager{
		processes: make(map[string]*ProcessInfo),
		platform:  platform,
	}, nil
}

// LaunchProfile launches a Chrome instance for a given profile
func (pm *ProcessManager) LaunchProfile(profile *Profile, options *LaunchOptions) (*ProcessInfo, error) {
	if profile == nil {
		return nil, ErrInvalidArgument
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	// Check if profile is already running
	if existingProcess, exists := pm.processes[profile.ID]; exists {
		if IsProcessRunning(existingProcess.PID) {
			return nil, ErrChromeAlreadyRunning
		}
		// Clean up stale process info
		delete(pm.processes, profile.ID)
	}

	// Prepare Chrome arguments
	args, err := pm.buildChromeArgs(profile, options)
	if err != nil {
		return nil, fmt.Errorf("failed to build Chrome arguments: %w", err)
	}

	// Start Chrome process
	ctx := context.Background()
	if options != nil && options.Timeout > 0 {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, options.Timeout)
		defer cancel()
	}

	cmd := exec.CommandContext(ctx, pm.platform.ChromeExecutable, args...)

	// Set environment variables
	if options != nil && options.Environment != nil {
		env := os.Environ()
		for key, value := range options.Environment {
			env = append(env, fmt.Sprintf("%s=%s", key, value))
		}
		cmd.Env = env
	}

	// Start the process
	if err := cmd.Start(); err != nil {
		return nil, NewProcessError(0, profile.ID, "start", err)
	}

	// Create process info
	processInfo := &ProcessInfo{
		PID:         cmd.Process.Pid,
		ProfileID:   profile.ID,
		StartTime:   time.Now(),
		Status:      ProfileStatusStarting,
		UserDataDir: profile.UserDataDir,
	}

	if options != nil {
		processInfo.DebugPort = options.DebugPort
	}

	// Store process info
	pm.processes[profile.ID] = processInfo

	// Start a goroutine to monitor the process
	go pm.monitorProcess(profile.ID, cmd.Process)

	// Wait a bit to ensure the process started successfully
	time.Sleep(100 * time.Millisecond)

	if !IsProcessRunning(cmd.Process.Pid) {
		delete(pm.processes, profile.ID)
		return nil, ErrChromeStartFailed
	}

	processInfo.Status = ProfileStatusRunning
	return processInfo, nil
}

// StopProfile stops a Chrome instance for a given profile
func (pm *ProcessManager) StopProfile(profileID string) error {
	if profileID == "" {
		return ErrInvalidArgument
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	processInfo, exists := pm.processes[profileID]
	if !exists {
		return ErrChromeNotRunning
	}

	if !IsProcessRunning(processInfo.PID) {
		delete(pm.processes, profileID)
		return ErrChromeNotRunning
	}

	process, err := GetProcessByPID(processInfo.PID)
	if err != nil {
		delete(pm.processes, profileID)
		return NewProcessError(processInfo.PID, profileID, "find", err)
	}

	// Update status to stopping
	processInfo.Status = ProfileStatusStopping

	// Try graceful shutdown first
	if err := process.Signal(os.Interrupt); err != nil {
		// If graceful shutdown fails, force kill
		if killErr := process.Kill(); killErr != nil {
			return NewProcessError(processInfo.PID, profileID, "kill", killErr)
		}
	}

	// Wait for process to terminate
	done := make(chan error, 1)
	go func() {
		_, err := process.Wait()
		done <- err
	}()

	select {
	case <-time.After(10 * time.Second):
		// Force kill if graceful shutdown takes too long
		process.Kill()
		<-done // Wait for the process to actually terminate
	case <-done:
		// Process terminated gracefully
	}

	// Clean up
	delete(pm.processes, profileID)
	return nil
}

// GetProcessStatus returns the status of a Chrome process for a given profile
func (pm *ProcessManager) GetProcessStatus(profileID string) (ProfileStatus, error) {
	if profileID == "" {
		return ProfileStatusStopped, ErrInvalidArgument
	}

	pm.mu.RLock()
	defer pm.mu.RUnlock()

	processInfo, exists := pm.processes[profileID]
	if !exists {
		return ProfileStatusStopped, nil
	}

	if !IsProcessRunning(processInfo.PID) {
		// Process is no longer running, clean up
		pm.mu.RUnlock()
		pm.mu.Lock()
		delete(pm.processes, profileID)
		pm.mu.Unlock()
		pm.mu.RLock()
		return ProfileStatusStopped, nil
	}

	return processInfo.Status, nil
}

// ListRunningProcesses returns information about all running Chrome processes
func (pm *ProcessManager) ListRunningProcesses() ([]*ProcessInfo, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	var runningProcesses []*ProcessInfo
	for profileID, processInfo := range pm.processes {
		if IsProcessRunning(processInfo.PID) {
			// Create a copy to avoid race conditions
			processCopy := *processInfo
			runningProcesses = append(runningProcesses, &processCopy)
		} else {
			// Mark for cleanup
			pm.mu.RUnlock()
			pm.mu.Lock()
			delete(pm.processes, profileID)
			pm.mu.Unlock()
			pm.mu.RLock()
		}
	}

	return runningProcesses, nil
}

// GetProcessInfo returns process information for a specific profile
func (pm *ProcessManager) GetProcessInfo(profileID string) (*ProcessInfo, error) {
	if profileID == "" {
		return nil, ErrInvalidArgument
	}

	pm.mu.RLock()
	defer pm.mu.RUnlock()

	processInfo, exists := pm.processes[profileID]
	if !exists {
		return nil, ErrChromeNotRunning
	}

	if !IsProcessRunning(processInfo.PID) {
		pm.mu.RUnlock()
		pm.mu.Lock()
		delete(pm.processes, profileID)
		pm.mu.Unlock()
		pm.mu.RLock()
		return nil, ErrChromeNotRunning
	}

	// Return a copy to avoid race conditions
	processCopy := *processInfo
	return &processCopy, nil
}

// CleanupStaleProcesses removes process entries for processes that are no longer running
func (pm *ProcessManager) CleanupStaleProcesses() {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	for profileID, processInfo := range pm.processes {
		if !IsProcessRunning(processInfo.PID) {
			delete(pm.processes, profileID)
		}
	}
}

// buildChromeArgs builds the command line arguments for Chrome
func (pm *ProcessManager) buildChromeArgs(profile *Profile, options *LaunchOptions) ([]string, error) {
	args := []string{}

	// User data directory
	args = append(args, ArgUserDataDir+"="+profile.UserDataDir)

	// Add platform-specific arguments
	platformArgs := CreatePlatformSpecificArgs()
	args = append(args, platformArgs...)

	// Debug port
	debugPort := DefaultDebugPortStart
	if options != nil && options.DebugPort > 0 {
		debugPort = options.DebugPort
	}
	args = append(args, ArgRemoteDebuggingPort+"="+strconv.Itoa(debugPort))

	// Launch parameters from profile
	if profile.LaunchParams != nil {
		for key, value := range profile.LaunchParams {
			if value == "" {
				args = append(args, key)
			} else {
				args = append(args, key+"="+value)
			}
		}
	}

	// Proxy configuration
	if profile.ProxyConfig != nil {
		proxyArg := fmt.Sprintf("%s://%s:%d",
			profile.ProxyConfig.Type,
			profile.ProxyConfig.Host,
			profile.ProxyConfig.Port)

		if profile.ProxyConfig.Username != "" && profile.ProxyConfig.Password != "" {
			proxyArg = fmt.Sprintf("%s://%s:%s@%s:%d",
				profile.ProxyConfig.Type,
				profile.ProxyConfig.Username,
				profile.ProxyConfig.Password,
				profile.ProxyConfig.Host,
				profile.ProxyConfig.Port)
		}

		args = append(args, ArgProxyServer+"="+proxyArg)
	}

	// Window configuration
	if profile.WindowConfig != nil {
		if profile.WindowConfig.Width > 0 && profile.WindowConfig.Height > 0 {
			args = append(args, ArgWindowSize+"="+
				strconv.Itoa(profile.WindowConfig.Width)+","+
				strconv.Itoa(profile.WindowConfig.Height))
		}

		if profile.WindowConfig.X >= 0 && profile.WindowConfig.Y >= 0 {
			args = append(args, ArgWindowPosition+"="+
				strconv.Itoa(profile.WindowConfig.X)+","+
				strconv.Itoa(profile.WindowConfig.Y))
		}

		if profile.WindowConfig.Maximized {
			args = append(args, ArgStartMaximized)
		}

		if profile.WindowConfig.Fullscreen {
			args = append(args, ArgStartFullscreen)
		}
	}

	// Launch options
	if options != nil {
		if options.Headless {
			args = append(args, ArgHeadless)
		}

		if options.NoSandbox {
			args = append(args, ArgNoSandbox)
		}

		if options.DisableWebSecurity {
			args = append(args, ArgDisableWebSecurity)
		}

		// Custom arguments
		if options.CustomArgs != nil {
			args = append(args, options.CustomArgs...)
		}
	}

	return args, nil
}

// monitorProcess monitors a Chrome process and updates its status
func (pm *ProcessManager) monitorProcess(profileID string, process *os.Process) {
	defer func() {
		pm.mu.Lock()
		defer pm.mu.Unlock()

		if processInfo, exists := pm.processes[profileID]; exists {
			processInfo.Status = ProfileStatusStopped
			delete(pm.processes, profileID)
		}
	}()

	// Wait for the process to exit
	process.Wait()
}

// UpdateProfile updates the profile reference in a running process (used by ProfileManager)
func (pm *ProcessManager) UpdateProfile(profile *Profile) error {
	if profile == nil {
		return ErrInvalidArgument
	}

	pm.mu.Lock()
	defer pm.mu.Unlock()

	processInfo, exists := pm.processes[profile.ID]
	if !exists {
		return nil // No running process to update
	}

	// Update the user data directory if it changed
	processInfo.UserDataDir = profile.UserDataDir

	return nil
}

// GetAllProcesses returns all process information (running and stopped)
func (pm *ProcessManager) GetAllProcesses() map[string]*ProcessInfo {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	result := make(map[string]*ProcessInfo)
	for profileID, processInfo := range pm.processes {
		processCopy := *processInfo
		result[profileID] = &processCopy
	}

	return result
}

// KillAllProcesses forcefully terminates all managed Chrome processes
func (pm *ProcessManager) KillAllProcesses() error {
	pm.mu.Lock()
	defer pm.mu.Unlock()

	var errors []error
	for profileID, processInfo := range pm.processes {
		if IsProcessRunning(processInfo.PID) {
			if process, err := GetProcessByPID(processInfo.PID); err == nil {
				if killErr := process.Kill(); killErr != nil {
					errors = append(errors, NewProcessError(processInfo.PID, profileID, "kill", killErr))
				}
			}
		}
		delete(pm.processes, profileID)
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to kill some processes: %v", errors)
	}

	return nil
}

// FindAvailableDebugPort finds an available debug port starting from the default port
func (pm *ProcessManager) FindAvailableDebugPort() (int, error) {
	pm.mu.RLock()
	defer pm.mu.RUnlock()

	usedPorts := make(map[int]bool)
	for _, processInfo := range pm.processes {
		if processInfo.DebugPort > 0 {
			usedPorts[processInfo.DebugPort] = true
		}
	}

	// Start from the default port and find the first available one
	for port := DefaultDebugPortStart; port < DefaultDebugPortStart+1000; port++ {
		if !usedPorts[port] {
			return port, nil
		}
	}

	return 0, fmt.Errorf("no available debug ports found")
}
