package chrome

import (
	"errors"
	"fmt"
)

// Common error variables
var (
	// Profile management errors
	ErrProfileNotFound    = errors.New("profile not found")
	ErrProfileExists      = errors.New("profile already exists")
	ErrProfileNameEmpty   = errors.New("profile name cannot be empty")
	ErrProfileNameTooLong = errors.New("profile name too long")
	ErrProfileIDEmpty     = errors.New("profile ID cannot be empty")
	ErrProfileIDInvalid   = errors.New("profile ID contains invalid characters")
	ErrProfileInUse       = errors.New("profile is currently in use")
	ErrProfileCorrupted   = errors.New("profile data is corrupted")

	// Chrome process errors
	ErrChromeNotFound         = errors.New("chrome executable not found")
	ErrChromeAlreadyRunning   = errors.New("chrome instance is already running for this profile")
	ErrChromeNotRunning       = errors.New("chrome instance is not running for this profile")
	ErrChromeStartFailed      = errors.New("failed to start Chrome instance")
	ErrChromeStopFailed       = errors.New("failed to stop Chrome instance")
	ErrChromeTimeout          = errors.New("chrome operation timed out")
	ErrChromeProcessNotFound  = errors.New("chrome process not found")
	ErrChromeInvalidDebugPort = errors.New("invalid chrome debug port")

	// File system errors
	ErrUserDataDirNotFound   = errors.New("user data directory not found")
	ErrUserDataDirExists     = errors.New("user data directory already exists")
	ErrUserDataDirPermission = errors.New("insufficient permissions for user data directory")
	ErrUserDataDirCorrupted  = errors.New("user data directory is corrupted")
	ErrConfigFileNotFound    = errors.New("configuration file not found")
	ErrConfigFileCorrupted   = errors.New("configuration file is corrupted")
	ErrIconFileNotFound      = errors.New("icon file not found")
	ErrIconFileInvalid       = errors.New("icon file format is invalid")

	// Import/Export errors
	ErrExportFailed          = errors.New("failed to export profile")
	ErrImportFailed          = errors.New("failed to import profile")
	ErrExportDataInvalid     = errors.New("export data is invalid")
	ErrExportVersionMismatch = errors.New("export data version mismatch")
	ErrBackupFailed          = errors.New("failed to create backup")
	ErrBackupNotFound        = errors.New("backup file not found")
	ErrBackupCorrupted       = errors.New("backup file is corrupted")

	// Proxy errors
	ErrProxyConfigInvalid        = errors.New("proxy configuration is invalid")
	ErrProxyConnectionFailed     = errors.New("proxy connection failed")
	ErrProxyAuthenticationFailed = errors.New("proxy authentication failed")

	// Platform errors
	ErrUnsupportedPlatform     = errors.New("unsupported platform")
	ErrPlatformDetectionFailed = errors.New("failed to detect platform")

	// General errors
	ErrInvalidArgument       = errors.New("invalid argument")
	ErrOperationNotSupported = errors.New("operation not supported")
	ErrNotImplemented        = errors.New("operation not implemented")
	ErrInternalError         = errors.New("internal error")
	ErrOperationCancelled    = errors.New("operation was cancelled")
	ErrResourceLocked        = errors.New("resource is locked")
)

// ProfileError represents profile-specific errors with additional context
type ProfileError struct {
	ProfileID string
	Operation string
	Err       error
}

func (e *ProfileError) Error() string {
	if e.ProfileID != "" {
		return fmt.Sprintf("profile '%s' %s: %v", e.ProfileID, e.Operation, e.Err)
	}
	return fmt.Sprintf("profile %s: %v", e.Operation, e.Err)
}

func (e *ProfileError) Unwrap() error {
	return e.Err
}

// NewProfileError creates a new ProfileError
func NewProfileError(profileID, operation string, err error) *ProfileError {
	return &ProfileError{
		ProfileID: profileID,
		Operation: operation,
		Err:       err,
	}
}

// ProcessError represents Chrome process-specific errors
type ProcessError struct {
	PID       int
	ProfileID string
	Operation string
	Err       error
}

func (e *ProcessError) Error() string {
	if e.PID > 0 {
		return fmt.Sprintf("chrome process %d (profile '%s') %s: %v", e.PID, e.ProfileID, e.Operation, e.Err)
	}
	return fmt.Sprintf("chrome process (profile '%s') %s: %v", e.ProfileID, e.Operation, e.Err)
}

func (e *ProcessError) Unwrap() error {
	return e.Err
}

// NewProcessError creates a new ProcessError
func NewProcessError(pid int, profileID, operation string, err error) *ProcessError {
	return &ProcessError{
		PID:       pid,
		ProfileID: profileID,
		Operation: operation,
		Err:       err,
	}
}

// FileSystemError represents file system operation errors
type FileSystemError struct {
	Path      string
	Operation string
	Err       error
}

func (e *FileSystemError) Error() string {
	return fmt.Sprintf("filesystem %s '%s': %v", e.Operation, e.Path, e.Err)
}

func (e *FileSystemError) Unwrap() error {
	return e.Err
}

// NewFileSystemError creates a new FileSystemError
func NewFileSystemError(path, operation string, err error) *FileSystemError {
	return &FileSystemError{
		Path:      path,
		Operation: operation,
		Err:       err,
	}
}

// ValidationError represents validation errors
type ValidationError struct {
	Field   string
	Value   interface{}
	Rule    string
	Message string
}

func (e *ValidationError) Error() string {
	if e.Message != "" {
		return e.Message
	}
	return fmt.Sprintf("validation failed for field '%s' with value '%v': %s", e.Field, e.Value, e.Rule)
}

// NewValidationError creates a new ValidationError
func NewValidationError(field string, value interface{}, rule, message string) *ValidationError {
	return &ValidationError{
		Field:   field,
		Value:   value,
		Rule:    rule,
		Message: message,
	}
}

// ConfigError represents configuration-related errors
type ConfigError struct {
	ConfigType string
	Field      string
	Err        error
}

func (e *ConfigError) Error() string {
	if e.Field != "" {
		return fmt.Sprintf("config error in %s.%s: %v", e.ConfigType, e.Field, e.Err)
	}
	return fmt.Sprintf("config error in %s: %v", e.ConfigType, e.Err)
}

func (e *ConfigError) Unwrap() error {
	return e.Err
}

// NewConfigError creates a new ConfigError
func NewConfigError(configType, field string, err error) *ConfigError {
	return &ConfigError{
		ConfigType: configType,
		Field:      field,
		Err:        err,
	}
}

// IsProfileNotFound checks if the error is a profile not found error
func IsProfileNotFound(err error) bool {
	return errors.Is(err, ErrProfileNotFound)
}

// IsProfileInUse checks if the error is a profile in use error
func IsProfileInUse(err error) bool {
	return errors.Is(err, ErrProfileInUse)
}

// IsChromeNotRunning checks if the error is a Chrome not running error
func IsChromeNotRunning(err error) bool {
	return errors.Is(err, ErrChromeNotRunning)
}

// IsChromeAlreadyRunning checks if the error is a Chrome already running error
func IsChromeAlreadyRunning(err error) bool {
	return errors.Is(err, ErrChromeAlreadyRunning)
}

// IsFileSystemError checks if the error is a file system error
func IsFileSystemError(err error) bool {
	var fsErr *FileSystemError
	return errors.As(err, &fsErr)
}

// IsValidationError checks if the error is a validation error
func IsValidationError(err error) bool {
	var valErr *ValidationError
	return errors.As(err, &valErr)
}

// IsConfigError checks if the error is a configuration error
func IsConfigError(err error) bool {
	var cfgErr *ConfigError
	return errors.As(err, &cfgErr)
}

// WrapError wraps an error with additional context
func WrapError(err error, message string) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", message, err)
}

// WrapErrorf wraps an error with formatted additional context
func WrapErrorf(err error, format string, args ...interface{}) error {
	if err == nil {
		return nil
	}
	return fmt.Errorf("%s: %w", fmt.Sprintf(format, args...), err)
}
