package chrome

import (
	"os"
	"path/filepath"
	"testing"
	"time"
)

func TestNewProcessManager(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	if pm == nil {
		t.<PERSON>al("Process manager should not be nil")
	}

	if pm.platform == nil {
		t.Fatal("Platform info should not be nil")
	}

	if pm.processes == nil {
		t.Fatal("Process map should not be nil")
	}
}

func TestProcessManager_GetProcessStatus_NotRunning(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	status, err := pm.GetProcessStatus("non-existent")
	if err != nil {
		t.Fatalf("GetProcessStatus should not return error for non-existent profile: %v", err)
	}

	if status != ProfileStatusStopped {
		t.Errorf("Expected status %s, got %s", ProfileStatusStopped, status)
	}
}

func TestProcessManager_GetProcessStatus_EmptyID(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	_, err = pm.GetProcessStatus("")
	if err == nil {
		t.Fatal("Expected error for empty profile ID")
	}
}

func TestProcessManager_ListRunningProcesses_Empty(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	processes, err := pm.ListRunningProcesses()
	if err != nil {
		t.Fatalf("Failed to list running processes: %v", err)
	}

	if len(processes) != 0 {
		t.Errorf("Expected 0 running processes, got %d", len(processes))
	}
}

func TestProcessManager_StopProfile_NotRunning(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	err = pm.StopProfile("non-existent")
	if err == nil {
		t.Fatal("Expected error when stopping non-existent profile")
	}
}

func TestProcessManager_StopProfile_EmptyID(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	err = pm.StopProfile("")
	if err == nil {
		t.Fatal("Expected error for empty profile ID")
	}
}

func TestProcessManager_GetProcessInfo_NotRunning(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	_, err = pm.GetProcessInfo("non-existent")
	if err == nil {
		t.Fatal("Expected error for non-existent profile")
	}
}

func TestProcessManager_GetProcessInfo_EmptyID(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	_, err = pm.GetProcessInfo("")
	if err == nil {
		t.Fatal("Expected error for empty profile ID")
	}
}

func TestProcessManager_CleanupStaleProcesses(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	// This should not panic or error
	pm.CleanupStaleProcesses()
}

func TestProcessManager_GetAllProcesses_Empty(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	processes := pm.GetAllProcesses()
	if len(processes) != 0 {
		t.Errorf("Expected 0 processes, got %d", len(processes))
	}
}

func TestProcessManager_KillAllProcesses_Empty(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	err = pm.KillAllProcesses()
	if err != nil {
		t.Errorf("KillAllProcesses should not error when no processes: %v", err)
	}
}

func TestProcessManager_FindAvailableDebugPort(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	port, err := pm.FindAvailableDebugPort()
	if err != nil {
		t.Fatalf("Failed to find available debug port: %v", err)
	}

	if port < DefaultDebugPortStart {
		t.Errorf("Port should be >= %d, got %d", DefaultDebugPortStart, port)
	}
}

func TestProcessManager_UpdateProfile(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	// Test with nil profile
	err = pm.UpdateProfile(nil)
	if err == nil {
		t.Fatal("Expected error for nil profile")
	}

	// Test with valid profile (no running process)
	profile := &Profile{
		ID:          "test-profile",
		Name:        "Test Profile",
		UserDataDir: "/test/path",
	}

	err = pm.UpdateProfile(profile)
	if err != nil {
		t.Errorf("UpdateProfile should not error for valid profile: %v", err)
	}
}

func TestProcessManager_buildChromeArgs(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	// Create a test profile
	profile := &Profile{
		ID:          "test-profile",
		Name:        "Test Profile",
		UserDataDir: "/test/path",
		LaunchParams: map[string]string{
			"--disable-extensions": "",
			"--custom-flag":        "value",
		},
		ProxyConfig: &ProxyConfig{
			Type: "http",
			Host: "proxy.example.com",
			Port: 8080,
		},
		WindowConfig: &WindowConfig{
			Width:  1024,
			Height: 768,
			X:      100,
			Y:      100,
		},
	}

	options := &LaunchOptions{
		Headless:           true,
		NoSandbox:          true,
		DisableWebSecurity: true,
		DebugPort:          9999,
		CustomArgs:         []string{"--test-arg"},
		Environment: map[string]string{
			"TEST_VAR": "test_value",
		},
	}

	args, err := pm.buildChromeArgs(profile, options)
	if err != nil {
		t.Fatalf("Failed to build Chrome args: %v", err)
	}

	// Check that required arguments are present
	expectedArgs := []string{
		"--user-data-dir=/test/path",
		"--remote-debugging-port=9999",
		"--disable-extensions",
		"--custom-flag=value",
		"--proxy-server=http://proxy.example.com:8080",
		"--window-size=1024,768",
		"--window-position=100,100",
		"--headless",
		"--no-sandbox",
		"--disable-web-security",
		"--test-arg",
	}

	for _, expectedArg := range expectedArgs {
		found := false
		for _, arg := range args {
			if arg == expectedArg {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("Expected argument '%s' not found in args: %v", expectedArg, args)
		}
	}
}

func TestProcessManager_buildChromeArgs_MinimalProfile(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	// Minimal profile
	profile := &Profile{
		ID:          "minimal-profile",
		Name:        "Minimal Profile",
		UserDataDir: "/minimal/path",
	}

	args, err := pm.buildChromeArgs(profile, nil)
	if err != nil {
		t.Fatalf("Failed to build Chrome args for minimal profile: %v", err)
	}

	// Should at least have user data dir and debug port
	userDataDirFound := false
	debugPortFound := false

	for _, arg := range args {
		if arg == "--user-data-dir=/minimal/path" {
			userDataDirFound = true
		}
		if arg == "--remote-debugging-port=9222" { // Default debug port
			debugPortFound = true
		}
	}

	if !userDataDirFound {
		t.Error("User data dir argument not found")
	}
	if !debugPortFound {
		t.Error("Debug port argument not found")
	}
}

func TestProcessManager_buildChromeArgs_ProxyWithAuth(t *testing.T) {
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	profile := &Profile{
		ID:          "proxy-profile",
		Name:        "Proxy Profile",
		UserDataDir: "/proxy/path",
		ProxyConfig: &ProxyConfig{
			Type:     "http",
			Host:     "proxy.example.com",
			Port:     8080,
			Username: "user",
			Password: "pass",
		},
	}

	args, err := pm.buildChromeArgs(profile, nil)
	if err != nil {
		t.Fatalf("Failed to build Chrome args with proxy auth: %v", err)
	}

	proxyArgFound := false
	expectedProxyArg := "--proxy-server=http://user:<EMAIL>:8080"

	for _, arg := range args {
		if arg == expectedProxyArg {
			proxyArgFound = true
			break
		}
	}

	if !proxyArgFound {
		t.Errorf("Expected proxy argument '%s' not found in args: %v", expectedProxyArg, args)
	}
}

// Mock Chrome test - only run if Chrome is available and we're in test environment
func TestProcessManager_LaunchProfile_MockProcess(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping Chrome launch test in short mode")
	}

	// Check if Chrome is available
	pm, err := NewProcessManager()
	if err != nil {
		t.Fatalf("Failed to create process manager: %v", err)
	}

	if pm.platform.ChromeExecutable == "" {
		t.Skip("Chrome executable not found, skipping launch test")
	}

	// Create a temporary directory for the test profile
	tempDir, err := os.MkdirTemp("", "chrome_process_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	profile := &Profile{
		ID:          "test-launch",
		Name:        "Test Launch Profile",
		UserDataDir: filepath.Join(tempDir, "profile"),
		Status:      ProfileStatusStopped,
	}

	// Create user data directory
	if err := os.MkdirAll(profile.UserDataDir, 0755); err != nil {
		t.Fatalf("Failed to create user data dir: %v", err)
	}

	options := &LaunchOptions{
		Headless:  true, // Use headless mode for testing
		NoSandbox: true, // Often needed in test environments
		Timeout:   5 * time.Second,
	}

	// Note: This test might fail in environments without proper Chrome setup
	// In a real CI environment, you might want to mock the exec.Command
	processInfo, err := pm.LaunchProfile(profile, options)
	if err != nil {
		t.Logf("Chrome launch failed (expected in test environment): %v", err)
		return // Don't fail the test, just log
	}

	if processInfo == nil {
		t.Fatal("Process info should not be nil")
	}

	if processInfo.ProfileID != profile.ID {
		t.Errorf("Expected profile ID %s, got %s", profile.ID, processInfo.ProfileID)
	}

	// Clean up - try to stop the process
	stopErr := pm.StopProfile(profile.ID)
	if stopErr != nil {
		t.Logf("Failed to stop Chrome process: %v", stopErr)
	}
}
