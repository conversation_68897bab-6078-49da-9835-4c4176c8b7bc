package main

import (
	"fmt"
	"log"
	"time"

	"a8.tools/backend/modules/chrome"
	"a8.tools/backend/modules/rod"
)

func main() {
	// 基础示例
	fmt.Println("=== 指纹浏览器基础示例 ===")
	if err := basicExample(); err != nil {
		log.Printf("基础示例失败: %v", err)
	}

	// 高级示例
	fmt.Println("\n=== 指纹浏览器高级示例 ===")
	if err := advancedExample(); err != nil {
		log.Printf("高级示例失败: %v", err)
	}

	// 模板示例
	fmt.Println("\n=== 指纹模板示例 ===")
	if err := templateExample(); err != nil {
		log.Printf("模板示例失败: %v", err)
	}

	// 测试示例
	fmt.Println("\n=== 指纹测试示例 ===")
	if err := testExample(); err != nil {
		log.Printf("测试示例失败: %v", err)
	}

	demoFingerprint()
}

// basicExample 基础使用示例
func basicExample() error {
	// 创建指纹浏览器管理器
	fbm, err := chrome.NewFingerprintBrowserManager("./browser_data")
	if err != nil {
		return fmt.Errorf("创建指纹浏览器管理器失败: %w", err)
	}
	defer fbm.CloseAllSessions()

	// 生成随机指纹
	fingerprint, err := fbm.FingerprintManager.GenerateRandomFingerprint("windows")
	if err != nil {
		return fmt.Errorf("生成随机指纹失败: %w", err)
	}

	fmt.Printf("生成的指纹信息:\n")
	fmt.Printf("  用户代理: %s\n", fingerprint.UserAgent)
	fmt.Printf("  屏幕分辨率: %dx%d\n", fingerprint.ScreenResolution.Width, fingerprint.ScreenResolution.Height)
	fmt.Printf("  语言: %s\n", fingerprint.Language)
	fmt.Printf("  时区: %s\n", fingerprint.Timezone)
	fmt.Printf("  硬件并发: %d\n", fingerprint.Hardware.HardwareConcurrency)
	fmt.Printf("  WebRTC模式: %s\n", fingerprint.WebRTC.Mode)

	// 创建指纹配置文件
	profile, err := fbm.FingerprintManager.CreateFingerprintProfile(
		"测试指纹配置",
		"用于测试的指纹浏览器配置",
		fingerprint,
	)
	if err != nil {
		return fmt.Errorf("创建指纹配置文件失败: %w", err)
	}

	fmt.Printf("创建指纹配置文件成功: %s (ID: %s)\n", profile.Profile.Name, profile.Profile.ID)

	// 启动自动化会话
	launchOptions := &chrome.LaunchOptionsFingerprint{
		LaunchOptions: &chrome.LaunchOptions{
			Headless: false, // 显示浏览器窗口以便观察
			Timeout:  30 * time.Second,
		},
		ApplyFingerprint: true,
		StealthMode:      true,
		AntiDetectLevel:  "high",
	}

	session, err := fbm.LaunchWithAutomation(profile.Profile.ID, launchOptions)
	if err != nil {
		return fmt.Errorf("启动自动化会话失败: %w", err)
	}

	fmt.Printf("启动自动化会话成功: %s\n", session.SessionID)

	// 创建自动化任务
	plan, err := fbm.CreateAutomationPlan(session.SessionID, "基础测试", "测试指纹是否生效")
	if err != nil {
		return fmt.Errorf("创建自动化计划失败: %w", err)
	}

	// 添加任务
	if err := session.BrowserAutomation.AddNavigateTask(plan, "https://whatismyipaddress.com/", "访问IP检测页面"); err != nil {
		return fmt.Errorf("添加导航任务失败: %w", err)
	}

	session.BrowserAutomation.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "10s", "等待页面加载")
	session.BrowserAutomation.AddSleepTask(plan, "3s", "等待页面完全加载")
	session.BrowserAutomation.AddFullScreenshotTask(plan, "fingerprint_basic_test.png", "截图验证")

	// 获取一些基础信息
	session.BrowserAutomation.AddEvaluateTask(plan, "navigator.userAgent", "user_agent", "获取用户代理")
	session.BrowserAutomation.AddEvaluateTask(plan, "screen.width + 'x' + screen.height", "screen_resolution", "获取屏幕分辨率")
	session.BrowserAutomation.AddEvaluateTask(plan, "navigator.platform", "platform", "获取平台信息")

	// 执行任务
	if err := fbm.ExecuteAutomationPlan(session.SessionID, plan); err != nil {
		return fmt.Errorf("执行任务失败: %w", err)
	}

	// 获取执行结果
	variables := session.BrowserAutomation.GetVariables()
	fmt.Printf("执行结果:\n")
	fmt.Printf("  用户代理: %v\n", variables["user_agent"])
	fmt.Printf("  屏幕分辨率: %v\n", variables["screen_resolution"])
	fmt.Printf("  平台信息: %v\n", variables["platform"])

	fmt.Println("基础示例执行成功！")

	// 等待一段时间以便观察
	time.Sleep(5 * time.Second)

	// 清理会话
	return fbm.CloseSession(session.SessionID)
}

// advancedExample 高级使用示例
func advancedExample() error {
	fbm, err := chrome.NewFingerprintBrowserManager("./browser_data")
	if err != nil {
		return fmt.Errorf("创建指纹浏览器管理器失败: %w", err)
	}
	defer fbm.CloseAllSessions()

	// 创建自定义指纹配置
	customFingerprint := &chrome.FingerprintConfig{
		UserAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
		Language:  "zh-CN",
		Timezone:  "Asia/Shanghai",
		ScreenResolution: chrome.ScreenConfig{
			Width:             1920,
			Height:            1080,
			DeviceScaleFactor: 1.0,
			ColorDepth:        24,
			PixelDepth:        24,
			AvailWidth:        1920,
			AvailHeight:       1040,
			Orientation:       "landscape",
		},
		WebRTC: chrome.WebRTCConfig{
			Mode:              "disabled",
			LocalIPHandling:   "default",
			BlockMediaDevices: true,
		},
		Canvas: chrome.CanvasConfig{
			NoiseEnabled:    true,
			NoiseLevel:      0.1,
			RandomValues:    true,
			ConsistentNoise: true,
			NoiseType:       "gaussian",
		},
		WebGL: chrome.WebGLConfig{
			Vendor:              "Google Inc. (Intel)",
			Renderer:            "ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)",
			UnmaskedVendor:      "Intel Inc.",
			UnmaskedRenderer:    "Intel(R) UHD Graphics 630",
			RandomizeParameters: true,
		},
		Fonts: chrome.FontConfig{
			MaskFonts:       true,
			RandomizeFonts:  true,
			FakeFontMetrics: true,
		},
		Hardware: chrome.HardwareConfig{
			CPUClass:            "x86",
			HardwareConcurrency: 8,
			DeviceMemory:        8,
			Platform:            "Win32",
			MaxTouchPoints:      0,
		},
		Network: chrome.NetworkConfig{
			ConnectionType: "wifi",
			DownloadSpeed:  100.0,
			UploadSpeed:    20.0,
			RTT:            50,
			EffectiveType:  "4g",
			AcceptLanguage: "zh-CN,zh;q=0.9,en;q=0.8",
			AcceptEncoding: "gzip, deflate, br",
			DoNotTrack:     false,
			CustomHeaders:  map[string]string{"X-Forwarded-For": "*************"},
		},
		AntiDetect: chrome.AntiDetectConfig{
			StealthMode:              true,
			DisableWebDriver:         true,
			DisableAutomation:        true,
			MaskAutomationSignals:    true,
			RandomizeTimings:         true,
			SimulateHuman:            true,
			MaskPlugins:              true,
			MaskMimeTypes:            true,
			DisableHeadlessDetection: true,
			MouseMovementDelay:       100,
			TypingDelay:              50,
			ScrollDelay:              80,
			ClickDelay:               60,
			HumanLikeMovement:        true,
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		Version:   "1.0",
	}

	// 验证自定义指纹
	if err := customFingerprint.Validate(); err != nil {
		return fmt.Errorf("自定义指纹验证失败: %w", err)
	}

	fmt.Printf("自定义指纹哈希: %s\n", customFingerprint.GetHash())

	// 创建高级指纹配置文件
	profile, err := fbm.FingerprintManager.CreateFingerprintProfile(
		"高级指纹配置",
		"带有自定义反检测设置的指纹配置",
		customFingerprint,
	)
	if err != nil {
		return fmt.Errorf("创建高级指纹配置文件失败: %w", err)
	}

	fmt.Printf("创建高级指纹配置文件: %s\n", profile.Profile.Name)

	// 启动会话
	session, err := fbm.LaunchWithAutomation(profile.Profile.ID, &chrome.LaunchOptionsFingerprint{
		LaunchOptions: &chrome.LaunchOptions{
			Headless:  false,
			Timeout:   30 * time.Second,
			NoSandbox: true,
		},
		ApplyFingerprint:  true,
		StealthMode:       true,
		AntiDetectLevel:   "high",
		EnableAutomation:  true,
		AutomationTimeout: "60s",
		KeepSessionAlive:  true,
	})
	if err != nil {
		return fmt.Errorf("启动高级会话失败: %w", err)
	}

	// 创建复杂的自动化任务
	plan, err := fbm.CreateAutomationPlan(session.SessionID, "高级自动化测试", "测试反检测和人类化操作")
	if err != nil {
		return fmt.Errorf("创建高级计划失败: %w", err)
	}

	// 添加复杂任务序列
	if err := session.BrowserAutomation.AddNavigateTask(plan, "https://bot.sannysoft.com/", "访问机器人检测页面"); err != nil {
		return fmt.Errorf("添加导航任务失败: %w", err)
	}

	session.BrowserAutomation.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "15s", "等待页面加载")
	session.BrowserAutomation.AddSleepTask(plan, "2s", "模拟人类阅读时间")

	// 检查各种检测点
	session.BrowserAutomation.AddEvaluateTask(plan,
		`document.querySelector('#user-agent').textContent`,
		"detected_user_agent", "检测到的用户代理")

	session.BrowserAutomation.AddEvaluateTask(plan,
		`document.querySelector('#webdriver').textContent`,
		"webdriver_detected", "WebDriver检测结果")

	session.BrowserAutomation.AddEvaluateTask(plan,
		`document.querySelector('#chrome-runtime').textContent`,
		"chrome_runtime", "Chrome Runtime检测")

	// 截图保存结果
	session.BrowserAutomation.AddFullScreenshotTask(plan, "advanced_detection_test.png", "保存检测结果截图")

	// 执行高级测试
	if err := fbm.ExecuteAutomationPlan(session.SessionID, plan); err != nil {
		return fmt.Errorf("执行高级任务失败: %w", err)
	}

	// 分析结果
	variables := session.BrowserAutomation.GetVariables()
	fmt.Printf("高级检测结果:\n")
	fmt.Printf("  检测到的用户代理: %v\n", variables["detected_user_agent"])
	fmt.Printf("  WebDriver检测: %v\n", variables["webdriver_detected"])
	fmt.Printf("  Chrome Runtime: %v\n", variables["chrome_runtime"])

	// 等待观察
	time.Sleep(5 * time.Second)

	return fbm.CloseSession(session.SessionID)
}

// templateExample 模板使用示例
func templateExample() error {
	fbm, err := chrome.NewFingerprintBrowserManager("./browser_data")
	if err != nil {
		return fmt.Errorf("创建指纹浏览器管理器失败: %w", err)
	}
	defer fbm.CloseAllSessions()

	// 列出可用模板
	templates := fbm.FingerprintManager.ListTemplates()
	fmt.Printf("可用的指纹模板:\n")
	for _, template := range templates {
		fmt.Printf("  - %s (%s): %s\n", template.Name, template.Platform, template.Description)
	}

	// 如果有模板，使用第一个模板
	if len(templates) > 0 {
		template := templates[0]
		fmt.Printf("使用模板: %s\n", template.Name)

		// 从模板生成指纹
		fingerprint, err := fbm.FingerprintManager.GenerateFromTemplate(template.Name)
		if err != nil {
			return fmt.Errorf("从模板生成指纹失败: %w", err)
		}

		// 创建配置文件
		profile, err := fbm.FingerprintManager.CreateFingerprintProfile(
			fmt.Sprintf("模板配置_%s", template.Name),
			fmt.Sprintf("基于%s模板的配置", template.Name),
			fingerprint,
		)
		if err != nil {
			return fmt.Errorf("创建模板配置文件失败: %w", err)
		}

		fmt.Printf("成功创建基于模板的配置文件: %s\n", profile.Profile.Name)

		// 可以继续使用这个配置文件...
	}

	// 创建自定义模板
	customFingerprint, err := fbm.FingerprintManager.GenerateRandomFingerprint("darwin")
	if err != nil {
		return fmt.Errorf("生成自定义指纹失败: %w", err)
	}

	customTemplate := &chrome.FingerprintTemplate{
		Name:        "Custom macOS Template",
		Description: "自定义的macOS指纹模板",
		Platform:    "darwin",
		Category:    "custom",
		Config:      customFingerprint,
		CreatedAt:   time.Now(),
		Popular:     false,
		Verified:    false,
	}

	if err := fbm.FingerprintManager.SaveTemplate(customTemplate); err != nil {
		return fmt.Errorf("保存自定义模板失败: %w", err)
	}

	fmt.Printf("成功创建并保存自定义模板: %s\n", customTemplate.Name)

	return nil
}

// testExample 指纹测试示例
func testExample() error {
	fbm, err := chrome.NewFingerprintBrowserManager("./browser_data")
	if err != nil {
		return fmt.Errorf("创建指纹浏览器管理器失败: %w", err)
	}
	defer fbm.CloseAllSessions()

	// 创建测试用的指纹配置文件
	fingerprint, err := fbm.FingerprintManager.GenerateRandomFingerprint("")
	if err != nil {
		return fmt.Errorf("生成测试指纹失败: %w", err)
	}

	profile, err := fbm.FingerprintManager.CreateFingerprintProfile(
		"指纹测试配置",
		"用于测试指纹效果的配置",
		fingerprint,
	)
	if err != nil {
		return fmt.Errorf("创建测试配置文件失败: %w", err)
	}

	// 启动测试会话
	session, err := fbm.LaunchWithAutomation(profile.Profile.ID, &chrome.LaunchOptionsFingerprint{
		LaunchOptions: &chrome.LaunchOptions{
			Headless: false,
			Timeout:  30 * time.Second,
		},
		ApplyFingerprint: true,
		StealthMode:      true,
		AntiDetectLevel:  "high",
	})
	if err != nil {
		return fmt.Errorf("启动测试会话失败: %w", err)
	}

	// 执行指纹测试
	fmt.Println("正在测试Canvas指纹...")
	results, err := fbm.TestFingerprint(session.SessionID, "https://browserleaks.com/canvas")
	if err != nil {
		return fmt.Errorf("Canvas指纹测试失败: %w", err)
	}

	fmt.Printf("Canvas指纹测试结果:\n")
	fmt.Printf("  测试URL: %s\n", results.TestURL)
	fmt.Printf("  测试时间: %s\n", results.TestedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("  测试成功: %t\n", results.Success)
	fmt.Printf("  Canvas指纹: %s\n", results.CanvasFingerprint[:50]+"...") // 只显示前50个字符
	fmt.Printf("  WebGL指纹: %s\n", results.WebGLFingerprint)

	// 测试其他指纹网站
	testSites := []string{
		"https://audiofingerprint.openwpm.com/",
		"https://webbrowsertools.com/webgl-info/",
		"https://www.whatismybrowser.com/",
	}

	for _, site := range testSites {
		fmt.Printf("测试网站: %s\n", site)

		plan, err := fbm.CreateAutomationPlan(session.SessionID, "指纹网站测试", "访问指纹检测网站")
		if err != nil {
			continue
		}

		if err := session.BrowserAutomation.AddNavigateTask(plan, site, "访问测试网站"); err != nil {
			continue
		}

		session.BrowserAutomation.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "10s", "等待页面加载")
		session.BrowserAutomation.AddSleepTask(plan, "3s", "等待页面稳定")
		session.BrowserAutomation.AddFullScreenshotTask(plan, fmt.Sprintf("test_%d.png", time.Now().Unix()), "截图保存")

		if err := fbm.ExecuteAutomationPlan(session.SessionID, plan); err != nil {
			fmt.Printf("  测试失败: %v\n", err)
		} else {
			fmt.Printf("  测试成功\n")
		}

		time.Sleep(2 * time.Second)
	}

	// 获取统计信息
	stats := fbm.GetStats()
	fmt.Printf("\n管理器统计信息:\n")
	for key, value := range stats {
		if key != "fingerprint_stats" {
			fmt.Printf("  %s: %v\n", key, value)
		}
	}

	return fbm.CloseSession(session.SessionID)
}

// demonstrateSessionManagement 会话管理示例
func demonstrateSessionManagement() error {
	fbm, err := chrome.NewFingerprintBrowserManager("./browser_data")
	if err != nil {
		return err
	}
	defer fbm.CloseAllSessions()

	// 创建多个配置文件
	profiles := make([]*chrome.FingerprintProfile, 3)
	for i := 0; i < 3; i++ {
		fingerprint, err := fbm.FingerprintManager.GenerateRandomFingerprint("")
		if err != nil {
			continue
		}

		profile, err := fbm.FingerprintManager.CreateFingerprintProfile(
			fmt.Sprintf("测试配置_%d", i+1),
			fmt.Sprintf("第%d个测试配置", i+1),
			fingerprint,
		)
		if err != nil {
			continue
		}
		profiles[i] = profile
	}

	// 启动多个会话
	sessions := make([]*chrome.AutomationSession, 0)
	for _, profile := range profiles {
		if profile == nil {
			continue
		}

		session, err := fbm.LaunchWithAutomation(profile.Profile.ID, nil)
		if err != nil {
			continue
		}
		sessions = append(sessions, session)
	}

	fmt.Printf("成功启动 %d 个会话\n", len(sessions))

	// 列出所有会话
	allSessions := fbm.ListSessions()
	fmt.Printf("当前活动会话:\n")
	for _, session := range allSessions {
		fmt.Printf("  会话ID: %s, 配置文件: %s, 状态: %s\n",
			session.SessionID, session.ProfileID, session.Status)
	}

	// 并发执行任务
	for _, session := range sessions {
		go func(s *chrome.AutomationSession) {
			plan, err := fbm.CreateAutomationPlan(s.SessionID, "并发测试", "并发执行测试")
			if err != nil {
				return
			}

			if err := s.BrowserAutomation.AddNavigateTask(plan, "https://httpbin.org/ip", "获取IP信息"); err != nil {
				return
			}

			s.BrowserAutomation.AddWaitVisibleTask(plan, "body", rod.SelectorQuery, "10s", "等待页面加载")
			s.BrowserAutomation.AddSleepTask(plan, "2s", "等待")

			fbm.ExecuteAutomationPlan(s.SessionID, plan)
		}(session)
	}

	// 等待任务完成
	time.Sleep(10 * time.Second)

	fmt.Println("会话管理示例完成")
	return nil
}
