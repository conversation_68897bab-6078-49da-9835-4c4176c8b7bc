package database

import (
	"fmt"
	"log/slog"
	"os"
	"sync"
	"time"

	"a8.tools/backend/config"
	"a8.tools/backend/database/encryptable"
	"a8.tools/backend/database/models"
	"a8.tools/backend/database/models/account"
	"a8.tools/backend/database/optimizer"
	"a8.tools/backend/database/security"
	"a8.tools/backend/pkg/logger"
	"a8.tools/backend/utils/file"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	gormLogger "gorm.io/gorm/logger"
)

var (
	dB      *gorm.DB
	dbOnce  sync.Once
	dbMutex sync.RWMutex // 添加读写锁保护数据库实例
)

// IsDBInitialized 检查数据库是否已初始化
func IsDBInitialized() bool {
	dbMutex.RLock()
	defer dbMutex.RUnlock()
	return dB != nil
}

// GetDB safely retrieves the database instance, performing a health check.
func GetDB() (*gorm.DB, error) {
	dbMutex.RLock()
	defer dbMutex.RUnlock()
	if err := CheckDBHealth(); err != nil {
		return nil, err
	}
	return dB, nil
}

// CheckDBHealth verifies that the database is initialized and the file exists.
func CheckDBHealth() error {
	if dB == nil {
		return fmt.Errorf("NotInitialized")
	}

	dbPath, err := GetDatabasePath()
	if err != nil {
		return fmt.Errorf("DatabaseFileMissing")
	}

	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		// If the file is missing, invalidate the connection.
		go func() {
			dbMutex.Lock()
			defer dbMutex.Unlock()
			if dB != nil {
				sqlDB, _ := dB.DB()
				if sqlDB != nil {
					sqlDB.Close()
				}
				dB = nil
			}
		}()
		return fmt.Errorf("DatabaseFileMissing")
	}

	return nil
}

// getGormLogger 根据构建标签返回适当的GORM日志配置
func getGormLogger() gormLogger.Interface {
	// 创建独立的数据库 logger 实例，保存到 "database" 子目录
	dbLoggerConfig := logger.DefaultConfig()
	dbLoggerConfig.OutputFile = logger.GetDefaultLogFile("database")
	dbLoggerConfig.OutputConsole = false
	dbLoggerConfig.Level = logger.DEBUG
	dbLoggerConfig.AsyncWrite = true
	dbLoggerConfig.MaxSize = 50   // 50MB
	dbLoggerConfig.MaxAge = 30    // 30天
	dbLoggerConfig.MaxBackups = 5 // 5个备份文件
	dbLoggerConfig.Compress = true
	dbLoggerConfig.AddSource = true
	dbLoggerConfig.TimeFormat = "2006-01-02 15:04:05.000"

	// 创建独立的数据库 logger 实例
	dbLogger, err := logger.New(dbLoggerConfig)
	if err != nil {
		logger.Error("创建数据库 logger 失败，使用默认 logger", slog.String("error", err.Error()))
		dbLogger = logger.Default()
	}

	if config.IsDebugBuild {
		// 调试模式：启用详细日志，便于开发调试
		gormConfig := gormLogger.Config{
			SlowThreshold:             time.Second,     // 慢SQL阈值，超过1秒的查询会被标记为慢查询
			LogLevel:                  gormLogger.Info, // 日志级别设为Info，会显示所有SQL语句
			IgnoreRecordNotFoundError: true,            // 忽略记录未找到的错误
			Colorful:                  true,            // 启用彩色打印，使日志更易读
		}
		// 使用结构化的 logger 适配器，提供更好的日志格式
		return NewStructuredGormLoggerWithLogger(gormConfig, dbLogger)
	} else {
		// 生产模式：使用较低的日志级别，但仍然记录错误和慢查询
		gormConfig := gormLogger.Config{
			SlowThreshold:             5 * time.Second, // 生产模式下只记录超过5秒的慢查询
			LogLevel:                  gormLogger.Warn, // 只记录警告和错误级别的日志
			IgnoreRecordNotFoundError: true,            // 忽略记录未找到的错误
			Colorful:                  false,           // 禁用彩色打印
		}
		// 使用结构化的 logger 适配器，确保日志通过统一的系统管理
		return NewStructuredGormLoggerWithLogger(gormConfig, dbLogger)
	}
}

// GetDatabasePath 获取数据库文件路径
func GetDatabasePath() (string, error) {
	return file.GetDatabaseFile("app.db")
}

// ReInitDB 重新初始化数据库连接（用于密码变更后）
func ReInitDB() error {
	dbMutex.Lock()
	defer dbMutex.Unlock()

	// 关闭现有数据库连接
	if dB != nil {
		sqlDB, err := dB.DB()
		if err == nil {
			sqlDB.Close()
		}
		dB = nil
	}

	// 重置同步标志，允许重新初始化
	dbOnce = sync.Once{}

	// 清除加密器缓存，确保使用新密码
	if err := clearAllEncryptorCaches(); err != nil {
		logger.Warn("清除加密器缓存失败", slog.String("error", err.Error()))
	}

	// 使用新密码重新初始化数据库
	return initDBInternal()
}

// initDBInternal 内部数据库初始化逻辑
func initDBInternal() error {

	dbFile, err := file.GetDatabaseFile("app.db")
	if err != nil {
		return err
	}

	// 在打开连接之前检查文件是否存在
	// 这是为了防止 SQLite 驱动在文件不存在时自动创建一个新的空数据库
	if _, err := os.Stat(dbFile); os.IsNotExist(err) {
		return fmt.Errorf("DatabaseFileMissing")
	}

	// 根据构建标签获取安全的日志配置
	gormLogger := getGormLogger()

	// 基础 SQLite 连接参数
	dsn := fmt.Sprintf("%s?_busy_timeout=5000",
		dbFile)

	// Open the database connection
	db, err := gorm.Open(sqlite.Open(dsn), &gorm.Config{
		Logger: gormLogger, // 使用安全的日志配置
	})
	if err != nil {
		return err
	}

	// 获取通用数据库对象，设置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return err
	}

	// 设置连接池参数
	// SetMaxIdleConns: 设置空闲连接池中连接的最大数量
	sqlDB.SetMaxIdleConns(10)
	// SetMaxOpenConns: 设置打开数据库连接的最大数量
	sqlDB.SetMaxOpenConns(100)
	// SetConnMaxLifetime: 设置连接可复用的最大时间
	sqlDB.SetConnMaxLifetime(time.Hour)
	// SetConnMaxIdleTime: 设置连接在连接池中最大空闲时间
	sqlDB.SetConnMaxIdleTime(time.Minute * 30)

	// 1. 首先执行数据库迁移，确保数据库结构完整
	err = db.AutoMigrate(
		&account.Email{},
		&account.Discord{},
		&account.Telegram{},
		&account.Proxy{},
		&account.X{},
		&models.Wallet{},
		&models.Browser{},
		&models.Setting{},
	)

	if err != nil {
		return fmt.Errorf("数据库迁移失败: %w", err)
	}

	// 设置全局数据库实例，确保后续优化操作可以访问
	dB = db

	// 2. 数据库结构就绪后，应用 SQLite 性能优化配置
	optimizer := optimizer.NewSQLiteOptimizer(db)
	if err := optimizer.OptimizeSettings(); err != nil {
		logger.Warn("SQLite 优化配置失败", slog.String("error", err.Error()))
	}

	// 3. 最后应用安全性改进，确保在优化配置基础上增强安全性
	securityConfig := security.DefaultSecurityConfig()
	if err := security.ApplySecurityImprovements(db, securityConfig); err != nil {
		logger.Warn("应用安全性改进失败", slog.String("error", err.Error()))
	}

	return nil
}

// InitDB 初始化数据库和加密器
func InitDB() error {
	var initErr error
	dbOnce.Do(func() {
		initErr = initDBInternal()
	})
	return initErr
}

// clearAllEncryptorCaches 清除所有加密器缓存
func clearAllEncryptorCaches() error {
	// 获取所有注册的加密模型
	allModels := encryptable.GetAllModels()

	// 为每个模型清除缓存
	for _, modelInfo := range allModels {
		if encryptableModel, ok := modelInfo.Model.(interface{ ClearEncryptorCache() }); ok {
			encryptableModel.ClearEncryptorCache()
		}
	}

	return nil
}
