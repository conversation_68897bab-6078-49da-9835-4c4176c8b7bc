package encryptable

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"log/slog"
	"reflect"
	"strings"
	"sync"
	"time"

	"a8.tools/backend/pkg/cryptor"
	"a8.tools/backend/pkg/interfaces"
	"a8.tools/backend/pkg/logger"
	"a8.tools/backend/services/password"
	"gorm.io/gorm"
)

var (
	// 全局加密器缓存，避免重复创建
	globalEncryptorCache = &encryptorCache{}
	// 缓存过期时间（5分钟）
	cacheExpireTime = 5 * time.Minute

	// 外部密码管理器实例，通过依赖注入设置
	externalPasswordManager interfaces.PasswordManagerInterface
	passwordManagerMutex    sync.RWMutex
)

// encryptorCache 加密器缓存结构
type encryptorCache struct {
	encryptor            cryptor.Encryptor
	lastUpdate           time.Time
	passwordManagerMutex sync.RWMutex
}

// Mixin 加密混入，提供通用的加密/解密功能
type Mixin struct{}

// isEncryptedData 检查数据是否为加密数据
// 简单的启发式检查：加密数据通常是base64编码，长度较长且包含特定字符
func isEncryptedData(data string) bool {
	// 如果数据太短，很可能是明文
	if len(data) < 20 {
		return false
	}

	// 如果以常见明文模式开头，很可能是明文
	if strings.HasPrefix(data, "0x") || // 十六进制
		strings.Contains(data, " ") || // 包含空格的助记词
		len(strings.Fields(data)) > 1 { // 多个单词
		return false
	}

	// 检查是否看起来像base64编码的数据
	// base64字符集: A-Z, a-z, 0-9, +, /, =
	base64Chars := 0
	for _, c := range data {
		if (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') ||
			(c >= '0' && c <= '9') || c == '+' || c == '/' || c == '=' {
			base64Chars++
		}
	}

	// 如果大部分字符都是base64字符，认为是加密数据
	return float64(base64Chars)/float64(len(data)) > 0.8
}

// getEncryptor 获取缓存的加密器实例，如果缓存过期或不存在则重新创建
func (e *Mixin) getEncryptor() (cryptor.Encryptor, error) {
	globalEncryptorCache.passwordManagerMutex.RLock()

	// 检查缓存是否有效
	if globalEncryptorCache.encryptor != nil &&
		time.Since(globalEncryptorCache.lastUpdate) < cacheExpireTime {
		encryptor := globalEncryptorCache.encryptor
		globalEncryptorCache.passwordManagerMutex.RUnlock()
		return encryptor, nil
	}

	globalEncryptorCache.passwordManagerMutex.RUnlock()

	// 获取写锁重新创建加密器
	globalEncryptorCache.passwordManagerMutex.Lock()
	defer globalEncryptorCache.passwordManagerMutex.Unlock()

	// 双重检查，防止并发创建
	if globalEncryptorCache.encryptor != nil &&
		time.Since(globalEncryptorCache.lastUpdate) < cacheExpireTime {
		return globalEncryptorCache.encryptor, nil
	}

	// 使用外部密码管理器获取DEK
	passwordManagerMutex.RLock()
	passwordManager := externalPasswordManager
	passwordManagerMutex.RUnlock()

	var dek []byte
	var err error

	if passwordManager != nil {
		// 使用外部密码管理器
		dek, err = passwordManager.GetDataEncryptionKey()
		if err != nil {
			logger.Error("获取数据加密密钥失败", slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to get data encryption key: %w", err)
		}
	} else {
		// 使用密码管理器
		passwordManager := password.GetGlobalPasswordManager()
		if passwordManager == nil {
			return nil, errors.New("password manager not available")
		}

		if !passwordManager.HasAppPassword() {
			return nil, errors.New("no password set")
		}

		dek, err = passwordManager.GetDataEncryptionKey()
		if err != nil {
			logger.Error("获取数据加密密钥失败", slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to get data encryption key: %w", err)
		}
	}

	if len(dek) == 0 {
		return nil, errors.New("data encryption key is not available")
	}

	// 创建新的加密器并更新缓存
	// 将DEK编码为base64字符串传递给现有的加密器
	dekStr := base64.StdEncoding.EncodeToString(dek)
	globalEncryptorCache.encryptor = cryptor.NewEncryptor(dekStr)
	globalEncryptorCache.lastUpdate = time.Now()

	return globalEncryptorCache.encryptor, nil
}

// BeforeSave GORM hook，在保存前加密字段
func (e *Mixin) BeforeSave(tx *gorm.DB) error {
	encryptor, err := e.getEncryptor()
	if err != nil {
		// 如果没有密码或无法获取加密器，则无法加密，必须中断操作
		return err
	}

	return e.processFields(tx, encryptor, "encrypt")
}

// AfterFind GORM hook，在查询后解密字段
func (e *Mixin) AfterFind(tx *gorm.DB) error {
	encryptor, err := e.getEncryptor()
	if err != nil {
		// 检查是否是密码需要输入的错误
		if isPasswordRequiredError(err) {
			// 如果是密码需要输入的错误，记录日志但不阻止查询
			tx.Logger.Warn(context.Background(), "解密跳过：密码不可用 - %v", err)
			return nil // 不返回错误，允许查询继续
		}
		// 其他错误（如系统错误）应该阻止查询
		return err
	}

	return e.processFields(tx, encryptor, "decrypt")
}

// processFields 是处理加密和解密的核心逻辑
func (e *Mixin) processFields(tx *gorm.DB, encryptor cryptor.Encryptor, mode string) error {
	if tx.Statement.Dest == nil {
		return nil
	}

	// 获取模型实例的反射值
	destValue := reflect.ValueOf(tx.Statement.Dest)
	if destValue.Kind() == reflect.Ptr {
		destValue = destValue.Elem()
	}

	// 根据是单个对象还是切片进行处理
	if destValue.Kind() == reflect.Slice {
		for i := 0; i < destValue.Len(); i++ {
			if err := e.processSingleInstance(tx, destValue.Index(i), encryptor, mode); err != nil {
				return err // 在切片处理中，任何一个失败都应中断并返回错误
			}
		}
	} else {
		return e.processSingleInstance(tx, destValue, encryptor, mode)
	}

	return nil
}

// processSingleInstance 处理单个模型实例的加解密
func (e *Mixin) processSingleInstance(tx *gorm.DB, instanceValue reflect.Value, encryptor cryptor.Encryptor, mode string) error {
	if instanceValue.Kind() == reflect.Ptr {
		instanceValue = instanceValue.Elem()
	}

	if !instanceValue.IsValid() || instanceValue.Kind() != reflect.Struct {
		return nil // 不是结构体或无效值，无法处理
	}

	// 从注册表获取模型信息
	registry := GetGlobalRegistry()
	modelInfo, ok := registry.GetModelByType(instanceValue.Type())
	if !ok || len(modelInfo.EncryptedFields) == 0 {
		return nil // 模型未注册或没有加密字段
	}

	// 遍历缓存的加密字段索引
	for _, fieldIndex := range modelInfo.EncryptedFields {
		field := instanceValue.Field(fieldIndex)

		if !field.CanSet() || field.Type().Kind() != reflect.String {
			continue
		}

		originalValue := field.String()
		if originalValue == "" {
			continue
		}

		var resultValue string
		var err error

		if mode == "encrypt" {
			if isEncryptedData(originalValue) {
				continue // 避免重复加密
			}
			resultValue, err = encryptor.Encrypt(originalValue)
		} else { // decrypt
			if !isEncryptedData(originalValue) {
				continue // 明文数据，无需解密
			}
			resultValue, err = encryptor.Decrypt(originalValue)
		}

		if err != nil {
			// 使用 tx.Logger 记录错误
			tx.Logger.Error(context.Background(), "字段 %s 处理失败: %v", instanceValue.Type().Field(fieldIndex).Name, err)
			if mode == "encrypt" {
				return err // 加密失败必须返回错误以中断事务
			}
			continue // 解密失败则继续
		}

		field.SetString(resultValue)
	}

	return nil
}

// ClearEncryptorCache 清除加密器缓存（用于密码更改等场景）
func (e *Mixin) ClearEncryptorCache() {
	globalEncryptorCache.passwordManagerMutex.Lock()
	defer globalEncryptorCache.passwordManagerMutex.Unlock()

	globalEncryptorCache.encryptor = nil
	globalEncryptorCache.lastUpdate = time.Time{}

	// 同时清除外部密码管理器的缓存
	passwordManagerMutex.RLock()
	passwordManager := externalPasswordManager
	passwordManagerMutex.RUnlock()

	if passwordManager != nil {
		passwordManager.ClearPassword()
	}
}

// ClearFieldCache (no-op after refactor)
// Kept for API compatibility if needed.
func (e *Mixin) ClearFieldCache() {
	// The new design uses a central registry, so individual mixin field caches are no longer used.
	// The registry has its own lifecycle management.
}

// GetCacheStats 获取缓存统计信息（用于监控和调试）
func (e *Mixin) GetCacheStats() map[string]any {
	globalEncryptorCache.passwordManagerMutex.RLock()
	encryptorCached := globalEncryptorCache.encryptor != nil
	lastUpdate := globalEncryptorCache.lastUpdate
	globalEncryptorCache.passwordManagerMutex.RUnlock()

	registry := GetGlobalRegistry()
	modelCount := registry.GetModelCount()

	return map[string]any{
		"encryptor_cached":      encryptorCached,
		"encryptor_last_update": lastUpdate,
		"registered_models":     modelCount,
		"cache_expire_time":     cacheExpireTime,
	}
}

// SetPasswordManager 设置外部密码管理器（依赖注入）
func SetPasswordManager(manager interfaces.PasswordManagerInterface) {
	passwordManagerMutex.Lock()
	defer passwordManagerMutex.Unlock()
	externalPasswordManager = manager
}

// GetEncryptionManager 获取加密管理器实例（实现接口）
func GetEncryptionManager() interfaces.EncryptionManagerInterface {
	return &encryptionManagerImpl{}
}

// encryptionManagerImpl 加密管理器实现
type encryptionManagerImpl struct{}

func (e *encryptionManagerImpl) SetPasswordManager(manager interfaces.PasswordManagerInterface) {
	SetPasswordManager(manager)
}

func (e *encryptionManagerImpl) ClearEncryptorCache() {
	mixin := &Mixin{}
	mixin.ClearEncryptorCache()
}

// isPasswordRequiredError 检查错误是否是密码需要输入的错误
func isPasswordRequiredError(err error) bool {
	if err == nil {
		return false
	}

	// 检查错误消息是否包含相关关键字
	errMsg := err.Error()
	return strings.Contains(errMsg, "Password is required") ||
		strings.Contains(errMsg, "password manager not set") ||
		strings.Contains(errMsg, "no password set") ||
		strings.Contains(errMsg, "Password cache expired or invalid")
}
