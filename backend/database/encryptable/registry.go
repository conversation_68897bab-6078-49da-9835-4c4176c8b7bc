package encryptable

import (
	"fmt"
	"reflect"
	"sync"
)

// ModelRegistry 可加密模型注册表
type ModelRegistry struct {
	models map[string]ModelInfo
	mutex  sync.RWMutex
}

// ModelInfo 存储可加密模型的信息
type ModelInfo struct {
	Model           interface{} // 模型实例，用于反射和数据库操作
	TableName       string      // 数据库表名
	DisplayName     string      // 显示名称，用于日志和用户界面
	Category        string      // 模型分类，如 "account", "config", "system"
	EncryptedFields []int       // 缓存加密字段的索引
}

// 全局模型注册表实例
var globalRegistry *ModelRegistry
var registryOnce sync.Once

// GetGlobalRegistry 获取全局模型注册表实例
func GetGlobalRegistry() *ModelRegistry {
	registryOnce.Do(func() {
		globalRegistry = &ModelRegistry{
			models: make(map[string]ModelInfo),
		}
	})
	return globalRegistry
}

// RegisterModel 注册一个可加密的模型
func (r *ModelRegistry) RegisterModel(key string, info ModelInfo) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	// 验证必需字段
	if info.Model == nil {
		return fmt.Errorf("模型 %s 的 Model 字段不能为空", key)
	}
	if info.TableName == "" {
		return fmt.Errorf("模型 %s 的 TableName 字段不能为空", key)
	}
	if info.DisplayName == "" {
		return fmt.Errorf("模型 %s 的 DisplayName 字段不能为空", key)
	}

	// 使用反射查找加密字段
	modelType := reflect.TypeOf(info.Model)
	if modelType.Kind() == reflect.Ptr {
		modelType = modelType.Elem()
	}

	var encryptedFields []int
	for i := 0; i < modelType.NumField(); i++ {
		field := modelType.Field(i)
		if tag := field.Tag.Get("encrypt"); tag == "true" {
			encryptedFields = append(encryptedFields, i)
		}
	}
	info.EncryptedFields = encryptedFields

	// 检查是否已经注册
	if _, exists := r.models[key]; exists {
		return fmt.Errorf("模型 %s 已经注册", key)
	}

	r.models[key] = info
	return nil
}

// UnregisterModel 取消注册一个可加密的模型
func (r *ModelRegistry) UnregisterModel(key string) error {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	if _, exists := r.models[key]; !exists {
		return fmt.Errorf("模型 %s 未注册", key)
	}

	delete(r.models, key)
	return nil
}

// GetAllModels 获取所有注册的可加密模型
func (r *ModelRegistry) GetAllModels() []ModelInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	models := make([]ModelInfo, 0, len(r.models))
	for _, info := range r.models {
		models = append(models, info)
	}
	return models
}

// GetModelsByCategory 根据分类获取模型
func (r *ModelRegistry) GetModelsByCategory(category string) []ModelInfo {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	var models []ModelInfo
	for _, info := range r.models {
		if info.Category == category {
			models = append(models, info)
		}
	}
	return models
}

// GetModel 根据键获取特定模型
func (r *ModelRegistry) GetModel(key string) (ModelInfo, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	info, exists := r.models[key]
	return info, exists
}

// ListModelKeys 列出所有注册的模型键
func (r *ModelRegistry) ListModelKeys() []string {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	keys := make([]string, 0, len(r.models))
	for key := range r.models {
		keys = append(keys, key)
	}
	return keys
}

// GetModelCount 获取注册的模型数量
func (r *ModelRegistry) GetModelCount() int {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	return len(r.models)
}

// GetModelByType 根据反射类型获取模型信息（新增辅助方法）
func (r *ModelRegistry) GetModelByType(modelType reflect.Type) (ModelInfo, bool) {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	for _, info := range r.models {
		// 获取info.Model的类型，并处理指针
		infoType := reflect.TypeOf(info.Model)
		if infoType.Kind() == reflect.Ptr {
			infoType = infoType.Elem()
		}

		if infoType == modelType {
			return info, true
		}
	}

	return ModelInfo{}, false
}

// ValidateAllModels 验证所有注册的模型
func (r *ModelRegistry) ValidateAllModels() error {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	for _, info := range r.models {
		// 验证加密字段是否存在
		if len(info.EncryptedFields) == 0 {
			// 注意：这里可以根据业务逻辑决定是否要返回错误
			// 有些模型可能注册了但确实没有加密字段，这也许是合法的

		}
	}

	return nil
}

// ClearRegistry 清空注册表（主要用于测试）
func (r *ModelRegistry) ClearRegistry() {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.models = make(map[string]ModelInfo)
}

// GetModelStatistics 获取模型统计信息
func (r *ModelRegistry) GetModelStatistics() map[string]interface{} {
	r.mutex.RLock()
	defer r.mutex.RUnlock()

	stats := make(map[string]interface{})
	stats["total_models"] = len(r.models)

	// 按分类统计
	categoryCount := make(map[string]int)
	for _, info := range r.models {
		categoryCount[info.Category]++
	}
	stats["by_category"] = categoryCount

	// 加密字段统计
	totalFields := 0
	for _, info := range r.models {
		totalFields += len(info.EncryptedFields)
	}
	stats["total_encrypted_fields"] = totalFields

	return stats
}

// 便捷函数：注册模型到全局注册表
func RegisterModel(key string, info ModelInfo) error {
	return GetGlobalRegistry().RegisterModel(key, info)
}

// 便捷函数：从全局注册表获取所有模型
func GetAllModels() []ModelInfo {
	return GetGlobalRegistry().GetAllModels()
}

// 便捷函数：根据分类获取模型
func GetModelsByCategory(category string) []ModelInfo {
	return GetGlobalRegistry().GetModelsByCategory(category)
}

// 便捷函数：验证所有模型
func ValidateAllModels() error {
	return GetGlobalRegistry().ValidateAllModels()
}
