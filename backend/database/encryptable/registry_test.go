package encryptable

import (
	"fmt"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestModel 用于测试的模型
type TestModel struct {
	ID       uint   `gorm:"primarykey"`
	Name     string `gorm:"size:255"`
	Secret   string `gorm:"type:text" encrypt:"true"` // 这个字段需要加密
	Password string `gorm:"size:255" encrypt:"true"`  // 这个字段也需要加密

	Mixin
}

// TestInvalidModel 未实现 Encryptable 接口的模型
type TestInvalidModel struct {
	ID   uint   `gorm:"primarykey"`
	Name string `gorm:"size:255"`
}

func TestModelRegistry_RegisterModel(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 测试成功注册
	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	err := registry.RegisterModel("test.model", info)
	assert.NoError(t, err)

	// 验证模型已注册
	assert.Equal(t, 1, registry.GetModelCount())

	// 测试重复注册
	err = registry.RegisterModel("test.model", info)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "已经注册")

	// 测试无效模型注册
	invalidModel := &TestInvalidModel{}
	invalidInfo := ModelInfo{
		Model:       invalidModel,
		TableName:   "invalid_models",
		DisplayName: "无效模型",
		Category:    "test",
	}

	err = registry.RegisterModel("test.invalid", invalidInfo)
	assert.NoError(t, err) // 这应该成功，因为模型注册不检查接口实现，只检查加密标签
}

func TestModelRegistry_ValidationErrors(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	testModel := &TestModel{}

	// 测试空 Model
	info := ModelInfo{
		Model:       nil,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}
	err := registry.RegisterModel("test.empty", info)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "Model 字段不能为空")

	// 测试空 TableName
	info = ModelInfo{
		Model:       testModel,
		TableName:   "",
		DisplayName: "测试模型",
		Category:    "test",
	}
	err = registry.RegisterModel("test.table", info)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "TableName 字段不能为空")

	// 测试空 DisplayName
	info = ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "",
		Category:    "test",
	}
	err = registry.RegisterModel("test.display", info)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "DisplayName 字段不能为空")
}

func TestModelRegistry_GetOperations(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 注册测试模型
	testModel1 := &TestModel{}
	info1 := ModelInfo{
		Model:       testModel1,
		TableName:   "test_models1",
		DisplayName: "测试模型1",
		Category:    "test",
	}

	testModel2 := &TestModel{}
	info2 := ModelInfo{
		Model:       testModel2,
		TableName:   "test_models2",
		DisplayName: "测试模型2",
		Category:    "account",
	}

	err := registry.RegisterModel("test.model1", info1)
	require.NoError(t, err)
	err = registry.RegisterModel("test.model2", info2)
	require.NoError(t, err)

	// 测试获取所有模型
	allModels := registry.GetAllModels()
	assert.Equal(t, 2, len(allModels))

	// 测试按分类获取
	testCategoryModels := registry.GetModelsByCategory("test")
	assert.Equal(t, 1, len(testCategoryModels))
	assert.Equal(t, "测试模型1", testCategoryModels[0].DisplayName)

	accountCategoryModels := registry.GetModelsByCategory("account")
	assert.Equal(t, 1, len(accountCategoryModels))
	assert.Equal(t, "测试模型2", accountCategoryModels[0].DisplayName)

	// 测试获取特定模型
	model, exists := registry.GetModel("test.model1")
	assert.True(t, exists)
	assert.Equal(t, "测试模型1", model.DisplayName)

	_, exists = registry.GetModel("nonexistent")
	assert.False(t, exists)

	// 测试列出键
	keys := registry.ListModelKeys()
	assert.Equal(t, 2, len(keys))
	assert.Contains(t, keys, "test.model1")
	assert.Contains(t, keys, "test.model2")
}

func TestModelRegistry_UnregisterModel(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 注册模型
	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	err := registry.RegisterModel("test.model", info)
	require.NoError(t, err)
	assert.Equal(t, 1, registry.GetModelCount())

	// 取消注册
	err = registry.UnregisterModel("test.model")
	assert.NoError(t, err)
	assert.Equal(t, 0, registry.GetModelCount())

	// 取消注册不存在的模型
	err = registry.UnregisterModel("nonexistent")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "未注册")
}

func TestModelRegistry_ValidateAllModels(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 注册有效模型
	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	err := registry.RegisterModel("test.model", info)
	require.NoError(t, err)

	// 验证应该成功
	err = registry.ValidateAllModels()
	assert.NoError(t, err)
}

func TestModelRegistry_Statistics(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 注册多个模型
	models := []struct {
		key      string
		category string
		name     string
	}{
		{"test.model1", "test", "测试模型1"},
		{"test.model2", "test", "测试模型2"},
		{"account.model1", "account", "账户模型1"},
	}

	for _, m := range models {
		testModel := &TestModel{}
		info := ModelInfo{
			Model:       testModel,
			TableName:   m.key,
			DisplayName: m.name,
			Category:    m.category,
		}
		err := registry.RegisterModel(m.key, info)
		require.NoError(t, err)
	}

	// 获取统计信息
	stats := registry.GetModelStatistics()
	assert.Equal(t, 3, stats["total_models"])
	assert.Equal(t, 6, stats["total_encrypted_fields"]) // 每个 TestModel 有 2 个加密字段，3个模型共6个字段

	categoryStats := stats["by_category"].(map[string]int)
	assert.Equal(t, 2, categoryStats["test"])
	assert.Equal(t, 1, categoryStats["account"])
}

func TestModelRegistry_ClearRegistry(t *testing.T) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 注册模型
	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	err := registry.RegisterModel("test.model", info)
	require.NoError(t, err)
	assert.Equal(t, 1, registry.GetModelCount())

	// 清空注册表
	registry.ClearRegistry()
	assert.Equal(t, 0, registry.GetModelCount())
}

func TestGlobalRegistryFunctions(t *testing.T) {
	// 清空全局注册表以确保测试隔离
	globalRegistry = nil
	registryOnce = sync.Once{}

	// 测试全局注册表单例
	registry1 := GetGlobalRegistry()
	registry2 := GetGlobalRegistry()
	assert.Same(t, registry1, registry2)

	// 测试便捷函数
	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	err := RegisterModel("test.global", info)
	assert.NoError(t, err)

	allModels := GetAllModels()
	assert.Equal(t, 1, len(allModels))

	err = ValidateAllModels()
	assert.NoError(t, err)

	// 清理
	registry1.ClearRegistry()
}

func BenchmarkRegistry_RegisterModel(b *testing.B) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		key := fmt.Sprintf("test.model%d", i)
		_ = registry.RegisterModel(key, info)
	}
}

func BenchmarkRegistry_GetAllModels(b *testing.B) {
	registry := &ModelRegistry{
		models: make(map[string]ModelInfo),
	}

	// 预注册一些模型
	testModel := &TestModel{}
	info := ModelInfo{
		Model:       testModel,
		TableName:   "test_models",
		DisplayName: "测试模型",
		Category:    "test",
	}

	for i := 0; i < 100; i++ {
		key := fmt.Sprintf("test.model%d", i)
		_ = registry.RegisterModel(key, info)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = registry.GetAllModels()
	}
}
