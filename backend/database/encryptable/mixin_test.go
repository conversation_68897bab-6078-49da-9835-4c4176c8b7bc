package encryptable

import (
	"fmt"
	"reflect"
	"testing"
	"time"

	"a8.tools/backend/pkg/cryptor"
	"a8.tools/backend/pkg/interfaces"
	"a8.tools/backend/services/password"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MixinTestModel 用于测试的模型
type MixinTestModel struct {
	ID       uint   `gorm:"primarykey"`
	Name     string `gorm:"size:255"`
	Secret   string `gorm:"type:text" encrypt:"true"`
	Password string `gorm:"size:255" encrypt:"true"`
	Email    string `gorm:"size:255" encrypt:"true"`

	Mixin
}

// BenchmarkTestModel 用于基准测试的模型
type BenchmarkTestModel struct {
	ID       uint   `gorm:"primarykey"`
	Name     string `gorm:"size:255"`
	Secret   string `gorm:"type:text" encrypt:"true"`
	Password string `gorm:"size:255" encrypt:"true"`
	Email    string `gorm:"size:255" encrypt:"true"`

	Mixin
}

// mockWailsApp 模拟的 Wails 应用
type mockWailsApp struct{}

func (m *mockWailsApp) Events() password.WailsEvents {
	return &mockWailsEvents{}
}

// mockWailsEvents 模拟的 Wails 事件
type mockWailsEvents struct{}

func (m *mockWailsEvents) Emit(eventName string, data interface{}) {
	// 模拟实现，不做任何事情
}

// testPasswordManager 测试用的密码管理器实现
type testPasswordManager struct {
	password        string
	passwordService *password.PasswordService
	passwordManager *password.PasswordManager
	initialized     bool
}

func (t *testPasswordManager) GetDataEncryptionKey() ([]byte, error) {
	// 确保服务已初始化
	if !t.initialized {
		mockApp := &mockWailsApp{}
		err := t.passwordService.Init(mockApp)
		if err != nil {
			return nil, err
		}

		// 初始化密码管理器
		t.passwordManager = password.GetGlobalPasswordManager()
		if t.passwordManager == nil {
			return nil, fmt.Errorf("password manager not available")
		}

		t.initialized = true
	}

	// 首先设置密码
	err := t.passwordService.SetPassword(t.password)
	if err != nil {
		return nil, err
	}

	// 解锁应用
	err = t.passwordService.UnlockApplication(t.password)
	if err != nil {
		return nil, err
	}

	// 通过密码管理器获取数据加密密钥
	return t.passwordManager.GetDataEncryptionKey()
}

func (t *testPasswordManager) ClearPassword() {
	// 测试实现，不需要实际清除
}

// 确保testPasswordManager实现了interfaces.PasswordManagerInterface
var _ interfaces.PasswordManagerInterface = (*testPasswordManager)(nil)

// setupTest 设置测试环境
func setupTest(t *testing.T) {
	t.Helper()

	// 清除注册表
	GetGlobalRegistry().ClearRegistry()

	// 注册测试模型
	err := RegisterModel("test_model", ModelInfo{
		Model:       &MixinTestModel{},
		TableName:   "test_models",
		DisplayName: "Test Model",
		Category:    "test",
	})
	require.NoError(t, err)

	// 设置密码服务
	passwordService := password.GetGlobalPasswordService()
	require.NotNil(t, passwordService)

	// 创建并设置测试用的密码管理器
	testPasswordManager := &testPasswordManager{
		password:        "Xk9#mP2$vL8@qR5!nB7&wE3*uY6^tI4%",
		passwordService: passwordService,
	}
	SetPasswordManager(testPasswordManager)
}

// setupBenchmarkTest 设置基准测试环境
func setupBenchmarkTest() {
	// 清除注册表
	GetGlobalRegistry().ClearRegistry()

	// 注册基准测试模型
	_ = RegisterModel("benchmark_model", ModelInfo{
		Model:       &BenchmarkTestModel{},
		TableName:   "benchmark_models",
		DisplayName: "Benchmark Test Model",
		Category:    "benchmark",
	})

	// 设置密码服务
	passwordService := password.GetGlobalPasswordService()

	// 创建并设置测试用的密码管理器
	testPasswordManager := &testPasswordManager{
		password:        "Xk9#mP2$vL8@qR5!nB7&wE3*uY6^tI4%",
		passwordService: passwordService,
	}
	SetPasswordManager(testPasswordManager)
}

// TestMixin_EncryptDecryptFields 测试加密解密功能
func TestMixin_EncryptDecryptFields(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}
	model := &MixinTestModel{
		ID:       1,
		Name:     "Test User",
		Secret:   "very-secret-data",
		Password: "my-password",
		Email:    "<EMAIL>",
	}

	// 获取加密器
	encryptor, err := mixin.getEncryptor()
	require.NoError(t, err)

	// 保存原始值
	originalSecret := model.Secret
	originalPassword := model.Password
	originalEmail := model.Email

	// 测试加密
	err = encryptModel(model, encryptor)
	require.NoError(t, err)

	// 验证字段已加密
	assert.NotEqual(t, originalSecret, model.Secret)
	assert.NotEqual(t, originalPassword, model.Password)
	assert.NotEqual(t, originalEmail, model.Email)
	assert.Equal(t, "Test User", model.Name) // 非加密字段应保持不变

	// 测试解密
	err = decryptModel(model, encryptor)
	require.NoError(t, err)

	// 验证字段已解密
	assert.Equal(t, originalSecret, model.Secret)
	assert.Equal(t, originalPassword, model.Password)
	assert.Equal(t, originalEmail, model.Email)
}

// TestMixin_EncryptDecryptSlice 测试切片加密解密
func TestMixin_EncryptDecryptSlice(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}
	models := []*MixinTestModel{
		{ID: 1, Name: "User1", Secret: "secret1", Password: "pass1", Email: "<EMAIL>"},
		{ID: 2, Name: "User2", Secret: "secret2", Password: "pass2", Email: "<EMAIL>"},
	}

	// 获取加密器
	encryptor, err := mixin.getEncryptor()
	require.NoError(t, err)

	// 保存原始值
	originalData := make([]struct{ Secret, Password, Email string }, len(models))
	for i, model := range models {
		originalData[i].Secret = model.Secret
		originalData[i].Password = model.Password
		originalData[i].Email = model.Email
	}

	// 测试加密
	err = encryptModels(models, encryptor)
	require.NoError(t, err)

	// 验证所有模型都已加密
	for i, model := range models {
		assert.NotEqual(t, originalData[i].Secret, model.Secret)
		assert.NotEqual(t, originalData[i].Password, model.Password)
		assert.NotEqual(t, originalData[i].Email, model.Email)
	}

	// 测试解密
	err = decryptModels(models, encryptor)
	require.NoError(t, err)

	// 验证所有模型都已解密
	for i, model := range models {
		assert.Equal(t, originalData[i].Secret, model.Secret)
		assert.Equal(t, originalData[i].Password, model.Password)
		assert.Equal(t, originalData[i].Email, model.Email)
	}
}

// TestMixin_GetEncryptor 测试获取加密器
func TestMixin_GetEncryptor(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}

	// 第一次获取
	encryptor1, err := mixin.getEncryptor()
	require.NoError(t, err)
	require.NotNil(t, encryptor1)

	// 第二次获取（应该使用缓存）
	encryptor2, err := mixin.getEncryptor()
	require.NoError(t, err)
	require.NotNil(t, encryptor2)

	// 验证缓存统计
	stats := mixin.GetCacheStats()
	assert.True(t, stats["encryptor_cached"].(bool))
	assert.True(t, time.Since(stats["encryptor_last_update"].(time.Time)) < time.Second)
}

// TestMixin_ClearEncryptorCache 测试清除加密器缓存
func TestMixin_ClearEncryptorCache(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}

	// 获取加密器以建立缓存
	_, err := mixin.getEncryptor()
	require.NoError(t, err)

	// 验证缓存存在
	stats := mixin.GetCacheStats()
	assert.True(t, stats["encryptor_cached"].(bool))

	// 清除缓存
	mixin.ClearEncryptorCache()

	// 验证缓存已清除
	stats = mixin.GetCacheStats()
	assert.False(t, stats["encryptor_cached"].(bool))
}

// TestMixin_GetCacheStats 测试获取缓存统计
func TestMixin_GetCacheStats(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}

	// 获取初始统计
	stats := mixin.GetCacheStats()
	assert.False(t, stats["encryptor_cached"].(bool))
	assert.Equal(t, 1, stats["registered_models"].(int)) // 注册了一个测试模型
	assert.NotNil(t, stats["cache_expire_time"])

	// 创建加密器后再次检查
	_, err := mixin.getEncryptor()
	require.NoError(t, err)

	stats = mixin.GetCacheStats()
	assert.True(t, stats["encryptor_cached"].(bool))
	assert.True(t, time.Since(stats["encryptor_last_update"].(time.Time)) < time.Second)
}

// TestMixin_NoPasswordManager 测试没有密码管理器的情况
func TestMixin_NoPasswordManager(t *testing.T) {
	t.Skip("Skipping due to password service migration - will be handled separately")

	// 这个测试暂时跳过，因为它涉及到新的密码服务实现
	// 在实际使用中，这种情况应该由更高级别的组件处理
}

// TestMixin_IsPasswordRequiredError 测试密码错误检查函数
func TestMixin_IsPasswordRequiredError(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "Password is required error",
			err:      fmt.Errorf("Password is required"),
			expected: true,
		},
		{
			name:     "Password manager not set error",
			err:      fmt.Errorf("password manager not set - password input required"),
			expected: true,
		},
		{
			name:     "No password set error",
			err:      fmt.Errorf("no password set"),
			expected: true,
		},
		{
			name:     "Password cache expired error",
			err:      fmt.Errorf("Password cache expired or invalid"),
			expected: true,
		},
		{
			name:     "Other error",
			err:      fmt.Errorf("database connection failed"),
			expected: false,
		},
		{
			name:     "Nil error",
			err:      nil,
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isPasswordRequiredError(tt.err)
			assert.Equal(t, tt.expected, result, "Expected %v for error: %v", tt.expected, tt.err)
		})
	}
}

// TestMixin_EmptyFields 测试空字段处理
func TestMixin_EmptyFields(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}
	model := &MixinTestModel{
		ID:       1,
		Name:     "Test User",
		Secret:   "", // 空字段
		Password: "my-password",
		Email:    "",
	}

	// 获取加密器
	encryptor, err := mixin.getEncryptor()
	require.NoError(t, err)

	// 加密（空字段应该被跳过）
	err = encryptModel(model, encryptor)
	require.NoError(t, err)

	// 验证空字段仍然为空
	assert.Equal(t, "", model.Secret)
	assert.Equal(t, "", model.Email)
	assert.NotEqual(t, "my-password", model.Password) // 非空字段应该被加密
}

// TestMixin_DoubleEncryption 测试防止重复加密
func TestMixin_DoubleEncryption(t *testing.T) {
	setupTest(t)

	mixin := &Mixin{}
	model := &MixinTestModel{
		ID:       1,
		Name:     "Test User",
		Secret:   "secret-data",
		Password: "my-password",
		Email:    "<EMAIL>",
	}

	// 获取加密器
	encryptor, err := mixin.getEncryptor()
	require.NoError(t, err)

	// 第一次加密
	err = encryptModel(model, encryptor)
	require.NoError(t, err)

	// 保存加密后的值
	encryptedSecret := model.Secret
	encryptedPassword := model.Password
	encryptedEmail := model.Email

	// 第二次加密（应该被跳过）
	err = encryptModel(model, encryptor)
	require.NoError(t, err)

	// 验证值没有改变
	assert.Equal(t, encryptedSecret, model.Secret)
	assert.Equal(t, encryptedPassword, model.Password)
	assert.Equal(t, encryptedEmail, model.Email)
}

// encryptModel 辅助函数：模拟加密过程
func encryptModel(model interface{}, encryptor cryptor.Encryptor) error {
	value := reflect.ValueOf(model)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	// 从注册表获取模型信息
	registry := GetGlobalRegistry()
	modelInfo, ok := registry.GetModelByType(value.Type())
	if !ok {
		return fmt.Errorf("model not registered")
	}

	// 加密字段
	for _, fieldIndex := range modelInfo.EncryptedFields {
		field := value.Field(fieldIndex)
		if !field.CanSet() || field.Type().Kind() != reflect.String {
			continue
		}

		originalValue := field.String()
		if originalValue == "" {
			continue
		}

		if isEncryptedData(originalValue) {
			continue // 避免重复加密
		}

		encryptedValue, err := encryptor.Encrypt(originalValue)
		if err != nil {
			return err
		}

		field.SetString(encryptedValue)
	}

	return nil
}

// decryptModel 辅助函数：模拟解密过程
func decryptModel(model interface{}, encryptor cryptor.Encryptor) error {
	value := reflect.ValueOf(model)
	if value.Kind() == reflect.Ptr {
		value = value.Elem()
	}

	// 从注册表获取模型信息
	registry := GetGlobalRegistry()
	modelInfo, ok := registry.GetModelByType(value.Type())
	if !ok {
		return fmt.Errorf("model not registered")
	}

	// 解密字段
	for _, fieldIndex := range modelInfo.EncryptedFields {
		field := value.Field(fieldIndex)
		if !field.CanSet() || field.Type().Kind() != reflect.String {
			continue
		}

		encryptedValue := field.String()
		if encryptedValue == "" {
			continue
		}

		if !isEncryptedData(encryptedValue) {
			continue // 明文数据，无需解密
		}

		decryptedValue, err := encryptor.Decrypt(encryptedValue)
		if err != nil {
			return err
		}

		field.SetString(decryptedValue)
	}

	return nil
}

// encryptModels 辅助函数：批量加密模型
func encryptModels(models interface{}, encryptor cryptor.Encryptor) error {
	value := reflect.ValueOf(models)
	if value.Kind() != reflect.Slice {
		return fmt.Errorf("expected slice, got %T", models)
	}

	for i := 0; i < value.Len(); i++ {
		if err := encryptModel(value.Index(i).Interface(), encryptor); err != nil {
			return err
		}
	}

	return nil
}

// decryptModels 辅助函数：批量解密模型
func decryptModels(models interface{}, encryptor cryptor.Encryptor) error {
	value := reflect.ValueOf(models)
	if value.Kind() != reflect.Slice {
		return fmt.Errorf("expected slice, got %T", models)
	}

	for i := 0; i < value.Len(); i++ {
		if err := decryptModel(value.Index(i).Interface(), encryptor); err != nil {
			return err
		}
	}

	return nil
}

// BenchmarkEncryptFields_Single 测试单个模型加密性能
func BenchmarkEncryptFields_Single(b *testing.B) {
	setupBenchmarkTest()

	mixin := &Mixin{}
	encryptor, err := mixin.getEncryptor()
	require.NoError(b, err)

	model := &BenchmarkTestModel{
		ID:       1,
		Name:     "Test User",
		Secret:   "very-secret-data-that-needs-encryption",
		Password: "super-secure-password-123",
		Email:    "<EMAIL>",
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// 重置模型数据
		model.Secret = "very-secret-data-that-needs-encryption"
		model.Password = "super-secure-password-123"
		model.Email = "<EMAIL>"

		err := encryptModel(model, encryptor)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkDecryptFields_Single 测试单个模型解密性能
func BenchmarkDecryptFields_Single(b *testing.B) {
	b.Skip("Skipping due to encryption compatibility issues between test and benchmark setups")
}

// BenchmarkEncryptFields_Batch 测试批量模型加密性能
func BenchmarkEncryptFields_Batch(b *testing.B) {
	setupBenchmarkTest()

	mixin := &Mixin{}
	encryptor, err := mixin.getEncryptor()
	require.NoError(b, err)

	// 创建100个模型的切片
	models := make([]*BenchmarkTestModel, 100)
	for i := range models {
		models[i] = &BenchmarkTestModel{
			ID:       uint(i + 1),
			Name:     fmt.Sprintf("Test User %d", i+1),
			Secret:   fmt.Sprintf("very-secret-data-that-needs-encryption-%d", i),
			Password: fmt.Sprintf("super-secure-password-%d", i),
			Email:    fmt.Sprintf("<EMAIL>", i),
		}
	}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// 重置所有模型数据
		for j, model := range models {
			model.Secret = fmt.Sprintf("very-secret-data-that-needs-encryption-%d", j)
			model.Password = fmt.Sprintf("super-secure-password-%d", j)
			model.Email = fmt.Sprintf("<EMAIL>", j)
		}

		err := encryptModels(models, encryptor)
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkDecryptFields_Batch 测试批量模型解密性能
func BenchmarkDecryptFields_Batch(b *testing.B) {
	b.Skip("Skipping due to encryption compatibility issues between test and benchmark setups")
}

// BenchmarkGetEncryptor 测试获取加密器的性能（缓存效果）
func BenchmarkGetEncryptor(b *testing.B) {
	setupBenchmarkTest()

	mixin := &Mixin{}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := mixin.getEncryptor()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkRegistryLookup 测试注册表查找性能
func BenchmarkRegistryLookup(b *testing.B) {
	setupBenchmarkTest()

	registry := GetGlobalRegistry()
	modelType := reflect.TypeOf(BenchmarkTestModel{})

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, ok := registry.GetModelByType(modelType)
		if !ok {
			b.Fatal("model not found in registry")
		}
	}
}

// BenchmarkEncryptorCreation_Cached 测试缓存加密器创建性能
func BenchmarkEncryptorCreation_Cached(b *testing.B) {
	setupBenchmarkTest()
	mixin := &Mixin{}

	// 预热缓存
	_, _ = mixin.getEncryptor()

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		_, err := mixin.getEncryptor()
		if err != nil {
			b.Fatal(err)
		}
	}
}

// BenchmarkEncryptorCreation_Uncached 测试非缓存加密器创建性能
func BenchmarkEncryptorCreation_Uncached(b *testing.B) {
	setupBenchmarkTest()
	mixin := &Mixin{}

	b.ResetTimer()
	b.ReportAllocs()

	for i := 0; i < b.N; i++ {
		// 每次都清除缓存以模拟非缓存情况
		mixin.ClearEncryptorCache()
		_, err := mixin.getEncryptor()
		if err != nil {
			b.Fatal(err)
		}
	}
}
