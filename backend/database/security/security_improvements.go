package security

import (
	"fmt"
	"log/slog"
	"os"
	"path/filepath"

	"a8.tools/backend/pkg/logger"
	"a8.tools/backend/utils/file"
	"gorm.io/gorm"
)

// SecurityConfig 安全配置
type SecurityConfig struct {
	UseSecureFilePerms bool
	UseSecurePragmas   bool
	BackupInterval     int // 小时
}

// DefaultSecurityConfig 返回默认安全配置
func DefaultSecurityConfig() SecurityConfig {
	return SecurityConfig{
		UseSecureFilePerms: true,
		UseSecurePragmas:   true,
		BackupInterval:     24,
	}
}

// ApplySecurityImprovements 应用安全性改进
func ApplySecurityImprovements(db *gorm.DB, config SecurityConfig) error {
	if config.UseSecureFilePerms {
		if err := secureFilePermissions(); err != nil {
			return fmt.Errorf("设置安全文件权限失败: %w", err)
		}
	}

	if config.UseSecurePragmas {
		if err := applySecurePragmas(db); err != nil {
			return fmt.Errorf("应用安全 PRAGMA 设置失败: %w", err)
		}
	}

	return nil
}

// secureFilePermissions 设置安全的文件权限
func secureFilePermissions() error {
	// 获取应用数据目录
	dataDir, err := file.GetAppDataDir()
	if err != nil {
		return err
	}

	// 设置目录权限为 700 (仅所有者可访问)
	if err := os.Chmod(dataDir, 0700); err != nil {
		logger.Error("设置数据目录权限失败", slog.String("error", err.Error()))
	}

	// 设置数据库目录权限
	dbDir := filepath.Join(dataDir, "databases")
	if err := os.Chmod(dbDir, 0700); err != nil {
		logger.Error("设置数据库目录权限失败", slog.String("error", err.Error()))
	}

	// 设置数据库文件权限为 600 (仅所有者可读写)
	dbFile, err := file.GetDatabaseFile("app.db")
	if err != nil {
		return err
	}

	if err := os.Chmod(dbFile, 0600); err != nil {
		logger.Error("设置数据库文件权限失败", slog.String("error", err.Error()))
	}

	return nil
}

// applySecurePragmas 应用安全的 PRAGMA 设置
func applySecurePragmas(db *gorm.DB) error {
	if db == nil {
		return fmt.Errorf("NotInitialized")
	}

	// 安全 PRAGMA 设置列表
	securityPragmas := []struct {
		pragma      string
		value       string
		description string
	}{
		{"secure_delete", "ON", "启用安全删除，确保删除的数据被完全清除"},
		{"foreign_keys", "ON", "启用外键约束，确保数据完整性"},
		{"cell_size_check", "ON", "启用单元格大小检查，防止数据损坏"},
		{"recursive_triggers", "ON", "启用递归触发器，确保数据一致性"},
		{"trusted_schema", "OFF", "禁用可信模式，增强安全性"},
		{"defer_foreign_keys", "OFF", "禁用延迟外键检查，立即验证约束"},
	}

	// 应用每个 PRAGMA 设置
	for _, pragma := range securityPragmas {
		sql := fmt.Sprintf("PRAGMA %s = %s", pragma.pragma, pragma.value)
		if err := db.Exec(sql).Error; err != nil {
			logger.Error("设置 PRAGMA 失败", slog.String("pragma", pragma.pragma), slog.String("error", err.Error()))
			continue
		}

	}

	// 验证关键设置是否生效
	criticalPragmas := []string{"secure_delete", "foreign_keys"}
	for _, pragma := range criticalPragmas {
		var value string
		sql := fmt.Sprintf("PRAGMA %s", pragma)
		if err := db.Raw(sql).Scan(&value).Error; err != nil {
			return fmt.Errorf("验证 %s 设置失败: %w", pragma, err)
		}

		if value != "1" {
			return fmt.Errorf("关键安全设置 %s 未生效，当前值: %s", pragma, value)
		}
	}

	return nil
}

// VerifyDatabaseSecurity 验证数据库安全性
func VerifyDatabaseSecurity(db *gorm.DB) error {
	if db == nil {
		return fmt.Errorf("NotInitialized")
	}

	// 检查完整性
	var result string
	if err := db.Raw("PRAGMA integrity_check").Scan(&result).Error; err != nil {
		return fmt.Errorf("完整性检查失败: %w", err)
	}

	if result != "ok" {
		return fmt.Errorf("数据库完整性检查失败: %s", result)
	}

	// 检查安全设置
	securityChecks := []struct {
		pragma   string
		expected string
	}{
		{"secure_delete", "1"},
		{"foreign_keys", "1"},
		{"journal_mode", "wal"},
	}

	for _, check := range securityChecks {
		var value string
		sql := fmt.Sprintf("PRAGMA %s", check.pragma)
		if err := db.Raw(sql).Scan(&value).Error; err != nil {
			logger.Error("检查 PRAGMA 失败", slog.String("pragma", check.pragma), slog.String("error", err.Error()))
			continue
		}

		if value != check.expected {
			logger.Error("PRAGMA 设置不正确", slog.String("pragma", check.pragma), slog.String("expected", check.expected), slog.String("actual", value))
		}
	}

	return nil
}

// CreateSecureBackup 创建安全备份
func CreateSecureBackup(db *gorm.DB) error {
	if db == nil {
		return fmt.Errorf("NotInitialized")
	}

	// 获取备份目录
	dataDir, err := file.GetAppDataDir()
	if err != nil {
		return err
	}

	backupDir := filepath.Join(dataDir, "backups")
	if err := os.MkdirAll(backupDir, 0700); err != nil {
		return fmt.Errorf("创建备份目录失败: %w", err)
	}

	// 生成备份文件名（包含时间戳）
	timestamp := os.Getenv("TIMESTAMP")
	if timestamp == "" {
		timestamp = "manual"
	}
	backupFile := filepath.Join(backupDir, fmt.Sprintf("app_backup_%s.db", timestamp))

	// 执行备份
	backupSQL := fmt.Sprintf("VACUUM INTO '%s'", backupFile)
	if err := db.Exec(backupSQL).Error; err != nil {
		return fmt.Errorf("备份数据库失败: %w", err)
	}

	// 设置备份文件权限
	if err := os.Chmod(backupFile, 0600); err != nil {
		logger.Error("设置备份文件权限失败", slog.String("error", err.Error()))
	}

	return nil
}
