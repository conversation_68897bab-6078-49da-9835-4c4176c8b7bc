package database

import (
	"log/slog"

	"a8.tools/backend/database/encryptable"
	"a8.tools/backend/database/models"
	"a8.tools/backend/database/models/account"
	"a8.tools/backend/pkg/logger"
)

// init 包初始化时自动注册所有可加密模型
func init() {
	registerAllEncryptableModels()
}

// registerAllEncryptableModels 注册所有可加密的模型
func registerAllEncryptableModels() {
	registry := encryptable.GetGlobalRegistry()

	// 注册账户相关模型
	modelsList := []struct {
		key  string
		info encryptable.ModelInfo
	}{
		{
			key: "account.email",
			info: encryptable.ModelInfo{
				Model:       &account.Email{},
				TableName:   "emails",
				DisplayName: "Email账户",
				Category:    "account",
			},
		},
		{
			key: "account.discord",
			info: encryptable.ModelInfo{
				Model:       &account.Discord{},
				TableName:   "discords",
				DisplayName: "Discord账户",
				Category:    "account",
			},
		},
		{
			key: "account.telegram",
			info: encryptable.ModelInfo{
				Model:       &account.Telegram{},
				TableName:   "telegrams",
				DisplayName: "Telegram账户",
				Category:    "account",
			},
		},
		{
			key: "account.proxy",
			info: encryptable.ModelInfo{
				Model:       &account.Proxy{},
				TableName:   "proxies",
				DisplayName: "代理配置",
				Category:    "config",
			},
		},
		{
			key: "account.x",
			info: encryptable.ModelInfo{
				Model:       &account.X{},
				TableName:   "xes",
				DisplayName: "X账户",
				Category:    "account",
			},
		},
		{
			key: "models.wallet",
			info: encryptable.ModelInfo{
				Model:       &models.Wallet{},
				TableName:   "wallets",
				DisplayName: "钱包信息",
				Category:    "config",
			},
		},
	}

	// 批量注册模型
	for _, modelDef := range modelsList {
		if err := registry.RegisterModel(modelDef.key, modelDef.info); err != nil {
			logger.Error("注册模型失败", slog.String("model", modelDef.key), slog.String("error", err.Error()))
		}
	}

	// 验证所有注册的模型
	if err := registry.ValidateAllModels(); err != nil {
		logger.Error("模型验证失败", slog.String("error", err.Error()))
	}
}

// RegisterAdditionalModel 动态注册额外的可加密模型
func RegisterAdditionalModel(key string, info encryptable.ModelInfo) error {
	registry := encryptable.GetGlobalRegistry()

	if err := registry.RegisterModel(key, info); err != nil {
		logger.Error("注册额外模型失败", slog.String("model", key), slog.String("error", err.Error()))
		return err
	}

	return nil
}

// GetEncryptableModelsByCategory 根据分类获取可加密模型（便捷函数）
func GetEncryptableModelsByCategory(category string) []encryptable.ModelInfo {
	registry := encryptable.GetGlobalRegistry()
	return registry.GetModelsByCategory(category)
}

// GetAllEncryptableModels 获取所有可加密模型（便捷函数）
func GetAllEncryptableModels() []encryptable.ModelInfo {
	registry := encryptable.GetGlobalRegistry()
	return registry.GetAllModels()
}
