package models

type Browser struct {
	BaseModel
	Name     string `gorm:"column:name;type:text;not null" json:"name"`
	Path     string `gorm:"column:path;type:text;not null" json:"path"`
	Status   string `gorm:"column:status;type:text;not null" json:"status"`
	Remark   string `gorm:"column:remark;type:text;not null" json:"remark"`
	Wallet   string `gorm:"column:wallet;type:text;not null" json:"wallet"`
	Proxy    string `gorm:"column:proxy;type:text;not null" json:"proxy"`
	Discord  string `gorm:"column:discord;type:text;not null" json:"discord"`
	Telegram string `gorm:"column:telegram;type:text;not null" json:"telegram"`
	Twitter  string `gorm:"column:twitter;type:text;not null" json:"twitter"`
}

func (b *Browser) TableName() string {
	return "browser"
}

func (b *Browser) GetID() uint {
	return b.ID
}

func (b *<PERSON>rowser) SetID(id uint) {
	b.ID = id
}
