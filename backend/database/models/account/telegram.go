package account

import (
	"a8.tools/backend/database/encryptable"
	"a8.tools/backend/database/models"
)

type Telegram struct {
	models.BaseModel
	encryptable.Mixin        // 嵌入加密混入
	Account           string `gorm:"column:account;type:text;not null" json:"account"`
	Password          string `gorm:"column:password;type:text;not null" json:"password" encrypt:"true"`
	Remark            string `gorm:"column:remark;type:text;default:''" json:"remark"`
	Group             string `gorm:"column:group;type:text;default:''" json:"group"`
	Labels            string `gorm:"column:labels;type:text;default:''" json:"labels"`
}

func (t *Telegram) TableName() string {
	return "telegram"
}

func (t *Telegram) GetID() uint {
	return t.ID
}

func (t *Telegram) SetID(id uint) {
	t.ID = id
}
