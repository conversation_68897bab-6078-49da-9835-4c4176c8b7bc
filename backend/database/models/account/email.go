package account

import (
	"a8.tools/backend/database/encryptable"
	"a8.tools/backend/database/models"
)

type Email struct {
	models.BaseModel
	encryptable.Mixin        // 嵌入加密混入
	Email             string `gorm:"column:email;type:varchar(255);" json:"email"`
	Password          string `gorm:"column:password;type:varchar(255);" json:"password" encrypt:"true"`
	IMAP              string `gorm:"column:imap;type:varchar(255);" json:"imap"`
	SMTP              string `gorm:"column:smtp;type:varchar(255);" json:"smtp"`
	Proxy             string `gorm:"column:proxy;type:varchar(255);" json:"proxy"`
	Remark            string `gorm:"column:remark;type:varchar(255);" json:"remark"`
}

func (e *Email) TableName() string {
	return "email"
}

func (e *Email) GetID() uint {
	return e.ID
}

func (e *Email) SetID(id uint) {
	e.ID = id
}
