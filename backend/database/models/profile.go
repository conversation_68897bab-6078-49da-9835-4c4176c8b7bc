package models

type Profile struct {
	BaseModel
	Name   string `gorm:"column:name;type:text;not null" json:"name"`
	Path   string `gorm:"column:path;type:text;not null" json:"path"`
	Status string `gorm:"column:status;type:text;not null" json:"status"`
	Remark string `gorm:"column:remark;type:text;not null" json:"remark"`
	Wallet string `gorm:"column:wallet;type:text;not null" json:"wallet"`
}

func (p *Profile) TableName() string {
	return "profile"
}

func (p *Profile) GetID() uint {
	return p.ID
}

func (p *Profile) SetID(id uint) {
	p.ID = id
}
