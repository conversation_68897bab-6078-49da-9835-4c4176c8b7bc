package models

import "a8.tools/backend/database/encryptable"

type Wallet struct {
	BaseModel
	encryptable.Mixin        // 嵌入加密混入
	Network           string `gorm:"column:network;type:varchar(255);" json:"network"`
	Addresses         string `gorm:"column:addresses;type:varchar(255);" json:"addresses"`
	PrivateKey        string `gorm:"column:private_key;type:varchar(255);" json:"privateKey" encrypt:"true"`
	PublicKey         string `gorm:"column:public_key;type:varchar(255);" json:"publicKey" encrypt:"true"`
	Mnemonic          string `gorm:"column:mnemonic;type:varchar(255);" json:"mnemonic" encrypt:"true"`
	Extra             string `gorm:"column:extra;type:text;" json:"extra"`
	Group             string `gorm:"column:group;type:varchar(255);" json:"group"`
	Label             string `gorm:"column:label;type:varchar(255);" json:"label"`
	Remark            string `gorm:"column:remark;type:varchar(255);" json:"remark"`
}

func (w *Wallet) TableName() string {
	return "wallet"
}

func (w *Wallet) GetID() uint {
	return w.ID
}

func (w *Wallet) SetID(id uint) {
	w.ID = id
}
