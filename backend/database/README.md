
# Database 模块 v2.0

**A8Tools 数据库模块**提供了完整的 SQLite 数据库管理解决方案，支持透明的字段级加密、自动优化、安全配置和高性能缓存。

## ✨ 核心特性

- 🔐 **透明加密**：字段级自动加密/解密，保护敏感数据
- 🚀 **高性能**：多级缓存、连接池、WAL 模式优化
- 🛡️ **安全性**：文件权限、PRAGMA 配置、完整性检查
- 🔧 **易用性**：自动模型注册、智能字段识别、构建标签控制
- 📊 **监控**：性能统计、健康检查、调试日志
- 🔄 **灵活性**：密码管理器接口、依赖注入、模块化设计

## 🚀 快速开始

### 1. 数据库初始化

```go
import "a8.tools/backend/database"

// 初始化数据库（不再需要密码参数）
err := database.InitDB()
if err != nil {
    log.Fatal("数据库初始化失败:", err)
}
```

**重要变更**：从 v2.0 开始，`InitDB()` 函数不再需要密码参数。密码管理现在通过专门的密码管理器接口处理，支持更灵活的密码获取方式。

### 2. 获取数据库实例

```go
// 安全获取数据库实例
db, err := database.GetDB()
if err != nil {
    log.Fatal("获取数据库实例失败:", err)
}

// 检查数据库健康状态
if !database.IsDBInitialized() {
    log.Fatal("数据库未初始化")
}
```

### 3. 基本 CRUD 操作

```go
import "a8.tools/backend/database/models/account"

// 创建记录
email := &account.Email{
    Email:    "<EMAIL>",
    Password: "secure-password", // 会自动加密
    IMAP:     "imap.example.com",
    SMTP:     "smtp.example.com",
}

err := db.Create(email).Error
if err != nil {
    log.Fatal("创建记录失败:", err)
}

// 查询记录（密码会自动解密）
var emails []account.Email
err = db.Find(&emails).Error
if err != nil {
    log.Fatal("查询记录失败:", err)
}
```

## 🔐 加密系统

### 核心概念

数据库模块提供了透明的字段级加密功能，敏感数据在存储时自动加密，查询时自动解密。

### 密码管理器架构

从 v2.0 开始，加密系统采用了新的密码管理器架构：

```go
import "a8.tools/backend/database/encryptable"

// 设置密码管理器（通常在应用启动时）
encryptable.SetPasswordManager(yourPasswordManager)

// 获取加密管理器
encryptionManager := encryptable.GetEncryptionManager()
```

**密码管理器接口**：
```go
type PasswordManagerInterface interface {
    GetDataEncryptionKey() ([]byte, error)
    ClearPassword()
}
```

**优势**：
- 支持多种密码获取方式（用户输入、文件、环境变量等）
- 更好的密码缓存和生命周期管理
- 支持密码状态监控和事件通知

### 加密接口

```go
// Encryptable 接口定义
type Encryptable interface {
    GetEncryptedFields() []string
}
```

### 加密混入

```go
import "a8.tools/backend/database/encryptable"

type MyModel struct {
    models.BaseModel
    encryptable.Mixin        // 嵌入加密混入
    
    Username string `json:"username"`
    Password string `json:"password" encrypt:"true"` // 使用标签标记加密字段
    Secret   string `json:"secret" encrypt:"true"`   // 使用标签标记加密字段
}

func (m *MyModel) TableName() string {
    return "my_models"
}
```

**重要变更**：
- 不再需要实现 `GetEncryptedFields()` 方法
- 改用 `encrypt:"true"` 标签标记需要加密的字段
- 系统会自动通过反射识别加密字段并注册到模型注册表
- 支持更好的性能优化和字段索引缓存

### 加密性能优化

系统内置了多级缓存优化：

1. **加密器缓存**：避免重复创建加密器实例，支持5分钟缓存过期
2. **字段反射缓存**：预计算字段索引，避免运行时反射（70.2% 性能提升）
3. **批量处理优化**：支持切片数据的批量加密/解密
4. **模型注册表**：统一管理所有加密模型，避免重复反射操作
5. **智能加密检测**：自动识别已加密数据，避免重复加密

### 性能基准测试

```bash
# 运行加密性能测试
go test -bench=. ./backend/database/encryptable/
```

## 📊 模型注册表

### 自动注册

系统在初始化时自动注册所有可加密模型：

```go
// 在 models_init.go 中自动执行
func init() {
    registerAllEncryptableModels()
}
```

### 手动注册

```go
import "a8.tools/backend/database/encryptable"

// 获取全局注册表
registry := encryptable.GetGlobalRegistry()

// 注册新的加密模型
err := registry.RegisterModel("my.model", encryptable.ModelInfo{
    Model:       &MyModel{},
    TableName:   "my_models",
    DisplayName: "我的模型",
    Category:    "custom",
})
```

### 查询注册的模型

```go
// 获取全局注册表
registry := encryptable.GetGlobalRegistry()

// 获取所有注册的模型
allModels := registry.GetAllModels()

// 按分类获取模型
accountModels := registry.GetModelsByCategory("account")

// 验证所有模型
err := registry.ValidateAllModels()

// 获取模型统计信息
modelCount := registry.GetModelCount()

// 根据类型获取模型
modelInfo, ok := registry.GetModelByType(reflect.TypeOf(&MyModel{}))
```

## 🔒 安全功能

### 数据重新加密

当需要更改主密码时，系统提供了安全的数据重新加密功能：

```go
import "a8.tools/backend/database/security"

// 验证旧密码是否能解密现有数据
err := security.ValidateDataDecryptability(db, oldPassword)
if err != nil {
    log.Fatal("密码验证失败:", err)
}

// 重新加密所有数据
err = database.ReencryptDatabaseData(
    oldPassword, 
    newPassword,
    func(totalTables, processedTables int, currentTable string) {
        fmt.Printf("进度: %d/%d - 正在处理: %s\n", 
            processedTables, totalTables, currentTable)
    },
)
```

### 安全配置

```go
import "a8.tools/backend/database/security"

// 应用安全配置
config := security.DefaultSecurityConfig()
config.UseSecureFilePerms = true
config.UseSecurePragmas = true
config.BackupInterval = 24 // 小时

err := security.ApplySecurityImprovements(db, config)
```

### 数据库完整性验证

```go
// 验证数据库安全性和完整性
err := security.VerifyDatabaseSecurity(db)
if err != nil {
    log.Fatal("安全验证失败:", err)
}
```

## ⚡ 性能优化

### SQLite 优化器

```go
import "a8.tools/backend/database/optimizer"

// 创建优化器
optimizer := optimizer.NewSQLiteOptimizer(db)

// 应用性能优化配置
err := optimizer.OptimizeSettings()
if err != nil {
    log.Printf("优化配置失败: %v", err)
}

// 执行数据库维护
err = optimizer.PerformMaintenance()
if err != nil {
    log.Printf("数据库维护失败: %v", err)
}
```

### 优化配置详情

系统自动应用以下优化配置：

- **WAL 模式**：提高并发性能
- **缓存优化**：64MB 缓存大小
- **内存映射**：256MB mmap 大小
- **连接池**：最大100个连接，10个空闲连接
- **超时设置**：5秒忙等待超时

### 性能监控

```go
// 获取数据库统计信息
stats, err := optimizer.GetDatabaseStats()
if err != nil {
    log.Printf("获取统计信息失败: %v", err)
} else {
    fmt.Printf("数据库大小: %d bytes\n", stats.DatabaseSize)
    fmt.Printf("页面数量: %d\n", stats.PageCount)
    fmt.Printf("空闲页面: %d\n", stats.FreePages)
}
```

## 💾 备份服务

### 创建备份

```go
import "a8.tools/backend/database/backup"

// 获取数据库路径
dbPath, err := database.GetDatabasePath()
if err != nil {
    log.Fatal("获取数据库路径失败:", err)
}

// 创建备份服务
backupService := backup.NewDatabaseBackupService(dbPath)

// 创建备份
backupPath, err := backupService.CreateBackup()
if err != nil {
    log.Fatal("创建备份失败:", err)
}
fmt.Printf("备份已创建: %s\n", backupPath)
```

### 恢复备份

```go
// 恢复数据库
err := backupService.RestoreFromBackup(backupPath)
if err != nil {
    log.Fatal("恢复备份失败:", err)
}

// 验证备份
err = backupService.ValidateBackup(backupPath)
if err != nil {
    log.Fatal("备份验证失败:", err)
}
```

### 安全备份

```go
import "a8.tools/backend/database/security"

// 创建安全备份（包含完整性检查）
err := security.CreateSecureBackup(db)
if err != nil {
    log.Fatal("创建安全备份失败:", err)
}
```

## 🗂️ 数据模型

### 基础模型

所有数据模型都继承自 `BaseModel`：

```go
type BaseModel struct {
    ID        uint           `gorm:"primaryKey;autoIncrement" json:"id"`
    CreatedAt time.Time      `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP" json:"created_at"`
    UpdatedAt time.Time      `gorm:"column:updated_at;type:datetime;default:CURRENT_TIMESTAMP" json:"updated_at"`
    DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;type:datetime;null" json:"deleted_at"`
}
```

### 账户模型

系统支持多种账户类型：

- **Email**：邮箱账户（加密密码）
- **Discord**：Discord 账户（加密密码）
- **Telegram**：Telegram 账户（加密密码）
- **Proxy**：代理账户（加密密码）
- **X**：X(Twitter) 账户（加密密码）

### 钱包模型

```go
type Wallet struct {
    BaseModel
    encryptable.Mixin
    
    Network    string `json:"network"`
    Addresses  string `json:"addresses"`
    PrivateKey string `json:"privateKey"` // 加密存储
    PublicKey  string `json:"publicKey"`  // 加密存储
    Mnemonic   string `json:"mnemonic"`   // 加密存储
    // ... 其他字段
}
```

### 代理管理模型

支持代理订阅、节点和实例管理：

- **ProxySubscription**：代理订阅源
- **ProxyNode**：代理节点
- **ProxyInstance**：运行中的代理实例

## 🔧 高级功能

### 密码重新初始化

```go
// 重新初始化数据库连接（密码变更后）
err := database.ReInitDB()
if err != nil {
    log.Fatal("重新初始化失败:", err)
}
```

**重要变更**：`ReInitDB()` 函数不再需要密码参数。密码管理现在通过密码管理器接口处理，重新初始化时会自动清除加密器缓存并使用新的密码。

### 数据库健康检查

```go
// 检查数据库健康状态
err := database.CheckDBHealth()
if err != nil {
    switch err.Error() {
    case "NotInitialized":
        log.Println("数据库未初始化")
    case "DatabaseFileMissing":
        log.Println("数据库文件缺失")
    default:
        log.Printf("数据库健康检查失败: %v", err)
    }
}
```

### 自定义加密模型

```go
type CustomModel struct {
    models.BaseModel
    encryptable.Mixin
    
    Name   string `json:"name"`
    Secret string `json:"secret" encrypt:"true"`
    Token  string `json:"token" encrypt:"true"`
}

func (c *CustomModel) TableName() string {
    return "custom_models"
}
```

**注意**：使用 `encrypt:"true"` 标签标记加密字段，系统会自动识别并注册到模型注册表中。

## 📝 最佳实践

### 1. 错误处理

```go
db, err := database.GetDB()
if err != nil {
    // 根据错误类型进行不同处理
    switch err.Error() {
    case "NotInitialized":
        // 重新初始化数据库
    case "DatabaseFileMissing":
        // 恢复备份或重新创建
    default:
        // 记录错误并处理
    }
}
```

### 2. 事务处理

```go
tx := db.Begin()
defer func() {
    if r := recover(); r != nil {
        tx.Rollback()
    }
}()

// 执行数据库操作
err := tx.Create(&model).Error
if err != nil {
    tx.Rollback()
    return err
}

// 提交事务
err = tx.Commit().Error
```

### 3. 性能优化

```go
// 批量操作时使用事务
tx := db.Begin()
for _, item := range items {
    tx.Create(&item)
}
tx.Commit()

// 使用预加载避免 N+1 问题
db.Preload("Association").Find(&models)
```

### 4. 安全考虑

- 定期更换主密码
- 定期创建数据库备份
- 监控数据库性能和健康状态
- 使用安全的文件权限设置

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
go test ./backend/database/...

# 运行加密功能测试
go test ./backend/database/encryptable/

# 运行性能基准测试
go test -bench=. ./backend/database/encryptable/
```

### 测试覆盖率

```bash
# 生成测试覆盖率报告
go test -coverprofile=coverage.out ./backend/database/...
go tool cover -html=coverage.out
```

## 🔍 故障排除

### 常见问题

1. **数据库初始化失败**
   - 检查密码是否正确
   - 确认数据库文件权限
   - 验证磁盘空间

2. **加密/解密失败**
   - 验证应用密码设置
   - 检查加密器缓存状态
   - 确认模型实现了 Encryptable 接口

3. **性能问题**
   - 检查 SQLite 优化配置
   - 监控数据库统计信息
   - 考虑增加缓存大小

### 调试模式

系统支持两种运行模式，通过构建标签控制：

#### 调试模式（开发环境）
```bash
# 启用调试模式
go build -tags debug
go run -tags debug main.go
```

**调试模式特性**：
- 详细的 SQL 日志输出
- 彩色日志显示
- 慢查询检测（超过1秒）
- 数据库初始化详细信息
- 加密/解密操作日志

#### 生产模式（默认）
```bash
# 生产模式（默认）
go build
go run main.go
```

**生产模式特性**：
- 完全禁用 SQL 日志（安全）
- 静默模式运行
- 最小化日志输出
- 优化性能配置

#### 运行时检测
```go
import "a8.tools/backend/config"

if config.IsDebugBuild {
    log.Println("运行在调试模式")
} else {
    log.Println("运行在生产模式")
}
```

## 📚 API 参考

### 核心函数

- `InitDB() error`：初始化数据库
- `GetDB() (*gorm.DB, error)`：获取数据库实例
- `IsDBInitialized() bool`：检查初始化状态
- `CheckDBHealth() error`：健康检查
- `ReInitDB() error`：重新初始化
- `ReencryptDatabaseData(oldPassword, newPassword string, callback func(int, int, string)) error`：重新加密数据
- `GetDatabasePath() (string, error)`：获取数据库文件路径

### 加密系统

- `encryptable.SetPasswordManager(manager PasswordManagerInterface)`：设置密码管理器
- `encryptable.GetEncryptionManager() EncryptionManagerInterface`：获取加密管理器
- `encryptable.GetGlobalRegistry() *ModelRegistry`：获取全局模型注册表
- `registry.RegisterModel(key string, info ModelInfo) error`：注册模型
- `registry.GetAllModels() []ModelInfo`：获取所有模型
- `registry.ValidateAllModels() error`：验证模型
- `registry.GetModelsByCategory(category string) []ModelInfo`：按分类获取模型
- `registry.GetModelCount() int`：获取模型数量

### 安全功能

- `security.ValidateDataDecryptability(db *gorm.DB, password string) error`：验证密码
- `security.ApplySecurityImprovements(db *gorm.DB, config SecurityConfig) error`：应用安全配置
- `security.CreateSecureBackup(db *gorm.DB) error`：创建安全备份

## 🤝 贡献指南

1. 新增模型时必须嵌入 `encryptable.Mixin`
2. 敏感字段必须使用 `encrypt:"true"` 标签标记
3. 模型必须实现 `TableName()` 方法
4. 添加适当的测试用例
5. 遵循现有的代码风格和命名约定
6. 更新相关文档
7. 确保模型在 `models_init.go` 中正确注册

## 🔄 版本更新说明

### v2.0 重大变更

#### 密码管理架构重构
- **移除密码参数**：`InitDB()` 和 `ReInitDB()` 不再需要密码参数
- **密码管理器接口**：引入 `PasswordManagerInterface` 统一管理密码
- **依赖注入**：通过 `SetPasswordManager()` 设置密码管理器
- **更好的缓存**：支持5分钟加密器缓存，提升性能

#### 加密字段标记方式变更
- **标签方式**：从 `GetEncryptedFields()` 方法改为 `encrypt:"true"` 标签
- **自动识别**：系统自动通过反射识别加密字段
- **性能优化**：预计算字段索引，避免运行时反射

#### 模型注册表系统
- **统一管理**：所有加密模型通过全局注册表管理
- **自动注册**：在 `models_init.go` 中自动注册所有模型
- **分类管理**：支持按分类获取和管理模型
- **运行时查询**：支持运行时查询模型信息

#### 日志系统改进
- **构建标签控制**：通过 `debug` 标签控制日志级别
- **安全日志**：生产模式下完全禁用 SQL 日志
- **开发友好**：调试模式下提供详细的 SQL 日志

#### 安全性增强
- **文件权限**：自动设置安全的文件权限（700/600）
- **PRAGMA 设置**：应用安全的 SQLite PRAGMA 配置
- **完整性检查**：增强的数据库完整性验证
- **智能加密检测**：避免重复加密已加密的数据

#### 性能优化
- **连接池优化**：更好的连接池配置
- **WAL 模式**：默认启用 WAL 模式提升并发性能
- **缓存优化**：64MB 缓存和 256MB mmap 配置
- **维护任务**：自动化的数据库维护和优化

### 迁移指南

#### 从 v1.x 迁移到 v2.0

1. **更新初始化代码**：
   ```go
   // 旧版本
   err := database.InitDB(password)
   
   // 新版本
   err := database.InitDB()
   ```

2. **设置密码管理器**：
   ```go
   // 实现密码管理器接口
   type MyPasswordManager struct{}
   
   func (m *MyPasswordManager) GetDataEncryptionKey() ([]byte, error) {
       // 返回数据加密密钥
   }
   
   func (m *MyPasswordManager) ClearPassword() {
       // 清除密码缓存
   }
   
   // 设置密码管理器
   encryptable.SetPasswordManager(&MyPasswordManager{})
   ```

3. **更新模型定义**：
   ```go
   // 旧版本
   type MyModel struct {
       models.BaseModel
       encryptable.Mixin
       Password string `json:"password"`
   }
   
   func (m *MyModel) GetEncryptedFields() []string {
       return []string{"Password"}
   }
   
   // 新版本
   type MyModel struct {
       models.BaseModel
       encryptable.Mixin
       Password string `json:"password" encrypt:"true"`
   }
   
   func (m *MyModel) TableName() string {
       return "my_models"
   }
   ```

4. **更新重新初始化代码**：
   ```go
   // 旧版本
   err := database.ReInitDB(newPassword)
   
   // 新版本
   err := database.ReInitDB()
   ```

## 📄 许可证

本项目采用 MIT 许可证。详情请参阅 LICENSE 文件。