package backup

import (
	"errors"
	"fmt"
	"io"
	"log/slog"
	"os"
	"path/filepath"
	"time"

	"a8.tools/backend/pkg/logger"
)

// DatabaseBackupService 数据库备份服务
type DatabaseBackupService struct {
	dbPath string
}

// NewDatabaseBackupService 创建数据库备份服务实例
func NewDatabaseBackupService(dbPath string) *DatabaseBackupService {
	return &DatabaseBackupService{
		dbPath: dbPath,
	}
}

// CreateBackup 创建数据库备份
func (s *DatabaseBackupService) CreateBackup() (string, error) {
	if s.dbPath == "" {
		logger.Error("数据库路径未设置")
		return "", errors.New("数据库路径未设置")
	}

	backupDir := filepath.Dir(s.dbPath)
	backupName := fmt.Sprintf("app_backup_%d.db", time.Now().Unix())
	backupPath := filepath.Join(backupDir, backupName)

	if err := s.copyFile(s.dbPath, backupPath); err != nil {
		logger.Error("复制数据库文件失败", slog.String("error", err.Error()))
		return "", fmt.Errorf("复制数据库文件失败: %w", err)
	}

	logger.Debug("数据库备份已创建", slog.String("path", backupPath))
	return backupPath, nil
}

// RestoreFromBackup 从备份恢复数据库
func (s *DatabaseBackupService) RestoreFromBackup(backupPath string) error {
	if s.dbPath == "" {
		logger.Error("数据库路径未设置")
		return errors.New("数据库路径未设置")
	}

	if err := s.copyFile(backupPath, s.dbPath); err != nil {
		logger.Error("恢复备份失败", slog.String("error", err.Error()))
		return fmt.Errorf("恢复备份失败: %w", err)
	}

	logger.Debug("已从备份恢复数据库", slog.String("path", backupPath))
	return nil
}

// CleanupBackup 清理备份文件
func (s *DatabaseBackupService) CleanupBackup(backupPath string) error {
	if err := os.Remove(backupPath); err != nil {
		logger.Error("清理备份文件失败", slog.String("error", err.Error()))
		return err
	}
	logger.Debug("备份文件已清理", slog.String("path", backupPath))
	return nil
}

// ValidateBackup 验证备份文件是否有效
func (s *DatabaseBackupService) ValidateBackup(backupPath string) error {
	info, err := os.Stat(backupPath)
	if err != nil {
		logger.Error("备份文件不存在", slog.String("error", err.Error()))
		return fmt.Errorf("备份文件不存在: %w", err)
	}

	if info.Size() == 0 {
		logger.Error("备份文件为空", slog.String("path", backupPath))
		return errors.New("备份文件为空")
	}

	return nil
}

// GetBackupInfo 获取备份文件信息
func (s *DatabaseBackupService) GetBackupInfo(backupPath string) (BackupInfo, error) {
	info, err := os.Stat(backupPath)
	if err != nil {
		logger.Error("获取备份文件信息失败", slog.String("error", err.Error()))
		return BackupInfo{}, fmt.Errorf("获取备份文件信息失败: %w", err)
	}

	return BackupInfo{
		Path:    backupPath,
		Size:    info.Size(),
		ModTime: info.ModTime(),
		IsValid: s.ValidateBackup(backupPath) == nil,
	}, nil
}

// copyFile 复制文件
func (s *DatabaseBackupService) copyFile(src, dst string) error {
	sourceFile, err := os.Open(src)
	if err != nil {
		logger.Error("获取备份文件信息失败", slog.String("error", err.Error()))
		return err
	}
	defer sourceFile.Close()

	destFile, err := os.Create(dst)
	if err != nil {
		logger.Error("创建备份文件失败", slog.String("error", err.Error()))
		return err
	}
	defer destFile.Close()

	_, err = io.Copy(destFile, sourceFile)
	if err != nil {
		logger.Error("复制备份文件失败", slog.String("error", err.Error()))
		return err
	}

	return destFile.Sync()
}

// BackupInfo 备份文件信息
type BackupInfo struct {
	Path    string    `json:"path"`
	Size    int64     `json:"size"`
	ModTime time.Time `json:"mod_time"`
	IsValid bool      `json:"is_valid"`
}
