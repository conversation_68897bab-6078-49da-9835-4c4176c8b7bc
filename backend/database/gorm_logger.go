package database

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"a8.tools/backend/pkg/logger"
	gormLogger "gorm.io/gorm/logger"
)

// GormLoggerAdapter 是一个适配器，将 GORM 日志调用转发到我们的 logger 模块
type GormLoggerAdapter struct {
	logger       *logger.Logger
	config       gormLogger.Config
	infoStr      string
	warnStr      string
	errStr       string
	traceStr     string
	traceErrStr  string
	traceWarnStr string
}

// NewGormLoggerAdapter 创建一个新的 GORM logger 适配器
func NewGormLoggerAdapter(config gormLogger.Config) *GormLoggerAdapter {
	// 获取全局 logger 实例
	defaultLogger := logger.Default()
	if defaultLogger == nil {
		// 如果没有全局 logger，创建一个基本的 logger
		loggerConfig := logger.DefaultConfig()
		loggerConfig.OutputConsole = true
		loggerConfig.Level = logger.INFO
		defaultLogger, _ = logger.New(loggerConfig)
	}

	return &GormLoggerAdapter{
		logger:       defaultLogger,
		config:       config,
		infoStr:      "%s\n[info] ",
		warnStr:      "%s\n[warn] ",
		errStr:       "%s\n[error] ",
		traceStr:     "%s\n[%.3fms] [rows:%v] %s",
		traceErrStr:  "%s %s\n[%.3fms] [rows:%v] %s",
		traceWarnStr: "%s %s\n[%.3fms] [rows:%v] %s",
	}
}

// LogMode 设置日志级别
func (l *GormLoggerAdapter) LogMode(level gormLogger.LogLevel) gormLogger.Interface {
	newLogger := *l
	newLogger.config.LogLevel = level
	return &newLogger
}

// Info 记录 info 级别的日志
func (l *GormLoggerAdapter) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.config.LogLevel >= gormLogger.Info {
		l.logger.Info(fmt.Sprintf(l.infoStr, time.Now().Format("2006/01/02 15:04:05")) + fmt.Sprintf(msg, data...))
	}
}

// Warn 记录 warn 级别的日志
func (l *GormLoggerAdapter) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.config.LogLevel >= gormLogger.Warn {
		l.logger.Warn(fmt.Sprintf(l.warnStr, time.Now().Format("2006/01/02 15:04:05")) + fmt.Sprintf(msg, data...))
	}
}

// Error 记录 error 级别的日志
func (l *GormLoggerAdapter) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.config.LogLevel >= gormLogger.Error {
		l.logger.Error(fmt.Sprintf(l.errStr, time.Now().Format("2006/01/02 15:04:05")) + fmt.Sprintf(msg, data...))
	}
}

// Trace 记录 SQL 查询日志
func (l *GormLoggerAdapter) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.config.LogLevel <= gormLogger.Silent {
		return
	}

	elapsed := time.Since(begin)
	switch {
	case err != nil && l.config.LogLevel >= gormLogger.Error && (!l.config.IgnoreRecordNotFoundError || err != gormLogger.ErrRecordNotFound):
		sql, rows := fc()
		if rows == -1 {
			l.logger.Error(fmt.Sprintf(l.traceErrStr, time.Now().Format("2006/01/02 15:04:05"), err, float64(elapsed.Nanoseconds())/1e6, "-", sql))
		} else {
			l.logger.Error(fmt.Sprintf(l.traceErrStr, time.Now().Format("2006/01/02 15:04:05"), err, float64(elapsed.Nanoseconds())/1e6, rows, sql))
		}
	case elapsed > l.config.SlowThreshold && l.config.SlowThreshold != 0 && l.config.LogLevel >= gormLogger.Warn:
		sql, rows := fc()
		slowLog := fmt.Sprintf("SLOW SQL >= %v", l.config.SlowThreshold)
		if rows == -1 {
			l.logger.Warn(fmt.Sprintf(l.traceWarnStr, time.Now().Format("2006/01/02 15:04:05"), slowLog, float64(elapsed.Nanoseconds())/1e6, "-", sql))
		} else {
			l.logger.Warn(fmt.Sprintf(l.traceWarnStr, time.Now().Format("2006/01/02 15:04:05"), slowLog, float64(elapsed.Nanoseconds())/1e6, rows, sql))
		}
	case l.config.LogLevel == gormLogger.Info:
		sql, rows := fc()
		if rows == -1 {
			l.logger.Info(fmt.Sprintf(l.traceStr, time.Now().Format("2006/01/02 15:04:05"), float64(elapsed.Nanoseconds())/1e6, "-", sql))
		} else {
			l.logger.Info(fmt.Sprintf(l.traceStr, time.Now().Format("2006/01/02 15:04:05"), float64(elapsed.Nanoseconds())/1e6, rows, sql))
		}
	}
}

// WithLogger 创建一个使用指定 logger 的新适配器
func (l *GormLoggerAdapter) WithLogger(newLogger *logger.Logger) *GormLoggerAdapter {
	newAdapter := *l
	newAdapter.logger = newLogger
	return &newAdapter
}

// GetConfig 获取当前配置
func (l *GormLoggerAdapter) GetConfig() gormLogger.Config {
	return l.config
}

// NewStructuredGormLogger 创建一个结构化的 GORM logger 适配器
func NewStructuredGormLogger(config gormLogger.Config) *StructuredGormLoggerAdapter {
	// 获取全局 logger 实例
	defaultLogger := logger.Default()
	if defaultLogger == nil {
		// 如果没有全局 logger，创建一个基本的 logger
		loggerConfig := logger.DefaultConfig()
		loggerConfig.OutputFile = logger.GetDefaultLogFile("gorm")
		loggerConfig.OutputConsole = true
		loggerConfig.Level = logger.INFO
		defaultLogger, _ = logger.New(loggerConfig)
	}

	return &StructuredGormLoggerAdapter{
		logger: defaultLogger,
		config: config,
	}
}

// NewStructuredGormLoggerWithLogger 创建一个使用指定 logger 的结构化 GORM logger 适配器
func NewStructuredGormLoggerWithLogger(config gormLogger.Config, customLogger *logger.Logger) *StructuredGormLoggerAdapter {
	if customLogger == nil {
		// 如果传入的 logger 为 nil，回退到使用全局 logger
		return NewStructuredGormLogger(config)
	}

	return &StructuredGormLoggerAdapter{
		logger: customLogger,
		config: config,
	}
}

// StructuredGormLoggerAdapter 是一个结构化的 GORM logger 适配器
type StructuredGormLoggerAdapter struct {
	logger *logger.Logger
	config gormLogger.Config
}

// LogMode 设置日志级别
func (l *StructuredGormLoggerAdapter) LogMode(level gormLogger.LogLevel) gormLogger.Interface {
	newLogger := *l
	newLogger.config.LogLevel = level
	return &newLogger
}

// Info 记录 info 级别的日志
func (l *StructuredGormLoggerAdapter) Info(ctx context.Context, msg string, data ...interface{}) {
	if l.config.LogLevel >= gormLogger.Info {
		l.logger.Info("GORM Info",
			slog.String("message", fmt.Sprintf(msg, data...)),
			slog.String("source", "gorm"),
		)
	}
}

// Warn 记录 warn 级别的日志
func (l *StructuredGormLoggerAdapter) Warn(ctx context.Context, msg string, data ...interface{}) {
	if l.config.LogLevel >= gormLogger.Warn {
		l.logger.Warn("GORM Warning",
			slog.String("message", fmt.Sprintf(msg, data...)),
			slog.String("source", "gorm"),
		)
	}
}

// Error 记录 error 级别的日志
func (l *StructuredGormLoggerAdapter) Error(ctx context.Context, msg string, data ...interface{}) {
	if l.config.LogLevel >= gormLogger.Error {
		l.logger.Error("GORM Error",
			slog.String("message", fmt.Sprintf(msg, data...)),
			slog.String("source", "gorm"),
		)
	}
}

// Trace 记录 SQL 查询日志
func (l *StructuredGormLoggerAdapter) Trace(ctx context.Context, begin time.Time, fc func() (string, int64), err error) {
	if l.config.LogLevel <= gormLogger.Silent {
		return
	}

	elapsed := time.Since(begin)
	switch {
	case err != nil && l.config.LogLevel >= gormLogger.Error && (!l.config.IgnoreRecordNotFoundError || err != gormLogger.ErrRecordNotFound):
		sql, rows := fc()
		l.logger.Error("GORM SQL Error",
			slog.String("sql", sql),
			slog.Duration("elapsed", elapsed),
			slog.Int64("rows", rows),
			slog.String("error", err.Error()),
			slog.String("source", "gorm"),
		)
	case elapsed > l.config.SlowThreshold && l.config.SlowThreshold != 0 && l.config.LogLevel >= gormLogger.Warn:
		sql, rows := fc()
		l.logger.Warn("GORM Slow SQL",
			slog.String("sql", sql),
			slog.Duration("elapsed", elapsed),
			slog.Duration("threshold", l.config.SlowThreshold),
			slog.Int64("rows", rows),
			slog.String("source", "gorm"),
		)
	case l.config.LogLevel == gormLogger.Info:
		sql, rows := fc()
		l.logger.Info("GORM SQL",
			slog.String("sql", sql),
			slog.Duration("elapsed", elapsed),
			slog.Int64("rows", rows),
			slog.String("source", "gorm"),
		)
	}
}

// WithLogger 创建一个使用指定 logger 的新适配器
func (l *StructuredGormLoggerAdapter) WithLogger(newLogger *logger.Logger) *StructuredGormLoggerAdapter {
	newAdapter := *l
	newAdapter.logger = newLogger
	return &newAdapter
}

// GetConfig 获取当前配置
func (l *StructuredGormLoggerAdapter) GetConfig() gormLogger.Config {
	return l.config
}
