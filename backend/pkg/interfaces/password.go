package interfaces

// PasswordManagerInterface 密码管理器接口，用于解耦不同包之间的依赖关系
type PasswordManagerInterface interface {
	// GetDataEncryptionKey 获取数据加密密钥
	GetDataEncryptionKey() ([]byte, error)

	// ClearPassword 清除缓存的密码
	ClearPassword()
}

// EncryptionManagerInterface 加密管理器接口，用于解耦encryptable包
type EncryptionManagerInterface interface {
	// SetPasswordManager 设置密码管理器（依赖注入）
	SetPasswordManager(manager PasswordManagerInterface)

	// ClearEncryptorCache 清除加密器缓存
	ClearEncryptorCache()
}
