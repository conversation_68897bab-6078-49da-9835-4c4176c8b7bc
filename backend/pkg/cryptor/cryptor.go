package cryptor

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"sync"
	"time"

	"golang.org/x/crypto/pbkdf2"
)

const (
	keyLength   = 32 // AES-256
	saltLength  = 32
	iterations  = 10000
	versionByte = 1 // 加密版本标识
)

// 支持的加密版本
const (
	Version1   = 1   // 当前版本
	Version211 = 211 // 旧版本（兼容性支持）
)

// Encryptor 定义了加密器接口
type Encryptor interface {
	Encrypt(plainText string) (string, error)
	Decrypt(cipherText string) (string, error)
	// 批量操作接口
	EncryptBatch(plainTexts []string) ([]string, error)
	DecryptBatch(cipherTexts []string) ([]string, error)
}

// keyCache 密钥缓存结构
type keyCache struct {
	key        []byte
	salt       []byte
	lastUpdate time.Time
	mutex      sync.RWMutex
}

// DefaultEncryptor 默认加密器实现
type DefaultEncryptor struct {
	masterKey []byte
	keyCache  *keyCache
}

// NewEncryptor 创建新的加密器实例
func NewEncryptor(key string) *DefaultEncryptor {
	return &DefaultEncryptor{
		masterKey: []byte(key),
		keyCache:  &keyCache{},
	}
}

// getDerivedKey 获取或生成派生密钥（带缓存）
func (e *DefaultEncryptor) getDerivedKey(salt []byte) []byte {
	e.keyCache.mutex.RLock()

	// 检查缓存是否有效（相同的盐值且在有效期内）
	if len(e.keyCache.key) == keyLength &&
		len(e.keyCache.salt) == saltLength &&
		bytes.Equal(e.keyCache.salt, salt) &&
		time.Since(e.keyCache.lastUpdate) < 10*time.Minute {
		key := make([]byte, keyLength)
		copy(key, e.keyCache.key)
		e.keyCache.mutex.RUnlock()
		return key
	}

	e.keyCache.mutex.RUnlock()

	// 获取写锁重新生成密钥
	e.keyCache.mutex.Lock()
	defer e.keyCache.mutex.Unlock()

	// 双重检查
	if len(e.keyCache.key) == keyLength &&
		len(e.keyCache.salt) == saltLength &&
		bytes.Equal(e.keyCache.salt, salt) &&
		time.Since(e.keyCache.lastUpdate) < 10*time.Minute {
		key := make([]byte, keyLength)
		copy(key, e.keyCache.key)
		return key
	}

	// 生成新的派生密钥
	derivedKey := pbkdf2.Key(e.masterKey, salt, iterations, keyLength, sha256.New)

	// 更新缓存
	e.keyCache.key = make([]byte, keyLength)
	copy(e.keyCache.key, derivedKey)
	e.keyCache.salt = make([]byte, saltLength)
	copy(e.keyCache.salt, salt)
	e.keyCache.lastUpdate = time.Now()

	return derivedKey
}

// Encrypt 加密字符串
func (e *DefaultEncryptor) Encrypt(text string) (string, error) {
	if len(e.masterKey) == 0 {
		return "", errors.New("encryption key not initialized")
	}

	// 生成随机盐值
	salt := make([]byte, saltLength)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return "", fmt.Errorf("生成盐值失败: %v", err)
	}

	// 使用缓存的派生密钥
	key := e.getDerivedKey(salt)

	// 创建 AES-GCM cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 cipher 失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 失败: %v", err)
	}

	// 生成随机 nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("生成 nonce 失败: %v", err)
	}

	// 加密数据
	ciphertext := gcm.Seal(nonce, nonce, []byte(text), nil)

	// 组合最终的加密数据：版本 + 盐值 + 密文
	finalData := make([]byte, 1+len(salt)+len(ciphertext))
	finalData[0] = versionByte
	copy(finalData[1:], salt)
	copy(finalData[1+len(salt):], ciphertext)

	return base64.URLEncoding.EncodeToString(finalData), nil
}

// Decrypt 解密字符串
func (e *DefaultEncryptor) Decrypt(cryptoText string) (string, error) {
	if len(e.masterKey) == 0 {
		return "", errors.New("encryption key not initialized")
	}

	// 解码 base64
	data, err := base64.URLEncoding.DecodeString(cryptoText)
	if err != nil {
		return "", fmt.Errorf("base64 解码失败: %v", err)
	}

	// 验证数据长度
	if len(data) < 1+saltLength {
		return "", errors.New("加密数据长度不足")
	}

	// 获取版本字节
	version := data[0]

	// 根据版本选择解密方法
	switch version {
	case Version1:
		return e.decryptV1(data)
	case Version211:
		return e.decryptV211(data)
	default:
		return "", fmt.Errorf("不支持的加密版本: %d", version)
	}
}

// decryptV1 解密版本1的数据
func (e *DefaultEncryptor) decryptV1(data []byte) (string, error) {
	// 提取盐值和密文
	salt := data[1 : 1+saltLength]
	ciphertext := data[1+saltLength:]

	// 使用缓存的派生密钥
	key := e.getDerivedKey(salt)

	// 创建 AES-GCM cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 cipher 失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 失败: %v", err)
	}

	// 验证数据长度
	if len(ciphertext) < gcm.NonceSize() {
		return "", errors.New("密文长度不足")
	}

	// 提取 nonce 和实际密文
	nonce := ciphertext[:gcm.NonceSize()]
	ciphertext = ciphertext[gcm.NonceSize():]

	// 解密数据
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %v", err)
	}

	return string(plaintext), nil
}

// decryptV211 解密版本211的数据（兼容性支持）
func (e *DefaultEncryptor) decryptV211(data []byte) (string, error) {
	// 版本211使用不同的数据格式
	// 假设版本211使用简单的AES-CBC模式，没有盐值
	if len(data) < 17 { // 1字节版本 + 16字节IV + 至少1字节密文
		return "", errors.New("版本211数据长度不足")
	}

	// 提取IV和密文
	iv := data[1:17]
	ciphertext := data[17:]

	// 使用主密钥的哈希作为加密密钥
	hasher := sha256.New()
	hasher.Write(e.masterKey)
	key := hasher.Sum(nil)

	// 创建 AES-CBC cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 cipher 失败: %v", err)
	}

	// 验证密文长度必须是块大小的倍数
	if len(ciphertext)%aes.BlockSize != 0 {
		return "", errors.New("版本211密文长度无效")
	}

	// 创建 CBC 解密器
	mode := cipher.NewCBCDecrypter(block, iv)

	// 解密数据
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// 移除PKCS7填充
	plaintext, err = e.removePKCS7Padding(plaintext)
	if err != nil {
		return "", fmt.Errorf("移除填充失败: %v", err)
	}

	return string(plaintext), nil
}

// removePKCS7Padding 移除PKCS7填充
func (e *DefaultEncryptor) removePKCS7Padding(data []byte) ([]byte, error) {
	if len(data) == 0 {
		return nil, errors.New("数据为空")
	}

	paddingLen := int(data[len(data)-1])
	if paddingLen > len(data) || paddingLen == 0 {
		return nil, errors.New("无效的填充")
	}

	// 验证填充
	for i := len(data) - paddingLen; i < len(data); i++ {
		if data[i] != byte(paddingLen) {
			return nil, errors.New("填充验证失败")
		}
	}

	return data[:len(data)-paddingLen], nil
}

// EncryptBatch 批量加密字符串
func (e *DefaultEncryptor) EncryptBatch(plainTexts []string) ([]string, error) {
	if len(e.masterKey) == 0 {
		return nil, errors.New("encryption key not initialized")
	}

	if len(plainTexts) == 0 {
		return []string{}, nil
	}

	results := make([]string, len(plainTexts))

	// 为批量操作生成一个共享的盐值和密钥
	salt := make([]byte, saltLength)
	if _, err := io.ReadFull(rand.Reader, salt); err != nil {
		return nil, fmt.Errorf("生成盐值失败: %v", err)
	}

	key := e.getDerivedKey(salt)

	// 创建 AES-GCM cipher（复用）
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, fmt.Errorf("创建 cipher 失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建 GCM 失败: %v", err)
	}

	// 批量处理每个文本
	for i, text := range plainTexts {
		if text == "" {
			results[i] = ""
			continue
		}

		// 为每个文本生成唯一的 nonce
		nonce := make([]byte, gcm.NonceSize())
		if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
			return nil, fmt.Errorf("生成 nonce 失败: %v", err)
		}

		// 加密数据
		ciphertext := gcm.Seal(nonce, nonce, []byte(text), nil)

		// 组合最终的加密数据
		finalData := make([]byte, 1+len(salt)+len(ciphertext))
		finalData[0] = versionByte
		copy(finalData[1:], salt)
		copy(finalData[1+len(salt):], ciphertext)

		results[i] = base64.URLEncoding.EncodeToString(finalData)
	}

	return results, nil
}

// DecryptBatch 批量解密字符串
func (e *DefaultEncryptor) DecryptBatch(cipherTexts []string) ([]string, error) {
	if len(e.masterKey) == 0 {
		return nil, errors.New("encryption key not initialized")
	}

	if len(cipherTexts) == 0 {
		return []string{}, nil
	}

	results := make([]string, len(cipherTexts))

	// 用于缓存相同盐值的密钥和cipher
	var cachedSalt []byte
	var cachedGCM cipher.AEAD

	for i, cryptoText := range cipherTexts {
		if cryptoText == "" {
			results[i] = ""
			continue
		}

		// 解码 base64
		data, err := base64.URLEncoding.DecodeString(cryptoText)
		if err != nil {
			return nil, fmt.Errorf("base64 解码失败 (索引 %d): %v", i, err)
		}

		// 验证数据长度
		if len(data) < 1+saltLength {
			return nil, fmt.Errorf("加密数据长度不足 (索引 %d)", i)
		}

		// 获取版本字节
		version := data[0]

		// 检查版本是否支持
		if version != Version1 && version != Version211 {
			return nil, fmt.Errorf("不支持的加密版本 (索引 %d): %d", i, version)
		}

		// 根据版本选择解密方法
		var plaintext string

		switch version {
		case Version1:
			// 提取盐值和密文
			salt := data[1 : 1+saltLength]
			ciphertext := data[1+saltLength:]

			// 检查是否可以复用之前的cipher
			if cachedGCM == nil || !bytes.Equal(cachedSalt, salt) {
				key := e.getDerivedKey(salt)

				// 创建新的 AES-GCM cipher
				block, err := aes.NewCipher(key)
				if err != nil {
					return nil, fmt.Errorf("创建 cipher 失败 (索引 %d): %v", i, err)
				}

				cachedGCM, err = cipher.NewGCM(block)
				if err != nil {
					return nil, fmt.Errorf("创建 GCM 失败 (索引 %d): %v", i, err)
				}

				// 更新缓存的盐值
				cachedSalt = make([]byte, len(salt))
				copy(cachedSalt, salt)
			}

			// 验证数据长度
			if len(ciphertext) < cachedGCM.NonceSize() {
				return nil, fmt.Errorf("密文长度不足 (索引 %d)", i)
			}

			// 提取 nonce 和实际密文
			nonce := ciphertext[:cachedGCM.NonceSize()]
			actualCiphertext := ciphertext[cachedGCM.NonceSize():]

			// 解密数据
			plaintextBytes, err := cachedGCM.Open(nil, nonce, actualCiphertext, nil)
			if err != nil {
				return nil, fmt.Errorf("解密失败 (索引 %d): %v", i, err)
			}

			plaintext = string(plaintextBytes)

		case Version211:
			var err error
			plaintext, err = e.decryptV211(data)
			if err != nil {
				return nil, fmt.Errorf("解密失败 (索引 %d): %v", i, err)
			}
		default:
			return nil, fmt.Errorf("不支持的加密版本 (索引 %d): %d", i, version)
		}

		results[i] = plaintext
	}

	return results, nil
}
