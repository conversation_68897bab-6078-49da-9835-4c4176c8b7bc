package cryptor

import (
	"encoding/base64"
	"regexp"
)

// isBase64Regex 用于检查字符串是否可能是 Base64 编码。
// 它允许标准的 Base64 字符 (A-Za-z0-9+/) 和 URL 安全的字符 (A-Za-z0-9-_)，以及可选的填充 '='。
var isBase64Regex = regexp.MustCompile(`^[A-Za-z0-9+/_-]*=?=?$`) 

// IsBase64 检查字符串是否是有效的 Base64 编码。
// 它首先使用正则表达式进行快速检查，然后尝试实际解码。
func IsBase64(s string) bool {
	if !isBase64Regex.MatchString(s) {
		return false
	}
	_, err := base64.URLEncoding.DecodeString(s)
	return err == nil
}
