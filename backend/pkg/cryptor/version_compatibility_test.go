package cryptor

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"testing"
)

// TestVersion211Compatibility 测试版本211的兼容性
func TestVersion211Compatibility(t *testing.T) {
	// 创建加密器
	encryptor := NewEncryptor("test-password-123")

	// 模拟版本211的加密数据
	plaintext := "test-data-for-version-211"

	// 创建版本211格式的加密数据
	encryptedData, err := createV211TestData(plaintext, encryptor.masterKey)
	if err != nil {
		t.Fatalf("创建版本211测试数据失败: %v", err)
	}

	// 测试解密
	decrypted, err := encryptor.Decrypt(encryptedData)
	if err != nil {
		t.Fatalf("解密版本211数据失败: %v", err)
	}

	if decrypted != plaintext {
		t.<PERSON><PERSON>rf("解密结果不匹配: 期望 %s, 得到 %s", plaintext, decrypted)
	}
}

// TestVersion211BatchDecryption 测试版本211的批量解密
func TestVersion211BatchDecryption(t *testing.T) {
	encryptor := NewEncryptor("test-password-123")

	plaintexts := []string{
		"test-data-1",
		"test-data-2",
		"test-data-3",
	}

	// 创建混合版本的加密数据
	var encryptedData []string

	// 第一个使用版本1
	encrypted1, err := encryptor.Encrypt(plaintexts[0])
	if err != nil {
		t.Fatalf("加密失败: %v", err)
	}
	encryptedData = append(encryptedData, encrypted1)

	// 第二个使用版本211
	encrypted2, err := createV211TestData(plaintexts[1], encryptor.masterKey)
	if err != nil {
		t.Fatalf("创建版本211数据失败: %v", err)
	}
	encryptedData = append(encryptedData, encrypted2)

	// 第三个使用版本1
	encrypted3, err := encryptor.Encrypt(plaintexts[2])
	if err != nil {
		t.Fatalf("加密失败: %v", err)
	}
	encryptedData = append(encryptedData, encrypted3)

	// 批量解密
	decrypted, err := encryptor.DecryptBatch(encryptedData)
	if err != nil {
		t.Fatalf("批量解密失败: %v", err)
	}

	// 验证结果
	for i, expected := range plaintexts {
		if decrypted[i] != expected {
			t.Errorf("批量解密结果不匹配 [%d]: 期望 %s, 得到 %s", i, expected, decrypted[i])
		}
	}
}

// createV211TestData 创建版本211格式的测试数据
func createV211TestData(plaintext string, masterKey []byte) (string, error) {
	// 使用主密钥的哈希作为加密密钥
	hasher := sha256.New()
	hasher.Write(masterKey)
	key := hasher.Sum(nil)

	// 创建 AES-CBC cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 生成随机IV
	iv := make([]byte, aes.BlockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}

	// 添加PKCS7填充
	paddedData := addPKCS7Padding([]byte(plaintext), aes.BlockSize)

	// 创建 CBC 加密器
	mode := cipher.NewCBCEncrypter(block, iv)

	// 加密数据
	ciphertext := make([]byte, len(paddedData))
	mode.CryptBlocks(ciphertext, paddedData)

	// 组合最终数据：版本(1字节) + IV(16字节) + 密文
	finalData := make([]byte, 1+len(iv)+len(ciphertext))
	finalData[0] = Version211
	copy(finalData[1:], iv)
	copy(finalData[1+len(iv):], ciphertext)

	return base64.URLEncoding.EncodeToString(finalData), nil
}

// addPKCS7Padding 添加PKCS7填充
func addPKCS7Padding(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := make([]byte, padding)
	for i := range padText {
		padText[i] = byte(padding)
	}
	return append(data, padText...)
}
