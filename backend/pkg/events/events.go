package events

import (
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/wailsapp/wails/v3/pkg/application"
)

// EventManager 统一事件管理器
type EventManager struct {
	app       *application.App
	init      atomic.Bool
	initMutex sync.Mutex
}

// 全局事件管理器实例
var (
	globalManager *EventManager
	once          sync.Once
)

// GetManager 获取全局事件管理器实例
func GetManager() *EventManager {
	once.Do(func() {
		globalManager = &EventManager{}
	})
	return globalManager
}

// Init 初始化事件管理器，必须在应用启动时调用
func (em *EventManager) Init(app *application.App) {
	em.initMutex.Lock()
	defer em.initMutex.Unlock()
	if em.init.Load() {
		return // 已经初始化
	}
	em.app = app
	em.init.Store(true)
}

// isInitialized 检查事件管理器是否已初始化
func (em *EventManager) isInitialized() bool {
	return em.init.Load()
}

// IsInitialized 检查事件管理器是否已初始化（公共方法）
func (em *EventManager) IsInitialized() bool {
	return em.isInitialized()
}

// EventData 事件数据结构
type EventData struct {
	Type    EventType   `json:"type"`
	Payload interface{} `json:"payload,omitempty"`
	Source  string      `json:"source,omitempty"`
	Time    int64       `json:"time"`
}

func getCurrentTimestamp() int64 {
	return time.Now().Unix()
}

// Emit 发送自定义事件
func (em *EventManager) Emit(eventType EventType, payload interface{}) error {
	if !em.isInitialized() {
		return fmt.Errorf("event manager not initialized")
	}

	data := EventData{
		Type:    eventType,
		Payload: payload,
		Time:    getCurrentTimestamp(),
	}

	em.app.Event.Emit(string(eventType), data)
	return nil
}

// EmitWithSource 发送带有来源的自定义事件
func (em *EventManager) EmitWithSource(eventType EventType, payload interface{}, source string) error {
	if !em.isInitialized() {
		return fmt.Errorf("event manager not initialized")
	}

	data := EventData{
		Type:    eventType,
		Payload: payload,
		Source:  source,
		Time:    getCurrentTimestamp(),
	}

	em.app.Event.Emit(string(eventType), data)
	return nil
}

// On 监听自定义事件
func (em *EventManager) On(eventType EventType, callback func(data EventData)) {
	if !em.isInitialized() {
		return
	}
	em.app.Event.On(string(eventType), func(event *application.CustomEvent) {
		if eventData, ok := event.Data.(EventData); ok {
			callback(eventData)
		}
	})
}

// Once 监听一次自定义事件
func (em *EventManager) Once(eventType EventType, callback func(data EventData)) {
	if !em.isInitialized() {
		return
	}
	em.app.Event.OnMultiple(string(eventType), func(event *application.CustomEvent) {
		if eventData, ok := event.Data.(EventData); ok {
			callback(eventData)
		}
	}, 1)
}

// Off 取消监听自定义事件
func (em *EventManager) Off(eventType EventType) {
	if !em.isInitialized() {
		return
	}
	em.app.Event.Off(string(eventType))
}

// EmitError 发送错误事件
func (em *EventManager) EmitError(source string, err error) error {
	payload := ErrorPayload{
		Message: err.Error(),
		Source:  source,
	}
	return em.EmitWithSource(EventError, payload, "system")
}

// EmitProgress 发送进度事件
func (em *EventManager) EmitProgress(taskID string, progress float64, message string) error {
	payload := ProgressPayload{
		TaskID:   taskID,
		Progress: progress,
		Message:  message,
	}
	return em.Emit(EventProgress, payload)
}

// EmitTaskStart 发送任务开始事件
func (em *EventManager) EmitTaskStart(taskID, taskType string) error {
	payload := TaskPayload{
		TaskID:   taskID,
		TaskType: taskType,
		Status:   "started",
	}
	return em.Emit(EventTaskStarted, payload)
}

// EmitTaskComplete 发送任务完成事件
func (em *EventManager) EmitTaskComplete(taskID string, result interface{}) error {
	payload := TaskPayload{
		TaskID: taskID,
		Status: "completed",
		Result: result,
	}
	return em.Emit(EventTaskComplete, payload)
}

// EmitTaskFailed 发送任务失败事件
func (em *EventManager) EmitTaskFailed(taskID string, err error) error {
	payload := TaskPayload{
		TaskID: taskID,
		Status: "failed",
		Error:  err.Error(),
	}
	return em.Emit(EventTaskFailed, payload)
}

// EmitDataUpdate 发送数据更新事件
func (em *EventManager) EmitDataUpdate(dataType string, data interface{}) error {
	payload := DataPayload{
		Type:   dataType,
		Action: "update",
		Data:   data,
	}
	return em.Emit(EventDataUpdated, payload)
}

// EmitDataCreate 发送数据创建事件
func (em *EventManager) EmitDataCreate(dataType string, data interface{}, id string) error {
	payload := DataPayload{
		Type:   dataType,
		Action: "create",
		Data:   data,
		ID:     id,
	}
	return em.Emit(EventDataCreated, payload)
}

// EmitDataDelete 发送数据删除事件
func (em *EventManager) EmitDataDelete(dataType string, id string) error {
	payload := DataPayload{
		Type:   dataType,
		Action: "delete",
		ID:     id,
	}
	return em.Emit(EventDataDeleted, payload)
}

// EmitBrowserAction 发送浏览器操作事件
func (em *EventManager) EmitBrowserAction(action, url string, data interface{}) error {
	payload := BrowserPayload{
		Action: action,
		URL:    url,
		Data:   data,
	}
	return em.Emit(EventBrowserProgress, payload)
}

// EmitWalletAction 发送钱包操作事件
func (em *EventManager) EmitWalletAction(action, address, network string, data interface{}) error {
	payload := WalletPayload{
		Action:  action,
		Address: address,
		Network: network,
		Data:    data,
	}
	return em.Emit(EventWalletTransaction, payload)
}

// EmitAirdropAction 发送空投操作事件
func (em *EventManager) EmitAirdropAction(action string, airdrop interface{}, status string) error {
	payload := AirdropPayload{
		Action:  action,
		Airdrop: airdrop,
		Status:  status,
	}
	return em.Emit(EventAirdropProgress, payload)
}
