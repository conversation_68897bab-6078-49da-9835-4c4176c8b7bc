package events

// EventType 定义事件类型
type EventType string

// 定义标准事件类型
const (
	// 系统事件
	EventApplicationStarted    EventType = "application.started"
	EventApplicationStopped    EventType = "application.stopped"
	EventApplicationForeground EventType = "application.foreground"
	EventApplicationBackground EventType = "application.background"
	EventApplicationUnlocked   EventType = "application.unlocked"
	EventApplicationLocked     EventType = "application.locked"

	// 窗口事件
	EventWindowReady      EventType = "window.ready"
	EventWindowFocus      EventType = "window.focus"
	EventWindowBlur       EventType = "window.blur"
	EventWindowShow       EventType = "window.show"
	EventWindowHide       EventType = "window.hide"
	EventWindowMaximize   EventType = "window.maximize"
	EventWindowUnmaximize EventType = "window.unmaximize"
	EventWindowMinimize   EventType = "window.minimize"
	EventWindowRestore    EventType = "window.restore"
	EventWindowResize     EventType = "window.resize"
	EventWindowMove       EventType = "window.move"
	EventWindowClose      EventType = "window.close"
	EventWindowClosing    EventType = "window.closing"

	// 任务相关事件
	EventTaskStarted   EventType = "task.started"
	EventTaskProgress  EventType = "task.progress"
	EventTaskComplete  EventType = "task.complete"
	EventTaskFailed    EventType = "task.failed"
	EventTaskCancelled EventType = "task.cancelled"

	// 数据相关事件
	EventDataUpdated EventType = "data.updated"
	EventDataCreated EventType = "data.created"
	EventDataDeleted EventType = "data.deleted"
	EventDataSynced  EventType = "data.synced"

	// 网络相关事件
	EventNetworkConnected    EventType = "network.connected"
	EventNetworkDisconnected EventType = "network.disconnected"
	EventNetworkError        EventType = "network.error"

	// 浏览器自动化事件
	EventBrowserStarted  EventType = "browser.started"
	EventBrowserStopped  EventType = "browser.stopped"
	EventBrowserError    EventType = "browser.error"
	EventBrowserProgress EventType = "browser.progress"

	// 钱包相关事件
	EventWalletConnected    EventType = "wallet.connected"
	EventWalletDisconnected EventType = "wallet.disconnected"
	EventWalletBalance      EventType = "wallet.balance"
	EventWalletTransaction  EventType = "wallet.transaction"

	// 空投相关事件
	EventAirdropFound    EventType = "airdrop.found"
	EventAirdropClaimed  EventType = "airdrop.claimed"
	EventAirdropExpired  EventType = "airdrop.expired"
	EventAirdropError    EventType = "airdrop.error"
	EventAirdropProgress EventType = "airdrop.progress"

	// 密码相关事件
	EventPasswordChangeStarted  EventType = "password.change.started"
	EventPasswordChangeProgress EventType = "password.change.progress"
	EventPasswordChangeComplete EventType = "password.change.complete"
	EventPasswordChangeFailed   EventType = "password.change.failed"
	EventPasswordSet            EventType = "password.set"
	EventPasswordVerified       EventType = "password.verified"
	EventPasswordCleared        EventType = "password.cleared"
	EventPasswordUpdated        EventType = "password.updated"
	EventPasswordInvalid        EventType = "password.invalid"
	EventPasswordRequired       EventType = "password.required"
	EventPasswordCacheExpired   EventType = "password.cache.expired"
	EventPasswordError          EventType = "password.error"

	// 错误事件
	EventError EventType = "error"

	// 通用事件
	EventProgress EventType = "progress"
	EventStatus   EventType = "status"
	EventWarning  EventType = "warning"
	EventInfo     EventType = "info"
)

// IsSystemEvent 检查是否为系统事件
func (et EventType) IsSystemEvent() bool {
	systemEvents := map[EventType]bool{
		EventApplicationStarted:    true,
		EventApplicationStopped:    true,
		EventApplicationForeground: true,
		EventApplicationBackground: true,
		EventApplicationUnlocked:   true,
		EventApplicationLocked:     true,
		EventWindowReady:           true,
		EventWindowFocus:           true,
		EventWindowBlur:            true,
		EventWindowShow:            true,
		EventWindowHide:            true,
		EventWindowMaximize:        true,
		EventWindowUnmaximize:      true,
		EventWindowMinimize:        true,
		EventWindowRestore:         true,
		EventWindowResize:          true,
		EventWindowMove:            true,
		EventWindowClose:           true,
		EventWindowClosing:         true,
	}
	return systemEvents[et]
}

// IsTaskEvent 检查是否为任务事件
func (et EventType) IsTaskEvent() bool {
	taskEvents := map[EventType]bool{
		EventTaskStarted:   true,
		EventTaskProgress:  true,
		EventTaskComplete:  true,
		EventTaskFailed:    true,
		EventTaskCancelled: true,
	}
	return taskEvents[et]
}

// IsDataEvent 检查是否为数据事件
func (et EventType) IsDataEvent() bool {
	dataEvents := map[EventType]bool{
		EventDataUpdated: true,
		EventDataCreated: true,
		EventDataDeleted: true,
		EventDataSynced:  true,
	}
	return dataEvents[et]
}

// IsPasswordEvent 检查是否为密码事件
func (et EventType) IsPasswordEvent() bool {
	passwordEvents := map[EventType]bool{
		EventPasswordChangeStarted:  true,
		EventPasswordChangeProgress: true,
		EventPasswordChangeComplete: true,
		EventPasswordChangeFailed:   true,
		EventPasswordSet:            true,
		EventPasswordVerified:       true,
		EventPasswordCleared:        true,
		EventPasswordUpdated:        true,
		EventPasswordInvalid:        true,
		EventPasswordRequired:       true,
		EventPasswordCacheExpired:   true,
		EventPasswordError:          true,
	}
	return passwordEvents[et]
}

// String 返回事件类型的字符串表示
func (et EventType) String() string {
	return string(et)
}

// ParseEventType 将字符串解析为事件类型
func ParseEventType(s string) (EventType, error) {
	eventType := EventType(s)
	// 这里可以添加验证逻辑
	return eventType, nil
}

// EventCategory 事件分类
type EventCategory string

const (
	CategorySystem   EventCategory = "system"
	CategoryTask     EventCategory = "task"
	CategoryData     EventCategory = "data"
	CategoryNetwork  EventCategory = "network"
	CategoryBrowser  EventCategory = "browser"
	CategoryWallet   EventCategory = "wallet"
	CategoryAirdrop  EventCategory = "airdrop"
	CategoryPassword EventCategory = "password"
	CategoryError    EventCategory = "error"
	CategoryGeneral  EventCategory = "general"
)

// GetCategory 获取事件所属的分类
func (et EventType) GetCategory() EventCategory {
	switch {
	case et.IsSystemEvent():
		return CategorySystem
	case et.IsTaskEvent():
		return CategoryTask
	case et.IsDataEvent():
		return CategoryData
	case et == EventNetworkConnected || et == EventNetworkDisconnected || et == EventNetworkError:
		return CategoryNetwork
	case et == EventBrowserStarted || et == EventBrowserStopped || et == EventBrowserError || et == EventBrowserProgress:
		return CategoryBrowser
	case et == EventWalletConnected || et == EventWalletDisconnected || et == EventWalletBalance || et == EventWalletTransaction:
		return CategoryWallet
	case et == EventAirdropFound || et == EventAirdropClaimed || et == EventAirdropExpired || et == EventAirdropError || et == EventAirdropProgress:
		return CategoryAirdrop
	case et.IsPasswordEvent():
		return CategoryPassword
	case et == EventError:
		return CategoryError
	default:
		return CategoryGeneral
	}
}

// ProgressPayload 进度事件载荷
type ProgressPayload struct {
	TaskID   string  `json:"task_id"`
	Progress float64 `json:"progress"`
	Message  string  `json:"message,omitempty"`
	Detail   string  `json:"detail,omitempty"`
}

// ErrorPayload 错误事件载荷
type ErrorPayload struct {
	Message string                 `json:"message"`
	Code    string                 `json:"code,omitempty"`
	Source  string                 `json:"source,omitempty"`
	Details map[string]interface{} `json:"details,omitempty"`
}

// TaskPayload 任务事件载荷
type TaskPayload struct {
	TaskID   string      `json:"task_id"`
	TaskType string      `json:"task_type,omitempty"`
	Status   string      `json:"status"`
	Result   interface{} `json:"result,omitempty"`
	Error    string      `json:"error,omitempty"`
}

// DataPayload 数据事件载荷
type DataPayload struct {
	Type   string      `json:"type"`
	Action string      `json:"action"`
	Data   interface{} `json:"data,omitempty"`
	ID     string      `json:"id,omitempty"`
}

// BrowserPayload 浏览器事件载荷
type BrowserPayload struct {
	Action string      `json:"action"`
	URL    string      `json:"url,omitempty"`
	Status string      `json:"status,omitempty"`
	Data   interface{} `json:"data,omitempty"`
}

// WalletPayload 钱包事件载荷
type WalletPayload struct {
	Action  string      `json:"action"`
	Address string      `json:"address,omitempty"`
	Balance string      `json:"balance,omitempty"`
	Network string      `json:"network,omitempty"`
	Data    interface{} `json:"data,omitempty"`
}

// AirdropPayload 空投事件载荷
type AirdropPayload struct {
	Action  string      `json:"action"`
	Airdrop interface{} `json:"airdrop,omitempty"`
	Status  string      `json:"status,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// PasswordChangePayload 密码修改事件载荷
type PasswordChangePayload struct {
	TotalTables     int     `json:"total_tables"`     // 总表数
	ProcessedTables int     `json:"processed_tables"` // 已处理表数
	CurrentTable    string  `json:"current_table"`    // 当前处理表
	Progress        float64 `json:"progress"`         // 进度百分比 (0-100)
	Stage           string  `json:"stage"`            // 当前阶段描述
}

// PasswordOperationPayload 密码操作事件载荷
type PasswordOperationPayload struct {
	Operation string `json:"operation"`         // 操作类型 (set, verify, clear)
	Success   bool   `json:"success"`           // 操作是否成功
	Message   string `json:"message,omitempty"` // 相关消息
	Error     string `json:"error,omitempty"`   // 错误信息
}

// 密码修改进度阶段常量
const (
	StagePreparingReencryption = "准备重新加密"
	StageReencryptingData      = "重新加密数据"
	StageUpdatingPassword      = "更新密码"
	StagePasswordUpdated       = "更新密码完成"
	StageCompleted             = "密码修改完成"
)
