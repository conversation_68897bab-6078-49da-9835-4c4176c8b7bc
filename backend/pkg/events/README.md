# Wails3 事件系统工具包

基于 Wails v3 的现代化事件系统，专为 A8Tools 加密货币空投猎人桌面应用设计。

## 🚀 特性

- **统一的事件管理**：全局单例模式，避免重复初始化
- **类型安全**：通过结构化载荷（Payload）实现类型安全，减少运行时错误
- **易用性**：简洁、统一的 API 设计，直接在 `EventManager` 上操作
- **扩展性**：易于添加新的事件类型和载荷
- **调试友好**：包含完整的测试和示例

## 📦 安装

```go
import "a8.tools/backend/pkg/events"
```

## 📁 文件结构

```
pkg/events/
├── README.md     # 文档说明
├── events.go     # 事件管理器实现
└── types.go      # 事件类型定义和载荷结构
```

### 核心文件说明

- **`events.go`** - 包含 `EventManager` 的完整实现，提供事件发送和监听的核心功能
- **`types.go`** - 统一定义所有事件类型常量、事件分类和载荷结构体，包含：
  - `EventType` 类型定义和74个预定义事件常量
  - `EventCategory` 分类系统
  - 7种载荷结构体（`ProgressPayload`、`ErrorPayload`、`TaskPayload`等）
  - 类型检查和转换辅助方法

## 🎯 快速开始

### 1. 初始化事件管理器

在应用启动时初始化，将 `*application.App` 实例传递给它。此操作只需执行一次。

```go
package main

import (
    "github.com/wailsapp/wails/v3/pkg/application"
    "a8.tools/backend/pkg/events"
)

func main() {
    app := application.New(application.Options{})
    
    // 初始化事件管理器
    events.GetManager().Init(app)
    
    // ... 其他初始化代码
}
```

### 2. 发送事件

所有事件都通过全局的 `EventManager` 实例发送。API 设计统一且类型安全。

```go
import (
    "errors"
    "a8.tools/backend/pkg/events"
)

func doSomething() {
    manager := events.GetManager()

    // 发送任务开始事件
    manager.EmitTaskStart("task-001", "数据同步")

    // 发送进度事件
    manager.EmitProgress("task-001", 0.5, "处理中...")

    // 发送数据更新事件
    manager.EmitDataUpdate("wallet", map[string]interface{}{
        "address": "0x123...",
        "balance": "2.0 ETH",
    })

    // 发送任务完成事件
    manager.EmitTaskComplete("task-001", map[string]interface{}{
        "result": "success",
        "data":   "处理结果",
    })

    // 发送错误事件
    manager.EmitError("数据同步器", errors.New("连接超时"))
}
```

### 3. 监听事件

#### 前端监听（JavaScript/React）

前端监听方式保持不变，事件载荷会自动序列化为 JSON。

```javascript
// 监听任务进度事件
window.runtime.EventsOn('task.progress', (data) => {
    // data.payload 是一个 ProgressPayload 结构对应的 JSON 对象
    console.log('任务进度:', data.payload.task_id, data.payload.progress);
    updateProgressBar(data.payload.task_id, data.payload.progress);
});

// 监听错误事件
window.runtime.EventsOnce('error', (data) => {
    // data.payload 是一个 ErrorPayload 结构对应的 JSON 对象
    console.error('错误:', data.payload.message);
    showErrorNotification(data.payload.message);
});
```

#### 后端监听

后端监听时，回调函数直接接收 `events.EventData` 类型。

```go
import (
    "encoding/json"
    "fmt"
    "a8.tools/backend/pkg/events"
)

// decodePayload 是一个辅助函数，用于将 interface{} 解码到具体的 payload 结构体
func decodePayload(data interface{}, target interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return fmt.Errorf("无法序列化 payload: %w", err)
	}
	if err := json.Unmarshal(bytes, target); err != nil {
		return fmt.Errorf("无法反序列化到目标结构: %w", err)
	}
	return nil
}

func setupListeners() {
    manager := events.GetManager()

    // 监听任务进度事件
    manager.On(events.EventTaskProgress, func(data events.EventData) {
        var payload events.ProgressPayload
        if err := decodePayload(data.Payload, &payload); err == nil {
            fmt.Printf("任务进度: %s - %.2f%%\n", payload.TaskID, payload.Progress * 100)
        }
    })

    // 只监听一次的错误事件
    manager.Once(events.EventError, func(data events.EventData) {
        var payload events.ErrorPayload
        if err := decodePayload(data.Payload, &payload); err == nil {
            fmt.Printf("错误来源: %s, 消息: %s\n", payload.Source, payload.Message)
        }
    })
}
```

## 📋 事件类型

### 预定义事件类型

系统提供了丰富的预定义事件类型，覆盖系统、任务、数据、网络、浏览器、钱包和空投等多个维度：

#### 系统事件
- `EventApplicationStarted` - 应用启动
- `EventApplicationStopped` - 应用停止
- `EventApplicationForeground` - 应用前台
- `EventApplicationBackground` - 应用后台
- `EventWindowReady` - 窗口就绪
- `EventWindowFocus` - 窗口获得焦点
- `EventWindowBlur` - 窗口失去焦点
- 更多窗口事件...

#### 任务事件
- `EventTaskStarted` - 任务开始
- `EventTaskProgress` - 任务进度
- `EventTaskComplete` - 任务完成
- `EventTaskFailed` - 任务失败
- `EventTaskCancelled` - 任务取消

#### 数据事件
- `EventDataUpdated` - 数据更新
- `EventDataCreated` - 数据创建
- `EventDataDeleted` - 数据删除
- `EventDataSynced` - 数据同步

#### 网络事件
- `EventNetworkConnected` - 网络连接
- `EventNetworkDisconnected` - 网络断开
- `EventNetworkError` - 网络错误

#### 浏览器自动化事件
- `EventBrowserStarted` - 浏览器启动
- `EventBrowserStopped` - 浏览器停止
- `EventBrowserError` - 浏览器错误
- `EventBrowserProgress` - 浏览器进度

#### 钱包事件
- `EventWalletConnected` - 钱包连接
- `EventWalletDisconnected` - 钱包断开
- `EventWalletBalance` - 钱包余额
- `EventWalletTransaction` - 钱包交易

#### 空投事件
- `EventAirdropFound` - 发现空投
- `EventAirdropClaimed` - 领取空投
- `EventAirdropExpired` - 空投过期
- `EventAirdropError` - 空投错误
- `EventAirdropProgress` - 空投进度

#### 通用事件
- `EventError` - 错误事件
- `EventProgress` - 进度事件
- `EventStatus` - 状态事件
- `EventWarning` - 警告事件
- `EventInfo` - 信息事件

### 事件分类

事件类型支持自动分类，可以通过 `GetCategory()` 方法获取事件所属的分类：

```go
eventType := events.EventTaskProgress
category := eventType.GetCategory() // 返回 CategoryTask
```

支持的分类包括：
- `CategorySystem` - 系统事件
- `CategoryTask` - 任务事件
- `CategoryData` - 数据事件
- `CategoryNetwork` - 网络事件
- `CategoryBrowser` - 浏览器事件
- `CategoryWallet` - 钱包事件
- `CategoryAirdrop` - 空投事件
- `CategoryError` - 错误事件
- `CategoryGeneral` - 通用事件

## 🛠️ 高级用法

### 自定义事件

发送自定义事件时，可以直接传递一个结构体或者 `map[string]interface{}` 作为载荷。

```go
// 定义自定义载荷
type CustomActionPayload struct {
    UserID    int       `json:"user_id"`
    Action    string    `json:"action"`
    Timestamp time.Time `json:"timestamp"`
}

// 发送自定义事件
manager.Emit("custom.user_action", CustomActionPayload{
    UserID: 123,
    Action: "button_click",
    Timestamp: time.Now(),
})
```

### 事件载荷结构

预定义的载荷结构体（在 `types.go` 中定义）提供了类型安全和字段提示：

#### `ProgressPayload` - 进度事件载荷
```go
type ProgressPayload struct {
    TaskID   string  `json:"task_id"`
    Progress float64 `json:"progress"`
    Message  string  `json:"message,omitempty"`
    Detail   string  `json:"detail,omitempty"`
}
```

#### `ErrorPayload` - 错误事件载荷
```go
type ErrorPayload struct {
    Message string                 `json:"message"`
    Code    string                 `json:"code,omitempty"`
    Source  string                 `json:"source,omitempty"`
    Details map[string]interface{} `json:"details,omitempty"`
}
```

#### `TaskPayload` - 任务事件载荷
```go
type TaskPayload struct {
    TaskID   string      `json:"task_id"`
    TaskType string      `json:"task_type,omitempty"`
    Status   string      `json:"status"`
    Result   interface{} `json:"result,omitempty"`
    Error    string      `json:"error,omitempty"`
}
```

#### `DataPayload` - 数据事件载荷
```go
type DataPayload struct {
    Type   string      `json:"type"`
    Action string      `json:"action"`
    Data   interface{} `json:"data,omitempty"`
    ID     string      `json:"id,omitempty"`
}
```

#### `BrowserPayload` - 浏览器事件载荷
```go
type BrowserPayload struct {
    Action string      `json:"action"`
    URL    string      `json:"url,omitempty"`
    Status string      `json:"status,omitempty"`
    Data   interface{} `json:"data,omitempty"`
}
```

#### `WalletPayload` - 钱包事件载荷
```go
type WalletPayload struct {
    Action  string      `json:"action"`
    Address string      `json:"address,omitempty"`
    Balance string      `json:"balance,omitempty"`
    Network string      `json:"network,omitempty"`
    Data    interface{} `json:"data,omitempty"`
}
```

#### `AirdropPayload` - 空投事件载荷
```go
type AirdropPayload struct {
    Action  string      `json:"action"`
    Airdrop interface{} `json:"airdrop,omitempty"`
    Status  string      `json:"status,omitempty"`
    Error   string      `json:"error,omitempty"`
}
```

## 🧪 测试

运行测试：

```bash
cd backend/pkg/events
go test -v
```

## 📝 最佳实践

1.  **正确初始化**：必须在应用启动时调用 `Init(app)`。
2.  **使用常量**：始终使用预定义的 `EventType` 常量。
3.  **错误处理**：检查 `Emit` 系列方法返回的 `error`。
4.  **类型安全**：尽可能使用预定义的 `Payload` 结构体，避免直接使用 `map`。
5.  **事件命名**：遵循 `category.action` 的点分命名法。
6.  **内存管理**：对于不再需要的长期监听器，使用 `Off` 方法进行清理。

## 📈 版本历史

### v1.1.0 (当前版本)
- **文件结构优化**：将 `helpers.go` 合并到 `types.go`，简化包结构
- **统一类型定义**：所有事件类型、分类和载荷结构现在都在 `types.go` 中统一管理
- **提升内聚性**：相关的类型定义和结构体现在位于同一文件中，提高代码的可读性和维护性
- **保持向后兼容**：API 接口保持不变，现有代码无需修改

### v1.0.0
- 初始版本，包含基础的事件管理器和类型定义
- 支持 74 种预定义事件类型
- 提供 7 种载荷结构体
- 实现事件分类系统

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个事件系统工具包。

### 开发指南
1. 新的事件类型应在 `types.go` 中定义
2. 相关的载荷结构体也应在 `types.go` 中添加
3. 事件管理器的核心逻辑在 `events.go` 中实现
4. 遵循现有的命名约定和注释规范
