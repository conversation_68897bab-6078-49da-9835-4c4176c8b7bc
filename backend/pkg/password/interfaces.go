package password

import (
	"a8.tools/backend/pkg/password/types"
)

// 重新导出类型，保持向后兼容
type (
	PasswordRepository    = types.PasswordRepository
	EncryptionKeyProvider = types.EncryptionKeyProvider
	PasswordCache         = types.PasswordCache
	PasswordValidator     = types.PasswordValidator
	EventPublisher        = types.EventPublisher
	WrappedKeyData        = types.WrappedKeyData
	CacheStats            = types.CacheStats
	PasswordStatus        = types.PasswordStatus
	ProgressCallback      = types.ProgressCallback
)
