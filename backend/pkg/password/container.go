package password

import (
	"log/slog"
	"sync"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/domain"
	"a8.tools/backend/pkg/password/provider"
	"a8.tools/backend/pkg/password/repository"
)

// Container 依赖注入容器
type Container struct {
	config *config.Config
	logger *slog.Logger

	// 仓库层
	passwordRepo PasswordRepository

	// 提供者层
	encryptionProvider EncryptionKeyProvider
	passwordCache      PasswordCache
	eventPublisher     EventPublisher

	// 领域层
	passwordDomain   *domain.PasswordDomain
	encryptionDomain *domain.EncryptionDomain
	cacheDomain      *domain.CacheDomain
	validator        PasswordValidator

	// 应用层
	applicationService PasswordApplicationService

	// 单例保护
	once sync.Once
	mu   sync.RWMutex
}

// NewContainer 创建依赖注入容器
func NewContainer(config *config.Config, logger *slog.Logger) *Container {
	return &Container{
		config: config,
		logger: logger,
	}
}

// Initialize 初始化容器（延迟初始化）
func (c *Container) Initialize() error {
	var initErr error

	c.once.Do(func() {
		initErr = c.initializeComponents()
	})

	return initErr
}

// initializeComponents 初始化所有组件
func (c *Container) initializeComponents() error {
	// 1. 初始化仓库层
	c.passwordRepo = repository.NewKeyringRepository(
		c.config.Password.ServiceName,
		c.config.Password.KeyName,
		&c.config.Encryption,
	)

	// 2. 初始化提供者层
	c.passwordCache = provider.NewRetryableCache(
		provider.NewMemoryPasswordCache(&c.config.Cache),
		&c.config.Cache,
	)

	baseEncryptionProvider := provider.NewEncryptionProvider(
		c.passwordRepo,
		c.passwordCache,
		&c.config.Encryption,
	)
	c.encryptionProvider = provider.NewCachedEncryptionProvider(
		baseEncryptionProvider,
		&c.config.Encryption,
	)

	c.eventPublisher = provider.NewMockEventPublisher(c.logger) // 默认使用模拟发布器

	// 3. 初始化验证器
	c.validator = domain.NewAdvancedPasswordValidator(
		&c.config.Validation,
		c.encryptionProvider,
	)

	// 4. 初始化领域层
	c.passwordDomain = domain.NewPasswordDomain(
		c.passwordRepo,
		c.validator,
		&c.config.Password,
	)

	c.encryptionDomain = domain.NewEncryptionDomain(
		c.encryptionProvider,
		&c.config.Encryption,
	)

	c.cacheDomain = domain.NewCacheDomain(
		c.passwordCache,
		&c.config.Cache,
	)

	// 5. 初始化应用层
	c.applicationService = NewPasswordApplicationService(
		c.passwordDomain,
		c.encryptionDomain,
		c.cacheDomain,
		c.eventPublisher,
		c.config,
		c.logger,
	)

	return nil
}

// GetPasswordRepository 获取密码仓库
func (c *Container) GetPasswordRepository() PasswordRepository {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.passwordRepo
}

// GetEncryptionProvider 获取加密提供者
func (c *Container) GetEncryptionProvider() EncryptionKeyProvider {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.encryptionProvider
}

// GetPasswordCache 获取密码缓存
func (c *Container) GetPasswordCache() PasswordCache {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.passwordCache
}

// GetEventPublisher 获取事件发布器
func (c *Container) GetEventPublisher() EventPublisher {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.eventPublisher
}

// SetEventPublisher 设置事件发布器
func (c *Container) SetEventPublisher(publisher EventPublisher) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.eventPublisher = publisher

	// 如果应用服务已经初始化，需要更新其事件发布器
	if c.applicationService != nil {
		// 重新创建应用服务以使用新的事件发布器
		c.applicationService = nil
		c.GetApplicationService() // 这会重新创建应用服务
	}
}

// GetPasswordDomain 获取密码领域服务
func (c *Container) GetPasswordDomain() *domain.PasswordDomain {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.passwordDomain
}

// GetEncryptionDomain 获取加密领域服务
func (c *Container) GetEncryptionDomain() *domain.EncryptionDomain {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.encryptionDomain
}

// GetCacheDomain 获取缓存领域服务
func (c *Container) GetCacheDomain() *domain.CacheDomain {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.cacheDomain
}

// GetValidator 获取密码验证器
func (c *Container) GetValidator() PasswordValidator {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.validator
}

// GetApplicationService 获取应用服务
func (c *Container) GetApplicationService() PasswordApplicationService {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.applicationService
}

// GetConfig 获取配置
func (c *Container) GetConfig() *config.Config {
	return c.config
}

// GetLogger 获取日志器
func (c *Container) GetLogger() *slog.Logger {
	return c.logger
}

// Cleanup 清理资源
func (c *Container) Cleanup() {
	c.mu.Lock()
	defer c.mu.Unlock()

	// 清理缓存
	if c.passwordCache != nil {
		c.passwordCache.Clear()
	}

	// 清理加密器缓存
	if cachedProvider, ok := c.encryptionProvider.(interface{ ClearCache() }); ok {
		cachedProvider.ClearCache()
	}
}

// 全局容器实例
var (
	globalContainer *Container
	globalOnce      sync.Once
)

// GetGlobalContainer 获取全局容器实例
func GetGlobalContainer() *Container {
	globalOnce.Do(func() {
		// 使用默认配置创建全局容器
		defaultConfig := config.LoadDefaultConfig()
		defaultLogger := slog.Default()
		globalContainer = NewContainer(defaultConfig, defaultLogger)
	})
	return globalContainer
}

// SetGlobalContainer 设置全局容器实例
func SetGlobalContainer(container *Container) {
	globalContainer = container
}

// InitializeGlobalContainer 初始化全局容器
func InitializeGlobalContainer(config *config.Config, logger *slog.Logger) error {
	globalContainer = NewContainer(config, logger)
	return globalContainer.Initialize()
}

// PasswordApplicationService 应用服务接口
type PasswordApplicationService interface {
	// 基础密码操作
	SetPassword(password string) error
	VerifyPassword(password string) error
	HasPassword() bool

	// 应用生命周期管理
	UnlockApplication(password string) error
	LockApplication() error
	IsUnlocked() bool

	// 密码变更
	ChangePassword(oldPassword, newPassword string) error
	ChangePasswordWithProgress(oldPassword, newPassword string, progressCallback ProgressCallback) error

	// 状态查询
	GetStatus() *PasswordStatus
	GetCacheStats() *CacheStats

	// 缓存管理
	ClearCache() error
	RefreshCache() error

	// 密码强度评估
	GetPasswordStrength(password string) (*domain.PasswordStrengthResult, error)

	// 加密功能
	GetEncryptor() (interface{}, error) // 返回 cryptor.Encryptor
	ValidateEncryption() error
}
