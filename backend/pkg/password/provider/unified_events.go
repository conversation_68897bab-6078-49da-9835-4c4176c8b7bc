package provider

import (
    "log/slog"

    "a8.tools/backend/pkg/events"
    "a8.tools/backend/pkg/password/types"
)

// UnifiedEventPublisher 统一事件系统适配器
type UnifiedEventPublisher struct {
    manager *events.EventManager
    logger  *slog.Logger
}

// NewUnifiedEventPublisher 创建统一事件发布器
func NewUnifiedEventPublisher(logger *slog.Logger) *UnifiedEventPublisher {
    return &UnifiedEventPublisher{
        manager: events.GetManager(),
        logger:  logger,
    }
}

// PublishPasswordSet 发布密码设置事件
func (p *UnifiedEventPublisher) PublishPasswordSet() {
    p.manager.Emit("password.set", map[string]interface{}{
        "message": "应用密码已设置",
    })
    p.logger.Debug("发布密码设置事件")
}

// PublishPasswordChanged 发布密码变更事件
func (p *UnifiedEventPublisher) PublishPasswordChanged() {
    p.manager.Emit("password.changed", map[string]interface{}{
        "message": "应用密码已修改",
    })
    p.logger.Debug("发布密码变更事件")
}

// PublishApplicationUnlocked 发布应用解锁事件
func (p *UnifiedEventPublisher) PublishApplicationUnlocked() {
    p.manager.Emit("application.unlocked", map[string]interface{}{
        "message": "应用已解锁",
    })
    p.logger.Debug("发布应用解锁事件")
}

// PublishApplicationLocked 发布应用锁定事件
func (p *UnifiedEventPublisher) PublishApplicationLocked() {
    p.manager.Emit("application.locked", map[string]interface{}{
        "message": "应用已锁定",
    })
    p.logger.Debug("发布应用锁定事件")
}

// PublishPasswordCacheExpired 发布密码缓存过期事件
func (p *UnifiedEventPublisher) PublishPasswordCacheExpired() {
    p.manager.Emit("password.cache.expired", map[string]interface{}{
        "message": "密码缓存已过期",
    })
    p.logger.Debug("发布密码缓存过期事件")
}

// PublishError 发布错误事件
func (p *UnifiedEventPublisher) PublishError(err error) {
    var errorData map[string]interface{}

    if passwordErr, ok := err.(*types.PasswordError); ok {
        errorData = map[string]interface{}{
            "code":      passwordErr.Code,
            "message":   passwordErr.Message,
            "timestamp": passwordErr.Timestamp,
            "context":   passwordErr.Context,
        }
    } else {
        errorData = map[string]interface{}{
            "code":    "UNKNOWN_ERROR",
            "message": err.Error(),
        }
    }

    p.manager.Emit("password.error", errorData)
    p.logger.Error("发布密码错误事件", "error", err)
}

// PublishProgress 发布进度事件
func (p *UnifiedEventPublisher) PublishProgress(stage string, progress float64, message string) {
    p.manager.Emit("password.change.progress", map[string]interface{}{
        "stage":    stage,
        "progress": progress,
        "message":  message,
    })
    p.logger.Debug("发布密码变更进度事件", "stage", stage, "progress", progress)
}