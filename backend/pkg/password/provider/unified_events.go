package provider

import (
	"log/slog"

	"a8.tools/backend/pkg/events"
	"a8.tools/backend/pkg/password/types"
)

// UnifiedEventPublisher 统一事件系统适配器
type UnifiedEventPublisher struct {
	manager *events.EventManager
	logger  *slog.Logger
}

// NewUnifiedEventPublisher 创建统一事件发布器
func NewUnifiedEventPublisher(logger *slog.Logger) *UnifiedEventPublisher {
	return &UnifiedEventPublisher{
		manager: events.GetManager(),
		logger:  logger,
	}
}

// PublishPasswordSet 发布密码设置事件
func (p *UnifiedEventPublisher) PublishPasswordSet() {
	payload := events.PasswordOperationPayload{
		Operation: "set",
		Success:   true,
		Message:   "应用密码已设置",
	}

	if err := p.manager.Emit(events.EventPasswordSet, payload); err != nil {
		p.logger.Error("发布密码设置事件失败", "error", err)
		return
	}
	p.logger.Debug("发布密码设置事件")
}

// PublishPasswordChanged 发布密码变更事件
func (p *UnifiedEventPublisher) PublishPasswordChanged() {
	payload := events.PasswordOperationPayload{
		Operation: "change",
		Success:   true,
		Message:   "应用密码已修改",
	}

	if err := p.manager.Emit(events.EventPasswordUpdated, payload); err != nil {
		p.logger.Error("发布密码变更事件失败", "error", err)
		return
	}
	p.logger.Debug("发布密码变更事件")
}

// PublishApplicationUnlocked 发布应用解锁事件
func (p *UnifiedEventPublisher) PublishApplicationUnlocked() {
	payload := events.PasswordOperationPayload{
		Operation: "verify",
		Success:   true,
		Message:   "应用已解锁",
	}

	if err := p.manager.Emit(events.EventApplicationUnlocked, payload); err != nil {
		p.logger.Error("发布应用解锁事件失败", "error", err)
		return
	}
	p.logger.Debug("发布应用解锁事件")
}

// PublishApplicationLocked 发布应用锁定事件
func (p *UnifiedEventPublisher) PublishApplicationLocked() {
	payload := events.PasswordOperationPayload{
		Operation: "clear",
		Success:   true,
		Message:   "应用已锁定",
	}

	if err := p.manager.Emit(events.EventApplicationLocked, payload); err != nil {
		p.logger.Error("发布应用锁定事件失败", "error", err)
		return
	}
	p.logger.Debug("发布应用锁定事件")
}

// PublishPasswordCacheExpired 发布密码缓存过期事件
func (p *UnifiedEventPublisher) PublishPasswordCacheExpired() {
	payload := events.PasswordOperationPayload{
		Operation: "cache_expired",
		Success:   false,
		Message:   "密码缓存已过期",
	}

	if err := p.manager.Emit(events.EventPasswordCacheExpired, payload); err != nil {
		p.logger.Error("发布密码缓存过期事件失败", "error", err)
		return
	}
	p.logger.Debug("发布密码缓存过期事件")
}

// PublishError 发布错误事件
func (p *UnifiedEventPublisher) PublishError(err error) {
	var payload events.PasswordOperationPayload

	if passwordErr, ok := err.(*types.PasswordError); ok {
		payload = events.PasswordOperationPayload{
			Operation: "error",
			Success:   false,
			Message:   passwordErr.Message,
			Error:     passwordErr.Error(),
		}
	} else {
		payload = events.PasswordOperationPayload{
			Operation: "error",
			Success:   false,
			Message:   "密码操作失败",
			Error:     err.Error(),
		}
	}

	if emitErr := p.manager.Emit(events.EventPasswordError, payload); emitErr != nil {
		p.logger.Error("发布密码错误事件失败", "error", emitErr, "original_error", err)
		return
	}
	p.logger.Error("发布密码错误事件", "error", err)
}

// PublishProgress 发布进度事件
func (p *UnifiedEventPublisher) PublishProgress(stage string, progress float64, message string) {
	payload := events.PasswordChangePayload{
		Progress: progress,
		Stage:    stage,
		// 这里可以根据需要设置其他字段，如果有的话
	}

	if err := p.manager.Emit(events.EventPasswordChangeProgress, payload); err != nil {
		p.logger.Error("发布密码变更进度事件失败", "error", err)
		return
	}
	p.logger.Debug("发布密码变更进度事件", "stage", stage, "progress", progress)
}
