package provider

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/sha256"
	"encoding/base64"
	"fmt"

	"golang.org/x/crypto/pbkdf2"

	"a8.tools/backend/pkg/cryptor"
	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// EncryptionProvider 加密提供者实现
type EncryptionProvider struct {
	repo   types.PasswordRepository
	cache  types.PasswordCache
	config *config.EncryptionConfig
}

// NewEncryptionProvider 创建加密提供者
func NewEncryptionProvider(repo types.PasswordRepository, cache types.PasswordCache, config *config.EncryptionConfig) *EncryptionProvider {
	return &EncryptionProvider{
		repo:   repo,
		cache:  cache,
		config: config,
	}
}

// GetDataEncryptionKey 获取数据加密密钥
func (p *EncryptionProvider) GetDataEncryptionKey() ([]byte, error) {
	// 1. 从缓存获取密码
	password, err := p.cache.Get()
	if err != nil {
		return nil, types.WrapError(types.ErrCodeCacheExpired, "密码缓存已过期", err)
	}

	// 2. 使用密码获取DEK
	return p.DeriveKeyFromPassword(password)
}

// DeriveKeyFromPassword 从密码派生密钥
func (p *EncryptionProvider) DeriveKeyFromPassword(password string) ([]byte, error) {
	if password == "" {
		return nil, types.NewPasswordError(types.ErrCodeEmptyPassword, "密码不能为空", nil)
	}

	// 获取包装的密钥数据
	wrappedKey, err := p.repo.GetWrappedKey()
	if err != nil {
		return nil, types.WrapError(types.ErrCodeRepositoryFailed, "获取包装密钥失败", err)
	}

	// 使用PBKDF2从密码派生KEK
	kek := pbkdf2.Key([]byte(password), wrappedKey.Salt, p.config.KeyDerivationRounds, 32, sha256.New)

	// 解密DEK
	dek, err := p.decryptDEK(wrappedKey.EncryptedDEK, kek)
	if err != nil {
		return nil, types.WrapError(types.ErrCodeDecryptionFailed, "解密DEK失败", err)
	}

	return dek, nil
}

// CreateEncryptor 创建加密器实例
func (p *EncryptionProvider) CreateEncryptor() (cryptor.Encryptor, error) {
	dek, err := p.GetDataEncryptionKey()
	if err != nil {
		return nil, err
	}

	// 将DEK编码为base64字符串传递给加密器
	dekStr := base64.StdEncoding.EncodeToString(dek)
	return cryptor.NewEncryptor(dekStr), nil
}

// ValidateKey 验证密钥有效性
func (p *EncryptionProvider) ValidateKey(key []byte) error {
	if len(key) != 32 {
		return types.NewPasswordError(types.ErrCodeKeyInvalid, "密钥长度必须为32字节", nil)
	}

	// 检查密钥是否全为零
	allZero := true
	for _, b := range key {
		if b != 0 {
			allZero = false
			break
		}
	}

	if allZero {
		return types.NewPasswordError(types.ErrCodeKeyInvalid, "密钥不能全为零", nil)
	}

	return nil
}

// decryptDEK 解密数据加密密钥
func (p *EncryptionProvider) decryptDEK(encryptedDEK, kek []byte) ([]byte, error) {
	// 使用AES-GCM解密DEK（与 KeyringRepository 保持一致）
	block, err := aes.NewCipher(kek)
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedDEK) < nonceSize {
		return nil, fmt.Errorf("加密数据长度不足")
	}

	nonce, ciphertext := encryptedDEK[:nonceSize], encryptedDEK[nonceSize:]

	dek, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密失败: %w", err)
	}

	return dek, nil
}

// CachedEncryptionProvider 带缓存的加密提供者
type CachedEncryptionProvider struct {
	provider       *EncryptionProvider
	encryptorCache *EncryptorCache
	config         *config.EncryptionConfig
}

// NewCachedEncryptionProvider 创建带缓存的加密提供者
func NewCachedEncryptionProvider(provider *EncryptionProvider, config *config.EncryptionConfig) *CachedEncryptionProvider {
	return &CachedEncryptionProvider{
		provider:       provider,
		encryptorCache: NewEncryptorCache(&config.CacheConfig),
		config:         config,
	}
}

// GetDataEncryptionKey 获取数据加密密钥
func (p *CachedEncryptionProvider) GetDataEncryptionKey() ([]byte, error) {
	return p.provider.GetDataEncryptionKey()
}

// DeriveKeyFromPassword 从密码派生密钥
func (p *CachedEncryptionProvider) DeriveKeyFromPassword(password string) ([]byte, error) {
	return p.provider.DeriveKeyFromPassword(password)
}

// CreateEncryptor 创建加密器实例（带缓存）
func (p *CachedEncryptionProvider) CreateEncryptor() (cryptor.Encryptor, error) {
	// 1. 尝试从缓存获取
	if cached, ok := p.encryptorCache.Get(); ok {
		if encryptor, ok := cached.(cryptor.Encryptor); ok {
			return encryptor, nil
		}
	}

	// 2. 创建新的加密器
	encryptor, err := p.provider.CreateEncryptor()
	if err != nil {
		return nil, err
	}

	// 3. 缓存加密器
	p.encryptorCache.Set(encryptor, p.config.CacheConfig.TTL)

	return encryptor, nil
}

// ValidateKey 验证密钥有效性
func (p *CachedEncryptionProvider) ValidateKey(key []byte) error {
	return p.provider.ValidateKey(key)
}

// ClearCache 清除加密器缓存
func (p *CachedEncryptionProvider) ClearCache() {
	p.encryptorCache.Clear()
}

// GetCacheStats 获取缓存统计信息
func (p *CachedEncryptionProvider) GetCacheStats() types.CacheStats {
	return p.encryptorCache.GetStats()
}
