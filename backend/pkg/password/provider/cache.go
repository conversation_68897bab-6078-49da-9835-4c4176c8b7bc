package provider

import (
	"fmt"
	"sync"
	"time"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// MemoryPasswordCache 内存密码缓存实现
type MemoryPasswordCache struct {
	password    string
	isValid     bool
	lastUpdate  time.Time
	ttl         time.Duration
	accessCount int64
	hitCount    int64
	config      *config.CacheConfig
	mutex       sync.RWMutex
}

// NewMemoryPasswordCache 创建内存密码缓存
func NewMemoryPasswordCache(config *config.CacheConfig) *MemoryPasswordCache {
	return &MemoryPasswordCache{
		config: config,
		ttl:    config.TTL,
	}
}

// Set 设置密码缓存
func (c *MemoryPasswordCache) Set(password string, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if password == "" {
		return types.NewPasswordError(types.ErrCodeEmptyPassword, "缓存密码不能为空", nil)
	}

	c.password = password
	c.isValid = true
	c.lastUpdate = time.Now()
	c.ttl = ttl

	return nil
}

// Get 获取缓存的密码
func (c *MemoryPasswordCache) Get() (string, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.accessCount++

	// 检查缓存是否有效
	if !c.isValid || c.password == "" || time.Since(c.lastUpdate) > c.ttl {
		return "", types.NewPasswordError(types.ErrCodeCacheExpired, "密码缓存已过期或无效", nil)
	}

	c.hitCount++
	return c.password, nil
}

// Clear 清除缓存
func (c *MemoryPasswordCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.password = ""
	c.isValid = false
	c.lastUpdate = time.Time{}
}

// IsValid 检查缓存是否有效
func (c *MemoryPasswordCache) IsValid() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return c.isValid && c.password != "" && time.Since(c.lastUpdate) < c.ttl
}

// Refresh 刷新缓存过期时间
func (c *MemoryPasswordCache) Refresh(ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isValid || c.password == "" {
		return types.NewPasswordError(types.ErrCodeCacheInvalid, "无法刷新无效的缓存", nil)
	}

	c.lastUpdate = time.Now()
	c.ttl = ttl

	return nil
}

// GetStats 获取缓存统计信息
func (c *MemoryPasswordCache) GetStats() types.CacheStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return types.CacheStats{
		HasPassword: c.password != "",
		IsValid:     c.isValid && time.Since(c.lastUpdate) < c.ttl,
		LastUpdate:  c.lastUpdate,
		TTL:         c.ttl,
		AccessCount: c.accessCount,
		HitCount:    c.hitCount,
	}
}

// EncryptorCache 加密器缓存
type EncryptorCache struct {
	encryptor   interface{} // cryptor.Encryptor
	lastUpdate  time.Time
	ttl         time.Duration
	accessCount int64
	hitCount    int64
	mutex       sync.RWMutex
}

// NewEncryptorCache 创建加密器缓存
func NewEncryptorCache(config *config.CacheConfig) *EncryptorCache {
	return &EncryptorCache{
		ttl: config.TTL,
	}
}

// Get 获取缓存的加密器
func (c *EncryptorCache) Get() (interface{}, bool) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.accessCount++

	// 检查缓存是否有效
	if c.encryptor == nil || time.Since(c.lastUpdate) > c.ttl {
		return nil, false
	}

	c.hitCount++
	return c.encryptor, true
}

// Set 设置加密器缓存
func (c *EncryptorCache) Set(encryptor interface{}, ttl time.Duration) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.encryptor = encryptor
	c.lastUpdate = time.Now()
	c.ttl = ttl
}

// Clear 清除加密器缓存
func (c *EncryptorCache) Clear() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.encryptor = nil
	c.lastUpdate = time.Time{}
}

// GetStats 获取缓存统计信息
func (c *EncryptorCache) GetStats() types.CacheStats {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	return types.CacheStats{
		HasPassword: c.encryptor != nil,
		IsValid:     c.encryptor != nil && time.Since(c.lastUpdate) < c.ttl,
		LastUpdate:  c.lastUpdate,
		TTL:         c.ttl,
		AccessCount: c.accessCount,
		HitCount:    c.hitCount,
	}
}

// RetryableCache 可重试的缓存包装器
type RetryableCache struct {
	cache  types.PasswordCache
	config *config.CacheConfig
}

// NewRetryableCache 创建可重试的缓存包装器
func NewRetryableCache(cache types.PasswordCache, config *config.CacheConfig) *RetryableCache {
	return &RetryableCache{
		cache:  cache,
		config: config,
	}
}

// Set 设置密码缓存（带重试）
func (c *RetryableCache) Set(password string, ttl time.Duration) error {
	var lastErr error

	for i := 0; i < c.config.MaxRetries; i++ {
		if err := c.cache.Set(password, ttl); err == nil {
			return nil
		} else {
			lastErr = err
			if i < c.config.MaxRetries-1 {
				time.Sleep(c.config.RetryInterval)
			}
		}
	}

	return types.WrapError(types.ErrCodeCacheFailed,
		fmt.Sprintf("缓存设置失败，重试%d次后仍然失败", c.config.MaxRetries), lastErr)
}

// Get 获取缓存的密码（带重试）
func (c *RetryableCache) Get() (string, error) {
	var lastErr error

	for i := 0; i < c.config.MaxRetries; i++ {
		if result, err := c.cache.Get(); err == nil {
			return result, nil
		} else {
			lastErr = err
			// 如果是缓存过期错误，不需要重试
			if types.GetErrorCode(err) == types.ErrCodeCacheExpired {
				return "", err
			}
			if i < c.config.MaxRetries-1 {
				time.Sleep(c.config.RetryInterval)
			}
		}
	}

	return "", types.WrapError(types.ErrCodeCacheFailed,
		fmt.Sprintf("缓存获取失败，重试%d次后仍然失败", c.config.MaxRetries), lastErr)
}

// Clear 清除缓存
func (c *RetryableCache) Clear() {
	c.cache.Clear()
}

// IsValid 检查缓存是否有效
func (c *RetryableCache) IsValid() bool {
	return c.cache.IsValid()
}

// Refresh 刷新缓存过期时间
func (c *RetryableCache) Refresh(ttl time.Duration) error {
	return c.cache.Refresh(ttl)
}

// GetStats 获取缓存统计信息
func (c *RetryableCache) GetStats() types.CacheStats {
	return c.cache.GetStats()
}
