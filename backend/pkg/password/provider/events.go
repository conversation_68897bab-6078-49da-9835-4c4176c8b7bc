package provider

import (
	"fmt"
	"log/slog"
	"time"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// 注意：事件常量现在统一定义在 events 包中
// 这里保留的实现是为了向后兼容，建议使用 UnifiedEventPublisher

// WailsEventPublisher Wails 事件发布器实现
type WailsEventPublisher struct {
	app    WailsApp // Wails 应用接口
	config *config.EventsConfig
	logger *slog.Logger
}

// WailsApp Wails 应用接口（避免循环依赖）
type WailsApp interface {
	Events() WailsEvents
}

// WailsEvents Wails 事件接口
type WailsEvents interface {
	Emit(eventName string, data interface{})
}

// NewWailsEventPublisher 创建 Wails 事件发布器
func NewWailsEventPublisher(app WailsApp, config *config.EventsConfig, logger *slog.Logger) *WailsEventPublisher {
	return &WailsEventPublisher{
		app:    app,
		config: config,
		logger: logger,
	}
}

// PublishPasswordSet 发布密码设置事件
func (p *WailsEventPublisher) PublishPasswordSet() {
	event := PasswordEvent{
		Type:      "password.set",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"message": "应用密码已设置",
		},
	}

	p.publishEvent("password.set", event)
}

// PublishPasswordChanged 发布密码变更事件
func (p *WailsEventPublisher) PublishPasswordChanged() {
	event := PasswordEvent{
		Type:      "password.changed",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"message": "应用密码已修改",
		},
	}

	p.publishEvent("password.changed", event)
}

// PublishApplicationUnlocked 发布应用解锁事件
func (p *WailsEventPublisher) PublishApplicationUnlocked() {
	event := PasswordEvent{
		Type:      "application.unlocked",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"message": "应用已解锁",
		},
	}

	p.publishEvent("application.unlocked", event)
}

// PublishApplicationLocked 发布应用锁定事件
func (p *WailsEventPublisher) PublishApplicationLocked() {
	event := PasswordEvent{
		Type:      "application.locked",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"message": "应用已锁定",
		},
	}

	p.publishEvent("application.locked", event)
}

// PublishPasswordCacheExpired 发布密码缓存过期事件
func (p *WailsEventPublisher) PublishPasswordCacheExpired() {
	event := PasswordEvent{
		Type:      "password.cache.expired",
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"message": "密码缓存已过期",
		},
	}

	p.publishEvent("password.cache.expired", event)
}

// PublishError 发布错误事件
func (p *WailsEventPublisher) PublishError(err error) {
	var errorData map[string]interface{}

	if passwordErr, ok := err.(*types.PasswordError); ok {
		errorData = map[string]interface{}{
			"code":      passwordErr.Code,
			"message":   passwordErr.Message,
			"timestamp": passwordErr.Timestamp,
			"context":   passwordErr.Context,
		}
	} else {
		errorData = map[string]interface{}{
			"code":    "UNKNOWN_ERROR",
			"message": err.Error(),
		}
	}

	event := PasswordEvent{
		Type:      "password.error",
		Timestamp: time.Now(),
		Data:      errorData,
	}

	p.publishEvent("password.error", event)
}

// PublishProgress 发布进度事件
func (p *WailsEventPublisher) PublishProgress(stage string, progress float64, message string) {
	event := ProgressEvent{
		Stage:     stage,
		Progress:  progress,
		Message:   message,
		Timestamp: time.Now(),
	}

	p.publishEvent("password.change.progress", event)
}

// 私有方法
func (p *WailsEventPublisher) publishEvent(eventName string, data interface{}) {
	if !p.config.Enabled {
		return
	}

	// 记录事件日志
	p.logger.Debug("发布密码事件",
		"event", eventName,
		"data", data,
	)

	// 发布到 Wails 事件系统
	if p.app != nil && p.app.Events() != nil {
		p.app.Events().Emit(eventName, data)
	}
}

// PasswordEvent 密码事件数据结构
type PasswordEvent struct {
	Type      string                 `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data,omitempty"`
}

// ProgressEvent 进度事件数据结构
type ProgressEvent struct {
	Stage     string    `json:"stage"`
	Progress  float64   `json:"progress"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// MockEventPublisher 模拟事件发布器（用于测试）
type MockEventPublisher struct {
	events []EventRecord
	logger *slog.Logger
}

// EventRecord 事件记录
type EventRecord struct {
	EventName string
	Data      interface{}
	Timestamp time.Time
}

// NewMockEventPublisher 创建模拟事件发布器
func NewMockEventPublisher(logger *slog.Logger) *MockEventPublisher {
	return &MockEventPublisher{
		events: make([]EventRecord, 0),
		logger: logger,
	}
}

// PublishPasswordSet 发布密码设置事件
func (p *MockEventPublisher) PublishPasswordSet() {
	p.recordEvent("password.set", "密码已设置")
}

// PublishPasswordChanged 发布密码变更事件
func (p *MockEventPublisher) PublishPasswordChanged() {
	p.recordEvent("password.changed", "密码已修改")
}

// PublishApplicationUnlocked 发布应用解锁事件
func (p *MockEventPublisher) PublishApplicationUnlocked() {
	p.recordEvent("application.unlocked", "应用已解锁")
}

// PublishApplicationLocked 发布应用锁定事件
func (p *MockEventPublisher) PublishApplicationLocked() {
	p.recordEvent("application.locked", "应用已锁定")
}

// PublishPasswordCacheExpired 发布密码缓存过期事件
func (p *MockEventPublisher) PublishPasswordCacheExpired() {
	p.recordEvent("password.cache.expired", "密码缓存已过期")
}

// PublishError 发布错误事件
func (p *MockEventPublisher) PublishError(err error) {
	p.recordEvent("password.error", err.Error())
}

// PublishProgress 发布进度事件
func (p *MockEventPublisher) PublishProgress(stage string, progress float64, message string) {
	progressData := fmt.Sprintf("阶段: %s, 进度: %.1f%%, 消息: %s", stage, progress, message)
	p.recordEvent("password.change.progress", progressData)
}

// GetEvents 获取所有事件记录
func (p *MockEventPublisher) GetEvents() []EventRecord {
	return p.events
}

// ClearEvents 清除所有事件记录
func (p *MockEventPublisher) ClearEvents() {
	p.events = make([]EventRecord, 0)
}

// GetEventsByType 根据类型获取事件
func (p *MockEventPublisher) GetEventsByType(eventType string) []EventRecord {
	var filtered []EventRecord
	for _, event := range p.events {
		if event.EventName == eventType {
			filtered = append(filtered, event)
		}
	}
	return filtered
}

// 私有方法
func (p *MockEventPublisher) recordEvent(eventName string, data interface{}) {
	record := EventRecord{
		EventName: eventName,
		Data:      data,
		Timestamp: time.Now(),
	}

	p.events = append(p.events, record)

	if p.logger != nil {
		p.logger.Debug("记录模拟事件",
			"event", eventName,
			"data", data,
		)
	}
}

// LoggingEventPublisher 带日志的事件发布器包装器
type LoggingEventPublisher struct {
	publisher types.EventPublisher
	logger    *slog.Logger
}

// NewLoggingEventPublisher 创建带日志的事件发布器
func NewLoggingEventPublisher(publisher types.EventPublisher, logger *slog.Logger) *LoggingEventPublisher {
	return &LoggingEventPublisher{
		publisher: publisher,
		logger:    logger,
	}
}

// PublishPasswordSet 发布密码设置事件
func (p *LoggingEventPublisher) PublishPasswordSet() {
	p.logger.Info("发布事件: 密码已设置")
	p.publisher.PublishPasswordSet()
}

// PublishPasswordChanged 发布密码变更事件
func (p *LoggingEventPublisher) PublishPasswordChanged() {
	p.logger.Info("发布事件: 密码已修改")
	p.publisher.PublishPasswordChanged()
}

// PublishApplicationUnlocked 发布应用解锁事件
func (p *LoggingEventPublisher) PublishApplicationUnlocked() {
	p.logger.Info("发布事件: 应用已解锁")
	p.publisher.PublishApplicationUnlocked()
}

// PublishApplicationLocked 发布应用锁定事件
func (p *LoggingEventPublisher) PublishApplicationLocked() {
	p.logger.Info("发布事件: 应用已锁定")
	p.publisher.PublishApplicationLocked()
}

// PublishPasswordCacheExpired 发布密码缓存过期事件
func (p *LoggingEventPublisher) PublishPasswordCacheExpired() {
	p.logger.Warn("发布事件: 密码缓存已过期")
	p.publisher.PublishPasswordCacheExpired()
}

// PublishError 发布错误事件
func (p *LoggingEventPublisher) PublishError(err error) {
	p.logger.Error("发布事件: 密码错误", "error", err)
	p.publisher.PublishError(err)
}

// PublishProgress 发布进度事件
func (p *LoggingEventPublisher) PublishProgress(stage string, progress float64, message string) {
	p.logger.Debug("发布事件: 进度更新",
		"stage", stage,
		"progress", progress,
		"message", message,
	)
	p.publisher.PublishProgress(stage, progress, message)
}
