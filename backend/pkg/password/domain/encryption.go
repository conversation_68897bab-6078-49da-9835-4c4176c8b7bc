package domain

import (
	"encoding/base64"
	"fmt"

	"a8.tools/backend/pkg/cryptor"
	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// EncryptionDomain 加密领域服务
type EncryptionDomain struct {
	keyProvider types.EncryptionKeyProvider
	config      *config.EncryptionConfig
}

// NewEncryptionDomain 创建加密领域服务
func NewEncryptionDomain(keyProvider types.EncryptionKeyProvider, config *config.EncryptionConfig) *EncryptionDomain {
	return &EncryptionDomain{
		keyProvider: keyProvider,
		config:      config,
	}
}

// GetEncryptor 获取加密器
func (d *EncryptionDomain) GetEncryptor() (cryptor.Encryptor, error) {
	encryptor, err := d.keyProvider.CreateEncryptor()
	if err != nil {
		return nil, types.WrapError(types.ErrCodeEncryptionFailed, "创建加密器失败", err)
	}

	return encryptor, nil
}

// ValidateEncryption 验证加密功能
func (d *EncryptionDomain) ValidateEncryption() error {
	// 1. 获取加密器
	encryptor, err := d.GetEncryptor()
	if err != nil {
		return err
	}

	// 2. 测试加密解密
	testData := "test_encryption_validation"
	encrypted, err := encryptor.Encrypt(testData)
	if err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "加密测试失败", err)
	}

	decrypted, err := encryptor.Decrypt(encrypted)
	if err != nil {
		return types.WrapError(types.ErrCodeDecryptionFailed, "解密测试失败", err)
	}

	if decrypted != testData {
		return types.NewPasswordError(types.ErrCodeDataIntegrity, "加密解密数据不匹配", nil)
	}

	return nil
}

// GetDataEncryptionKey 获取数据加密密钥
func (d *EncryptionDomain) GetDataEncryptionKey() ([]byte, error) {
	key, err := d.keyProvider.GetDataEncryptionKey()
	if err != nil {
		return nil, types.WrapError(types.ErrCodeKeyDerivationFailed, "获取数据加密密钥失败", err)
	}

	// 验证密钥
	if err := d.keyProvider.ValidateKey(key); err != nil {
		return nil, types.WrapError(types.ErrCodeKeyInvalid, "数据加密密钥无效", err)
	}

	return key, nil
}

// DeriveKeyFromPassword 从密码派生密钥
func (d *EncryptionDomain) DeriveKeyFromPassword(password string) ([]byte, error) {
	if password == "" {
		return nil, types.ErrEmptyPassword
	}

	key, err := d.keyProvider.DeriveKeyFromPassword(password)
	if err != nil {
		return nil, types.WrapError(types.ErrCodeKeyDerivationFailed, "从密码派生密钥失败", err)
	}

	// 验证派生的密钥
	if err := d.keyProvider.ValidateKey(key); err != nil {
		return nil, types.WrapError(types.ErrCodeKeyInvalid, "派生的密钥无效", err)
	}

	return key, nil
}

// ReencryptData 重新加密数据
func (d *EncryptionDomain) ReencryptData(oldPassword, newPassword string, progressCallback types.ProgressCallback) error {
	if progressCallback == nil {
		progressCallback = func(stage string, progress float64, message string) {
			// 默认空实现
		}
	}

	progressCallback("validation", 0.0, "验证密码")

	// 1. 验证旧密码
	oldKey, err := d.DeriveKeyFromPassword(oldPassword)
	if err != nil {
		return types.WrapError(types.ErrCodePasswordIncorrect, "旧密码验证失败", err)
	}

	// 2. 验证新密码
	newKey, err := d.DeriveKeyFromPassword(newPassword)
	if err != nil {
		return types.WrapError(types.ErrCodeValidationFailed, "新密码验证失败", err)
	}

	progressCallback("preparation", 10.0, "准备重加密")

	// 3. 创建加密器
	oldEncryptor, err := d.createEncryptorWithKey(oldKey)
	if err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "创建旧加密器失败", err)
	}

	newEncryptor, err := d.createEncryptorWithKey(newKey)
	if err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "创建新加密器失败", err)
	}

	progressCallback("reencryption", 20.0, "开始重新加密数据")

	// 4. 执行重新加密
	reencryptionService := NewReencryptionService(oldEncryptor, newEncryptor, progressCallback)
	if err := reencryptionService.ReencryptAllData(); err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "数据重新加密失败", err)
	}

	progressCallback("completion", 100.0, "重新加密完成")

	return nil
}

// createEncryptorWithKey 使用指定密钥创建加密器
func (d *EncryptionDomain) createEncryptorWithKey(key []byte) (cryptor.Encryptor, error) {
	// 这里需要根据实际的 cryptor 包实现来调整
	// 假设 cryptor.NewEncryptor 接受 base64 编码的密钥
	keyStr := base64.StdEncoding.EncodeToString(key)
	return cryptor.NewEncryptor(keyStr), nil
}

// ReencryptionService 重新加密服务
type ReencryptionService struct {
	oldEncryptor     cryptor.Encryptor
	newEncryptor     cryptor.Encryptor
	progressCallback types.ProgressCallback
}

// NewReencryptionService 创建重新加密服务
func NewReencryptionService(oldEncryptor, newEncryptor cryptor.Encryptor, progressCallback types.ProgressCallback) *ReencryptionService {
	return &ReencryptionService{
		oldEncryptor:     oldEncryptor,
		newEncryptor:     newEncryptor,
		progressCallback: progressCallback,
	}
}

// ReencryptAllData 重新加密所有数据
func (s *ReencryptionService) ReencryptAllData() error {
	// 这里需要与数据库加密模块集成
	// 获取所有需要重新加密的数据模型

	s.progressCallback("discovery", 25.0, "发现需要重新加密的数据")

	// TODO: 实现具体的重新加密逻辑
	// 1. 获取所有注册的可加密模型
	// 2. 遍历每个模型的所有记录
	// 3. 解密旧数据，用新密钥重新加密
	// 4. 更新数据库记录

	s.progressCallback("processing", 50.0, "处理数据重新加密")

	// 模拟重新加密过程
	stages := []string{"用户数据", "钱包数据", "账户数据", "设置数据"}
	for i, stage := range stages {
		progress := 50.0 + float64(i+1)*10.0
		s.progressCallback("processing", progress, fmt.Sprintf("重新加密%s", stage))

		// 这里应该调用具体的重新加密逻辑
		if err := s.reencryptModelData(stage); err != nil {
			return err
		}
	}

	s.progressCallback("finalization", 90.0, "完成数据重新加密")

	return nil
}

// reencryptModelData 重新加密特定模型的数据
func (s *ReencryptionService) reencryptModelData(modelName string) error {
	// TODO: 实现具体的模型数据重新加密逻辑
	// 这需要与数据库加密模块协调

	// 示例逻辑：
	// 1. 查询该模型的所有加密字段
	// 2. 遍历所有记录
	// 3. 解密每个加密字段
	// 4. 用新加密器重新加密
	// 5. 更新记录

	return nil
}

// EncryptionStatus 加密状态信息
type EncryptionStatus struct {
	IsAvailable    bool   `json:"is_available"`
	Algorithm      string `json:"algorithm"`
	KeyDerivation  string `json:"key_derivation"`
	LastValidation string `json:"last_validation"`
	ErrorMessage   string `json:"error_message,omitempty"`
}

// GetEncryptionStatus 获取加密状态
func (d *EncryptionDomain) GetEncryptionStatus() *EncryptionStatus {
	status := &EncryptionStatus{
		Algorithm:     d.config.Algorithm,
		KeyDerivation: fmt.Sprintf("PBKDF2-%d", d.config.KeyDerivationRounds),
	}

	// 验证加密功能
	if err := d.ValidateEncryption(); err != nil {
		status.IsAvailable = false
		status.ErrorMessage = err.Error()
	} else {
		status.IsAvailable = true
		status.LastValidation = "刚刚"
	}

	return status
}

// ClearEncryptionCache 清除加密相关缓存
func (d *EncryptionDomain) ClearEncryptionCache() {
	// 如果密钥提供者支持缓存清除，则调用
	if cachedProvider, ok := d.keyProvider.(interface{ ClearCache() }); ok {
		cachedProvider.ClearCache()
	}
}
