package domain

import (
	"fmt"
	"regexp"
	"strings"
	"unicode"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// PasswordValidator 密码验证器实现
type PasswordValidator struct {
	config             *config.ValidationConfig
	encryptionProvider types.EncryptionKeyProvider
}

// NewPasswordValidator 创建密码验证器
func NewPasswordValidator(config *config.ValidationConfig, encryptionProvider types.EncryptionKeyProvider) *PasswordValidator {
	return &PasswordValidator{
		config:             config,
		encryptionProvider: encryptionProvider,
	}
}

// ValidateStrength 验证密码强度
func (v *PasswordValidator) ValidateStrength(password string) error {
	req := v.config.StrengthRequirement

	// 检查最小长度
	if len(password) < req.MinLength {
		return types.NewPasswordError(
			types.ErrCodePasswordTooShort,
			fmt.Sprintf("密码长度不能少于%d位", req.MinLength),
			nil,
		)
	}

	// 检查是否包含大写字母
	if req.RequireUppercase && !v.containsUppercase(password) {
		return types.NewPasswordError(
			types.ErrCodePasswordWeak,
			"密码必须包含至少一个大写字母",
			nil,
		)
	}

	// 检查是否包含小写字母
	if req.RequireLowercase && !v.containsLowercase(password) {
		return types.NewPasswordError(
			types.ErrCodePasswordWeak,
			"密码必须包含至少一个小写字母",
			nil,
		)
	}

	// 检查是否包含数字
	if req.RequireNumbers && !v.containsNumber(password) {
		return types.NewPasswordError(
			types.ErrCodePasswordWeak,
			"密码必须包含至少一个数字",
			nil,
		)
	}

	// 检查是否包含特殊字符
	if req.RequireSymbols && !v.containsSymbol(password) {
		return types.NewPasswordError(
			types.ErrCodePasswordWeak,
			"密码必须包含至少一个特殊字符",
			nil,
		)
	}

	return nil
}

// ValidateDataDecryptability 验证密码能否解密现有数据
func (v *PasswordValidator) ValidateDataDecryptability(password string) error {
	if !v.config.EnableDataValidation {
		return nil
	}

	// 尝试使用密码派生密钥
	_, err := v.encryptionProvider.DeriveKeyFromPassword(password)
	if err != nil {
		return types.WrapError(
			types.ErrCodeDataIntegrity,
			"密码无法解密现有数据",
			err,
		)
	}

	// TODO: 这里可以添加更多的数据完整性检查
	// 例如：尝试解密一些样本数据来验证密码的正确性

	return nil
}

// ValidateFormat 验证密码格式
func (v *PasswordValidator) ValidateFormat(password string) error {
	// 检查密码是否为空
	if password == "" {
		return types.NewPasswordError(
			types.ErrCodeEmptyPassword,
			"密码不能为空",
			nil,
		)
	}

	// 检查密码是否只包含空白字符
	if strings.TrimSpace(password) == "" {
		return types.NewPasswordError(
			types.ErrCodeInvalidFormat,
			"密码不能只包含空白字符",
			nil,
		)
	}

	// 检查密码是否包含不可打印字符
	for _, r := range password {
		if !unicode.IsPrint(r) && !unicode.IsSpace(r) {
			return types.NewPasswordError(
				types.ErrCodeInvalidFormat,
				"密码包含不可打印字符",
				nil,
			)
		}
	}

	return nil
}

// 私有辅助方法
func (v *PasswordValidator) containsUppercase(password string) bool {
	for _, r := range password {
		if unicode.IsUpper(r) {
			return true
		}
	}
	return false
}

func (v *PasswordValidator) containsLowercase(password string) bool {
	for _, r := range password {
		if unicode.IsLower(r) {
			return true
		}
	}
	return false
}

func (v *PasswordValidator) containsNumber(password string) bool {
	for _, r := range password {
		if unicode.IsDigit(r) {
			return true
		}
	}
	return false
}

func (v *PasswordValidator) containsSymbol(password string) bool {
	// 定义常见的特殊字符
	symbols := `!@#$%^&*()_+-=[]{}|;:,.<>?`
	for _, r := range password {
		if strings.ContainsRune(symbols, r) {
			return true
		}
	}
	return false
}

// AdvancedPasswordValidator 高级密码验证器
type AdvancedPasswordValidator struct {
	*PasswordValidator
	commonPasswords []string
	bannedPatterns  []*regexp.Regexp
}

// NewAdvancedPasswordValidator 创建高级密码验证器
func NewAdvancedPasswordValidator(config *config.ValidationConfig, encryptionProvider types.EncryptionKeyProvider) *AdvancedPasswordValidator {
	base := NewPasswordValidator(config, encryptionProvider)

	// 常见弱密码列表
	commonPasswords := []string{
		"password", "123456", "123456789", "qwerty", "abc123",
		"password123", "admin", "root", "user", "guest",
		"12345678", "**********", "qwertyuiop", "asdfghjkl",
	}

	// 危险模式（如连续字符、重复字符等）
	bannedPatterns := []*regexp.Regexp{
		regexp.MustCompile(`(0123|1234|2345|3456|4567|5678|6789|7890)`),                                                                                           // 连续数字
		regexp.MustCompile(`(abcd|bcde|cdef|defg|efgh|fghi|ghij|hijk|ijkl|jklm|klmn|lmno|mnop|nopq|opqr|pqrs|qrst|rstu|stuv|tuvw|uvwx|vwxy|wxyz)`),                // 连续字母
		regexp.MustCompile(`(aaaa|bbbb|cccc|dddd|eeee|ffff|gggg|hhhh|iiii|jjjj|kkkk|llll|mmmm|nnnn|oooo|pppp|qqqq|rrrr|ssss|tttt|uuuu|vvvv|wwww|xxxx|yyyy|zzzz)`), // 重复字符
		regexp.MustCompile(`(0000|1111|2222|3333|4444|5555|6666|7777|8888|9999)`),                                                                                 // 重复数字
	}

	return &AdvancedPasswordValidator{
		PasswordValidator: base,
		commonPasswords:   commonPasswords,
		bannedPatterns:    bannedPatterns,
	}
}

// ValidateStrength 验证密码强度（增强版）
func (v *AdvancedPasswordValidator) ValidateStrength(password string) error {
	// 首先执行基础验证
	if err := v.PasswordValidator.ValidateStrength(password); err != nil {
		return err
	}

	// 检查是否为常见弱密码
	lowerPassword := strings.ToLower(password)
	for _, common := range v.commonPasswords {
		if lowerPassword == common || strings.Contains(lowerPassword, common) {
			return types.NewPasswordError(
				types.ErrCodePasswordWeak,
				"密码过于常见，请使用更复杂的密码",
				nil,
			)
		}
	}

	// 检查危险模式
	for _, pattern := range v.bannedPatterns {
		if pattern.MatchString(strings.ToLower(password)) {
			return types.NewPasswordError(
				types.ErrCodePasswordWeak,
				"密码包含不安全的模式（如重复字符或连续字符）",
				nil,
			)
		}
	}

	// 计算密码复杂度分数
	score := v.calculateComplexityScore(password)
	if score < 60 { // 最低复杂度要求
		return types.NewPasswordError(
			types.ErrCodePasswordWeak,
			fmt.Sprintf("密码复杂度不足（当前分数：%d，最低要求：60）", score),
			nil,
		)
	}

	return nil
}

// calculateComplexityScore 计算密码复杂度分数
func (v *AdvancedPasswordValidator) calculateComplexityScore(password string) int {
	score := 0

	// 长度分数（最多30分）
	length := len(password)
	if length >= 8 {
		score += 10
	}
	if length >= 12 {
		score += 10
	}
	if length >= 16 {
		score += 10
	}

	// 字符类型分数（每种类型10分，最多40分）
	if v.containsLowercase(password) {
		score += 10
	}
	if v.containsUppercase(password) {
		score += 10
	}
	if v.containsNumber(password) {
		score += 10
	}
	if v.containsSymbol(password) {
		score += 10
	}

	// 字符多样性分数（最多20分）
	uniqueChars := make(map[rune]bool)
	for _, r := range password {
		uniqueChars[r] = true
	}
	diversity := len(uniqueChars)
	if diversity >= 8 {
		score += 10
	}
	if diversity >= 12 {
		score += 10
	}

	// 模式惩罚
	for _, pattern := range v.bannedPatterns {
		if pattern.MatchString(strings.ToLower(password)) {
			score -= 20
		}
	}

	// 确保分数在0-100范围内
	if score < 0 {
		score = 0
	}
	if score > 100 {
		score = 100
	}

	return score
}
