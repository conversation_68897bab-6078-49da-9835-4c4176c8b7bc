package domain

import (
	"fmt"
	"time"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// CacheDomain 缓存领域服务
type CacheDomain struct {
	cache  types.PasswordCache
	config *config.CacheConfig
}

// NewCacheDomain 创建缓存领域服务
func NewCacheDomain(cache types.PasswordCache, config *config.CacheConfig) *CacheDomain {
	return &CacheDomain{
		cache:  cache,
		config: config,
	}
}

// CachePassword 缓存密码
func (d *CacheDomain) CachePassword(password string) error {
	if password == "" {
		return types.ErrEmptyPassword
	}

	if err := d.cache.Set(password, d.config.TTL); err != nil {
		return types.WrapError(types.ErrCodeCacheFailed, "密码缓存失败", err)
	}

	return nil
}

// GetCachedPassword 获取缓存的密码
func (d *CacheDomain) GetCachedPassword() (string, error) {
	cachedPassword, err := d.cache.Get()
	if err != nil {
		if types.GetErrorCode(err) == types.ErrCodeCacheExpired {
			return "", types.ErrCacheExpired
		}
		return "", types.WrapError(types.ErrCodeCacheFailed, "获取缓存密码失败", err)
	}

	return cachedPassword, nil
}

// ClearCache 清除缓存
func (d *CacheDomain) ClearCache() {
	d.cache.Clear()
}

// RefreshCache 刷新缓存
func (d *CacheDomain) RefreshCache() error {
	if err := d.cache.Refresh(d.config.TTL); err != nil {
		return types.WrapError(types.ErrCodeCacheFailed, "刷新缓存失败", err)
	}

	return nil
}

// IsPasswordCached 检查密码是否已缓存
func (d *CacheDomain) IsPasswordCached() bool {
	return d.cache.IsValid()
}

// GetCacheStatus 获取缓存状态
func (d *CacheDomain) GetCacheStatus() *CacheStatus {
	stats := d.cache.GetStats()

	status := &CacheStatus{
		IsValid:     stats.IsValid,
		HasPassword: stats.HasPassword,
		LastUpdate:  stats.LastUpdate,
		TTL:         stats.TTL,
		ExpiresAt:   stats.LastUpdate.Add(stats.TTL),
		HitRate:     d.calculateHitRate(stats),
		AccessCount: stats.AccessCount,
		HitCount:    stats.HitCount,
	}

	// 计算剩余时间
	if stats.IsValid {
		remaining := time.Until(status.ExpiresAt)
		if remaining > 0 {
			status.RemainingTime = remaining
		}
	}

	return status
}

// CacheStatus 缓存状态信息
type CacheStatus struct {
	IsValid       bool          `json:"is_valid"`
	HasPassword   bool          `json:"has_password"`
	LastUpdate    time.Time     `json:"last_update"`
	TTL           time.Duration `json:"ttl"`
	ExpiresAt     time.Time     `json:"expires_at"`
	RemainingTime time.Duration `json:"remaining_time"`
	HitRate       float64       `json:"hit_rate"`
	AccessCount   int64         `json:"access_count"`
	HitCount      int64         `json:"hit_count"`
}

// ExtendCacheTime 延长缓存时间
func (d *CacheDomain) ExtendCacheTime(additionalTime time.Duration) error {
	newTTL := d.config.TTL + additionalTime
	if err := d.cache.Refresh(newTTL); err != nil {
		return types.WrapError(types.ErrCodeCacheFailed, "延长缓存时间失败", err)
	}

	return nil
}

// SetCustomTTL 设置自定义TTL
func (d *CacheDomain) SetCustomTTL(ttl time.Duration) error {
	if ttl <= 0 {
		return types.NewPasswordError(types.ErrCodeConfigInvalid, "TTL必须大于0", nil)
	}

	if err := d.cache.Refresh(ttl); err != nil {
		return types.WrapError(types.ErrCodeCacheFailed, "设置自定义TTL失败", err)
	}

	return nil
}

// ValidateCache 验证缓存状态
func (d *CacheDomain) ValidateCache() error {
	if !d.cache.IsValid() {
		return types.ErrCacheExpired
	}

	// 尝试获取密码以验证缓存内容
	_, err := d.cache.Get()
	if err != nil {
		return types.WrapError(types.ErrCodeCacheInvalid, "缓存验证失败", err)
	}

	return nil
}

// GetCacheMetrics 获取缓存指标
func (d *CacheDomain) GetCacheMetrics() *CacheMetrics {
	stats := d.cache.GetStats()
	status := d.GetCacheStatus()

	metrics := &CacheMetrics{
		TotalAccess: stats.AccessCount,
		CacheHits:   stats.HitCount,
		CacheMisses: stats.AccessCount - stats.HitCount,
		HitRate:     d.calculateHitRate(stats),
		AverageAge:  time.Since(stats.LastUpdate),
		IsHealthy:   d.isCacheHealthy(stats),
		Efficiency:  d.calculateEfficiency(stats),
		Status:      status,
	}

	return metrics
}

// CacheMetrics 缓存指标
type CacheMetrics struct {
	TotalAccess int64         `json:"total_access"`
	CacheHits   int64         `json:"cache_hits"`
	CacheMisses int64         `json:"cache_misses"`
	HitRate     float64       `json:"hit_rate"`
	AverageAge  time.Duration `json:"average_age"`
	IsHealthy   bool          `json:"is_healthy"`
	Efficiency  float64       `json:"efficiency"`
	Status      *CacheStatus  `json:"status"`
}

// 私有辅助方法
func (d *CacheDomain) calculateHitRate(stats types.CacheStats) float64 {
	if stats.AccessCount == 0 {
		return 0.0
	}
	return float64(stats.HitCount) / float64(stats.AccessCount)
}

func (d *CacheDomain) isCacheHealthy(stats types.CacheStats) bool {
	// 缓存健康的标准：
	// 1. 缓存有效
	// 2. 命中率 > 50%
	// 3. 最近有访问记录
	hitRate := d.calculateHitRate(stats)
	recentAccess := time.Since(stats.LastUpdate) < d.config.TTL*2

	return stats.IsValid && hitRate > 0.5 && recentAccess
}

func (d *CacheDomain) calculateEfficiency(stats types.CacheStats) float64 {
	// 效率计算：考虑命中率和缓存年龄
	hitRate := d.calculateHitRate(stats)
	ageRatio := float64(time.Since(stats.LastUpdate)) / float64(d.config.TTL)

	// 年龄比例越小，效率越高
	ageFactor := 1.0 - ageRatio
	if ageFactor < 0 {
		ageFactor = 0
	}

	return hitRate * ageFactor
}

// CacheManager 缓存管理器
type CacheManager struct {
	domains map[string]*CacheDomain
	config  *config.CacheConfig
}

// NewCacheManager 创建缓存管理器
func NewCacheManager(config *config.CacheConfig) *CacheManager {
	return &CacheManager{
		domains: make(map[string]*CacheDomain),
		config:  config,
	}
}

// RegisterDomain 注册缓存域
func (m *CacheManager) RegisterDomain(name string, domain *CacheDomain) {
	m.domains[name] = domain
}

// GetDomain 获取缓存域
func (m *CacheManager) GetDomain(name string) (*CacheDomain, error) {
	domain, exists := m.domains[name]
	if !exists {
		return nil, types.NewPasswordError(
			types.ErrCodeConfigInvalid,
			fmt.Sprintf("缓存域 '%s' 不存在", name),
			nil,
		)
	}
	return domain, nil
}

// ClearAllCaches 清除所有缓存
func (m *CacheManager) ClearAllCaches() {
	for _, domain := range m.domains {
		domain.ClearCache()
	}
}

// GetAllCacheStatus 获取所有缓存状态
func (m *CacheManager) GetAllCacheStatus() map[string]*CacheStatus {
	status := make(map[string]*CacheStatus)
	for name, domain := range m.domains {
		status[name] = domain.GetCacheStatus()
	}
	return status
}

// ValidateAllCaches 验证所有缓存
func (m *CacheManager) ValidateAllCaches() map[string]error {
	results := make(map[string]error)
	for name, domain := range m.domains {
		results[name] = domain.ValidateCache()
	}
	return results
}
