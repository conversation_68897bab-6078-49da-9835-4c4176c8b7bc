package domain

import (
	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// PasswordDomain 密码领域服务
type PasswordDomain struct {
	repo      types.PasswordRepository
	validator types.PasswordValidator
	config    *config.PasswordConfig
}

// NewPasswordDomain 创建密码领域服务
func NewPasswordDomain(repo types.PasswordRepository, validator types.PasswordValidator, config *config.PasswordConfig) *PasswordDomain {
	return &PasswordDomain{
		repo:      repo,
		validator: validator,
		config:    config,
	}
}

// SetPassword 设置密码
func (d *PasswordDomain) SetPassword(password string) error {
	// 1. 验证密码格式
	if err := d.validator.ValidateFormat(password); err != nil {
		return err
	}

	// 2. 验证密码强度
	if err := d.validator.ValidateStrength(password); err != nil {
		return err
	}

	// 3. 存储密码
	if err := d.repo.Store(password); err != nil {
		return types.WrapError(types.ErrCodeStorageFailed, "密码存储失败", err)
	}

	return nil
}

// VerifyPassword 验证密码
func (d *PasswordDomain) VerifyPassword(password string) error {
	// 1. 检查密码是否为空
	if password == "" {
		return types.ErrEmptyPassword
	}

	// 2. 检查密码是否存在
	if !d.repo.Exists() {
		return types.ErrPasswordNotSet
	}

	// 3. 验证密码
	if !d.repo.Verify(password) {
		return types.ErrPasswordIncorrect
	}

	return nil
}

// HasPassword 检查是否已设置密码
func (d *PasswordDomain) HasPassword() bool {
	return d.repo.Exists()
}

// ChangePassword 修改密码
func (d *PasswordDomain) ChangePassword(oldPassword, newPassword string) error {
	// 1. 验证旧密码
	if err := d.VerifyPassword(oldPassword); err != nil {
		return types.WrapError(types.ErrCodePasswordIncorrect, "旧密码验证失败", err)
	}

	// 2. 验证新密码不能与旧密码相同
	if oldPassword == newPassword {
		return types.NewPasswordError(types.ErrCodeSamePassword, "新密码不能与旧密码相同", nil)
	}

	// 3. 验证新密码格式
	if err := d.validator.ValidateFormat(newPassword); err != nil {
		return types.WrapError(types.ErrCodeValidationFailed, "新密码格式无效", err)
	}

	// 4. 验证新密码强度
	if err := d.validator.ValidateStrength(newPassword); err != nil {
		return types.WrapError(types.ErrCodePasswordWeak, "新密码强度不足", err)
	}

	// 5. 验证新密码能否解密现有数据
	if err := d.validator.ValidateDataDecryptability(oldPassword); err != nil {
		return types.WrapError(types.ErrCodeDataIntegrity, "数据完整性检查失败", err)
	}

	// 6. 设置新密码
	if err := d.repo.Store(newPassword); err != nil {
		return types.WrapError(types.ErrCodeStorageFailed, "新密码存储失败", err)
	}

	return nil
}

// RemovePassword 删除密码
func (d *PasswordDomain) RemovePassword() error {
	if !d.repo.Exists() {
		return types.ErrPasswordNotSet
	}

	if err := d.repo.Remove(); err != nil {
		return types.WrapError(types.ErrCodeStorageFailed, "密码删除失败", err)
	}

	return nil
}

// GetPasswordStrength 获取密码强度评估
func (d *PasswordDomain) GetPasswordStrength(password string) (*PasswordStrengthResult, error) {
	result := &PasswordStrengthResult{
		Password:    password,
		Score:       0,
		Level:       "弱",
		Issues:      []string{},
		Suggestions: []string{},
	}

	// 基础格式检查
	if err := d.validator.ValidateFormat(password); err != nil {
		result.Issues = append(result.Issues, err.Error())
		return result, nil
	}

	// 强度检查
	if err := d.validator.ValidateStrength(password); err != nil {
		result.Issues = append(result.Issues, err.Error())
	}

	// 计算分数（如果验证器支持高级功能）
	if advancedValidator, ok := d.validator.(*AdvancedPasswordValidator); ok {
		result.Score = advancedValidator.calculateComplexityScore(password)
	} else {
		result.Score = d.calculateBasicScore(password)
	}

	// 确定强度等级
	result.Level = d.getStrengthLevel(result.Score)

	// 生成建议
	result.Suggestions = d.generateSuggestions(password, result.Score)

	return result, nil
}

// PasswordStrengthResult 密码强度评估结果
type PasswordStrengthResult struct {
	Password    string   `json:"password"`
	Score       int      `json:"score"`
	Level       string   `json:"level"`
	Issues      []string `json:"issues"`
	Suggestions []string `json:"suggestions"`
}

// 私有方法
func (d *PasswordDomain) calculateBasicScore(password string) int {
	score := 0
	length := len(password)

	// 长度分数
	if length >= 8 {
		score += 20
	}
	if length >= 12 {
		score += 20
	}
	if length >= 16 {
		score += 10
	}

	// 字符类型分数
	hasLower := false
	hasUpper := false
	hasDigit := false
	hasSymbol := false

	for _, r := range password {
		if r >= 'a' && r <= 'z' {
			hasLower = true
		} else if r >= 'A' && r <= 'Z' {
			hasUpper = true
		} else if r >= '0' && r <= '9' {
			hasDigit = true
		} else {
			hasSymbol = true
		}
	}

	if hasLower {
		score += 10
	}
	if hasUpper {
		score += 10
	}
	if hasDigit {
		score += 10
	}
	if hasSymbol {
		score += 20
	}

	return score
}

func (d *PasswordDomain) getStrengthLevel(score int) string {
	if score >= 80 {
		return "强"
	} else if score >= 60 {
		return "中等"
	} else if score >= 40 {
		return "较弱"
	} else {
		return "弱"
	}
}

func (d *PasswordDomain) generateSuggestions(password string, score int) []string {
	suggestions := []string{}

	if len(password) < 12 {
		suggestions = append(suggestions, "建议密码长度至少12位")
	}

	hasLower := false
	hasUpper := false
	hasDigit := false
	hasSymbol := false

	for _, r := range password {
		if r >= 'a' && r <= 'z' {
			hasLower = true
		} else if r >= 'A' && r <= 'Z' {
			hasUpper = true
		} else if r >= '0' && r <= '9' {
			hasDigit = true
		} else {
			hasSymbol = true
		}
	}

	if !hasLower {
		suggestions = append(suggestions, "建议包含小写字母")
	}
	if !hasUpper {
		suggestions = append(suggestions, "建议包含大写字母")
	}
	if !hasDigit {
		suggestions = append(suggestions, "建议包含数字")
	}
	if !hasSymbol {
		suggestions = append(suggestions, "建议包含特殊字符")
	}

	if score < 60 {
		suggestions = append(suggestions, "建议使用更复杂的密码组合")
	}

	return suggestions
}

// ValidatePasswordChange 验证密码变更的前置条件
func (d *PasswordDomain) ValidatePasswordChange(oldPassword, newPassword string) error {
	// 1. 验证旧密码
	if err := d.VerifyPassword(oldPassword); err != nil {
		return err
	}

	// 2. 验证新密码
	if err := d.validator.ValidateFormat(newPassword); err != nil {
		return err
	}

	if err := d.validator.ValidateStrength(newPassword); err != nil {
		return err
	}

	// 3. 验证新旧密码不同
	if oldPassword == newPassword {
		return types.NewPasswordError(types.ErrCodeSamePassword, "新密码不能与旧密码相同", nil)
	}

	return nil
}
