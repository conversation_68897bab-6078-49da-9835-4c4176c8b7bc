package types

import (
	"time"

	"a8.tools/backend/pkg/cryptor"
)

// PasswordRepository 密码存储仓库接口
type PasswordRepository interface {
	// Store 存储密码（使用密钥包装方案）
	Store(password string) error

	// Verify 验证密码
	Verify(password string) bool

	// Exists 检查密码是否存在
	Exists() bool

	// Remove 删除密码
	Remove() error

	// GetWrappedKey 获取包装的密钥数据
	GetWrappedKey() (*WrappedKeyData, error)
}

// EncryptionKeyProvider 加密密钥提供者接口
type EncryptionKeyProvider interface {
	// GetDataEncryptionKey 获取数据加密密钥
	GetDataEncryptionKey() ([]byte, error)

	// DeriveKeyFromPassword 从密码派生密钥
	DeriveKeyFromPassword(password string) ([]byte, error)

	// CreateEncryptor 创建加密器实例
	CreateEncryptor() (cryptor.Encryptor, error)

	// ValidateKey 验证密钥有效性
	ValidateKey(key []byte) error
}

// PasswordCache 密码缓存接口
type PasswordCache interface {
	// Set 设置密码缓存
	Set(password string, ttl time.Duration) error

	// Get 获取缓存的密码
	Get() (string, error)

	// Clear 清除缓存
	Clear()

	// IsValid 检查缓存是否有效
	IsValid() bool

	// Refresh 刷新缓存过期时间
	Refresh(ttl time.Duration) error

	// GetStats 获取缓存统计信息
	GetStats() CacheStats
}

// PasswordValidator 密码验证器接口
type PasswordValidator interface {
	// ValidateStrength 验证密码强度
	ValidateStrength(password string) error

	// ValidateDataDecryptability 验证密码能否解密现有数据
	ValidateDataDecryptability(password string) error

	// ValidateFormat 验证密码格式
	ValidateFormat(password string) error
}

// EventPublisher 事件发布者接口
type EventPublisher interface {
	// PublishPasswordSet 发布密码设置事件
	PublishPasswordSet()

	// PublishPasswordChanged 发布密码变更事件
	PublishPasswordChanged()

	// PublishApplicationUnlocked 发布应用解锁事件
	PublishApplicationUnlocked()

	// PublishApplicationLocked 发布应用锁定事件
	PublishApplicationLocked()

	// PublishPasswordCacheExpired 发布密码缓存过期事件
	PublishPasswordCacheExpired()

	// PublishError 发布错误事件
	PublishError(err error)

	// PublishProgress 发布进度事件
	PublishProgress(stage string, progress float64, message string)
}

// WrappedKeyData 包装的密钥数据
type WrappedKeyData struct {
	Salt         []byte `json:"salt"`
	EncryptedDEK []byte `json:"encrypted_dek"`
	Version      int    `json:"version"`
}

// CacheStats 缓存统计信息
type CacheStats struct {
	HasPassword bool          `json:"has_password"`
	IsValid     bool          `json:"is_valid"`
	LastUpdate  time.Time     `json:"last_update"`
	TTL         time.Duration `json:"ttl"`
	AccessCount int64         `json:"access_count"`
	HitCount    int64         `json:"hit_count"`
}

// PasswordStatus 密码状态信息
type PasswordStatus struct {
	HasPassword bool      `json:"has_password"`
	IsUnlocked  bool      `json:"is_unlocked"`
	LastUnlock  time.Time `json:"last_unlock,omitempty"`
	CacheValid  bool      `json:"cache_valid"`
	CacheExpiry time.Time `json:"cache_expiry,omitempty"`
}

// ProgressCallback 进度回调函数类型
type ProgressCallback func(stage string, progress float64, message string)
