package types

import (
	"fmt"
	"time"
)

// PasswordError 密码模块统一错误类型
type PasswordError struct {
	Code      ErrorCode              `json:"code"`
	Message   string                 `json:"message"`
	Cause     error                  `json:"cause,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
	Context   map[string]interface{} `json:"context,omitempty"`
}

func (e *PasswordError) Error() string {
	if e.Cause != nil {
		return fmt.Sprintf("[%s] %s: %v", e.Code, e.Message, e.Cause)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

func (e *PasswordError) Unwrap() error {
	return e.Cause
}

func (e *PasswordError) WithContext(key string, value interface{}) *PasswordError {
	if e.Context == nil {
		e.Context = make(map[string]interface{})
	}
	e.Context[key] = value
	return e
}

// NewPasswordError 创建新的密码错误
func NewPasswordError(code ErrorCode, message string, cause error) *PasswordError {
	return &PasswordError{
		Code:      code,
		Message:   message,
		Cause:     cause,
		Timestamp: time.Now(),
	}
}

// ErrorCode 错误代码枚举
type ErrorCode string

const (
	// 密码相关错误
	ErrCodePasswordNotSet     ErrorCode = "PASSWORD_NOT_SET"
	ErrCodePasswordIncorrect  ErrorCode = "PASSWORD_INCORRECT"
	ErrCodePasswordWeak       ErrorCode = "PASSWORD_WEAK"
	ErrCodeSamePassword       ErrorCode = "SAME_PASSWORD"
	ErrCodeEmptyPassword      ErrorCode = "EMPTY_PASSWORD"
	ErrCodePasswordTooShort   ErrorCode = "PASSWORD_TOO_SHORT"

	// 缓存相关错误
	ErrCodeCacheExpired       ErrorCode = "CACHE_EXPIRED"
	ErrCodeCacheFailed        ErrorCode = "CACHE_FAILED"
	ErrCodeCacheInvalid       ErrorCode = "CACHE_INVALID"

	// 加密相关错误
	ErrCodeEncryptionFailed   ErrorCode = "ENCRYPTION_FAILED"
	ErrCodeDecryptionFailed   ErrorCode = "DECRYPTION_FAILED"
	ErrCodeKeyDerivationFailed ErrorCode = "KEY_DERIVATION_FAILED"
	ErrCodeKeyInvalid         ErrorCode = "KEY_INVALID"

	// 存储相关错误
	ErrCodeStorageFailed      ErrorCode = "STORAGE_FAILED"
	ErrCodeKeyringFailed      ErrorCode = "KEYRING_FAILED"
	ErrCodeRepositoryFailed   ErrorCode = "REPOSITORY_FAILED"

	// 验证相关错误
	ErrCodeValidationFailed   ErrorCode = "VALIDATION_FAILED"
	ErrCodeDataIntegrity      ErrorCode = "DATA_INTEGRITY_CHECK_FAILED"
	ErrCodeInvalidFormat      ErrorCode = "INVALID_FORMAT"

	// 系统相关错误
	ErrCodeConfigInvalid      ErrorCode = "CONFIG_INVALID"
	ErrCodeDependencyFailed   ErrorCode = "DEPENDENCY_FAILED"
	ErrCodeInitializationFailed ErrorCode = "INITIALIZATION_FAILED"
	ErrCodeUnknownError       ErrorCode = "UNKNOWN_ERROR"
)

// IsPasswordError 检查错误是否为密码错误
func IsPasswordError(err error) bool {
	_, ok := err.(*PasswordError)
	return ok
}

// GetErrorCode 获取错误代码
func GetErrorCode(err error) ErrorCode {
	if passwordErr, ok := err.(*PasswordError); ok {
		return passwordErr.Code
	}
	return ErrCodeUnknownError
}

// WrapError 包装普通错误为密码错误
func WrapError(code ErrorCode, message string, cause error) *PasswordError {
	return NewPasswordError(code, message, cause)
}

// 预定义的常用错误
var (
	ErrPasswordNotSet     = NewPasswordError(ErrCodePasswordNotSet, "应用密码未设置", nil)
	ErrPasswordIncorrect  = NewPasswordError(ErrCodePasswordIncorrect, "密码不正确", nil)
	ErrCacheExpired       = NewPasswordError(ErrCodeCacheExpired, "密码缓存已过期", nil)
	ErrEmptyPassword      = NewPasswordError(ErrCodeEmptyPassword, "密码不能为空", nil)
)
