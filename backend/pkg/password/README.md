# 密码模块重构文档

## 概述

本文档描述了密码模块的重构架构和使用方法。重构后的模块采用了清晰的分层架构，解决了原有的循环依赖问题，提高了代码的可维护性和可测试性。

## 架构设计

### 分层结构

```
backend/pkg/password/
├── types/              # 类型定义层
│   ├── interfaces.go   # 核心接口定义
│   └── errors.go       # 错误类型定义
├── config/             # 配置管理
│   └── config.go       # 统一配置结构
├── repository/         # 仓库层
│   └── keyring.go      # Keyring 存储实现
├── provider/           # 提供者层
│   ├── cache.go        # 缓存提供者
│   ├── encryption.go   # 加密提供者
│   └── events.go       # 事件发布器
├── domain/             # 领域层
│   ├── password.go     # 密码领域服务
│   ├── encryption.go   # 加密领域服务
│   ├── cache.go        # 缓存领域服务
│   └── validation.go   # 密码验证器
├── services/password/v2/ # 服务层
│   └── password_service.go # Wails 绑定服务
├── interfaces.go       # 接口重导出（向后兼容）
├── errors.go          # 错误重导出（向后兼容）
├── container.go       # 依赖注入容器
├── application.go     # 应用服务
└── orchestrator.go    # 密码变更协调器
```

### 核心组件

#### 1. 依赖注入容器 (Container)
负责管理所有组件的依赖关系和生命周期。

#### 2. 应用服务 (PasswordApplicationService)
协调各个领域服务，提供高级业务操作。

#### 3. 领域服务
- **PasswordDomain**: 密码核心业务逻辑
- **EncryptionDomain**: 加密相关业务逻辑
- **CacheDomain**: 缓存管理业务逻辑

#### 4. 密码变更协调器 (PasswordOrchestrator)
处理复杂的密码变更流程，支持进度反馈和错误回滚。

#### 5. Wails 绑定服务 (PasswordService)
为前端提供简单易用的 API 接口。

## 使用方法

### 1. 基本初始化

```go
import (
    "a8.tools/backend/pkg/password"
    "a8.tools/backend/pkg/password/config"
)

// 加载配置
config := config.LoadDefaultConfig()

// 创建容器
container := password.NewContainer(config, logger)

// 初始化容器
err := container.Initialize()
if err != nil {
    log.Fatal("初始化失败:", err)
}

// 获取应用服务
appService := container.GetApplicationService()
```

### 2. Wails 集成

```go
import (
    "a8.tools/backend/services/password/v2"
)

// 创建 Wails 绑定服务
passwordService := v2.NewPasswordService()

// 在 Wails 应用启动后初始化
err := passwordService.Init(wailsApp)
if err != nil {
    log.Fatal("密码服务初始化失败:", err)
}
```

### 3. 基本操作

```go
// 设置密码
err := passwordService.SetPassword("your-password")

// 验证密码
isValid := passwordService.VerifyPassword("your-password")

// 解锁应用
err := passwordService.UnlockApplication("your-password")

// 检查解锁状态
isUnlocked := passwordService.IsUnlocked()

// 修改密码
err := passwordService.ChangePassword("old-password", "new-password")

// 带进度反馈的密码修改
err := passwordService.ChangePasswordWithProgress("old-password", "new-password")
```

### 4. 高级功能

```go
// 获取密码强度评估
strength, err := passwordService.GetPasswordStrength("password")

// 获取系统状态
status, err := passwordService.GetStatus()

// 获取缓存统计
stats, err := passwordService.GetCacheStats()

// 验证加密功能
err := passwordService.ValidateEncryption()
```

## 配置说明

配置文件支持以下选项：

```go
type Config struct {
    Password   PasswordConfig   `yaml:"password"`
    Encryption EncryptionConfig `yaml:"encryption"`
    Cache      CacheConfig      `yaml:"cache"`
    Validation ValidationConfig `yaml:"validation"`
    Events     EventsConfig     `yaml:"events"`
}
```

### 密码配置
- `ServiceName`: Keyring 服务名称
- `KeyName`: Keyring 密钥名称

### 加密配置
- `Algorithm`: 加密算法
- `KeyLength`: 密钥长度
- `SaltLength`: 盐值长度
- `Iterations`: PBKDF2 迭代次数

### 缓存配置
- `TTL`: 缓存过期时间
- `MaxRetries`: 最大重试次数
- `RetryDelay`: 重试延迟

### 验证配置
- `StrengthRequirement`: 密码强度要求
- `EnableDataDecryptabilityCheck`: 是否启用数据解密能力检查

## 错误处理

模块使用统一的错误类型 `PasswordError`：

```go
type PasswordError struct {
    Code      ErrorCode
    Message   string
    Cause     error
    Timestamp time.Time
    Context   map[string]interface{}
}
```

常见错误代码：
- `ErrCodePasswordNotSet`: 密码未设置
- `ErrCodePasswordIncorrect`: 密码不正确
- `ErrCodeCacheExpired`: 缓存已过期
- `ErrCodeEncryptionFailed`: 加密失败

## 事件系统

模块使用统一的事件系统（`backend/pkg/events`），支持以下密码相关事件：

### 密码操作事件
- `EventPasswordSet`: 密码设置完成
- `EventPasswordUpdated`: 密码修改完成
- `EventPasswordVerified`: 密码验证成功
- `EventPasswordCleared`: 密码清除/应用锁定
- `EventPasswordInvalid`: 密码验证失败
- `EventPasswordRequired`: 需要输入密码
- `EventPasswordCacheExpired`: 密码缓存过期
- `EventPasswordError`: 密码操作错误

### 应用状态事件
- `EventApplicationUnlocked`: 应用解锁
- `EventApplicationLocked`: 应用锁定

### 进度事件
- `EventPasswordChangeStarted`: 密码修改开始
- `EventPasswordChangeProgress`: 密码修改进度
- `EventPasswordChangeComplete`: 密码修改完成
- `EventPasswordChangeFailed`: 密码修改失败

### 事件载荷结构
```go
// 密码操作事件载荷
type PasswordOperationPayload struct {
    Operation string `json:"operation"`         // 操作类型 (set, verify, clear)
    Success   bool   `json:"success"`           // 操作是否成功
    Message   string `json:"message,omitempty"` // 相关消息
    Error     string `json:"error,omitempty"`   // 错误信息
}

// 密码修改进度事件载荷
type PasswordChangePayload struct {
    TotalTables     int     `json:"total_tables"`     // 总表数
    ProcessedTables int     `json:"processed_tables"` // 已处理表数
    CurrentTable    string  `json:"current_table"`    // 当前处理表
    Progress        float64 `json:"progress"`         // 进度百分比 (0-100)
    Stage           string  `json:"stage"`            // 当前阶段描述
}
```

## 测试

模块提供了模拟实现用于测试：

```go
// 使用模拟事件发布器
mockPublisher := provider.NewMockEventPublisher(logger)

// 获取事件记录
events := mockPublisher.GetEvents()
```

## 迁移指南

从旧版本迁移到新架构：

1. 更新导入路径
2. 使用新的初始化方式
3. 替换直接的组件调用为服务调用
4. 更新错误处理逻辑

## 性能优化

- 使用缓存减少重复计算
- 支持批量操作
- 异步事件发布
- 连接池管理

## 安全考虑

- 密码不会以明文形式存储
- 使用安全的密钥派生函数
- 支持密钥轮换
- 内存安全清理
