package repository

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/zalando/go-keyring"
	"golang.org/x/crypto/pbkdf2"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/types"
)

// KeyringRepository Keyring 存储仓库实现
type KeyringRepository struct {
	serviceName string
	keyName     string
	config      *config.EncryptionConfig
	cache       *ExistenceCache
	mu          sync.RWMutex
}

// NewKeyringRepository 创建 Keyring 仓库
func NewKeyringRepository(serviceName, keyName string, config *config.EncryptionConfig) *KeyringRepository {
	return &KeyringRepository{
		serviceName: serviceName,
		keyName:     keyName,
		config:      config,
		cache:       NewExistenceCache(30 * time.Second),
	}
}

// Store 存储密码
func (r *KeyringRepository) Store(password string) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	// 1. 生成随机的数据加密密钥（DEK）
	dek := make([]byte, 32)
	if _, err := rand.Read(dek); err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "生成DEK失败", err)
	}

	// 2. 生成随机盐值
	salt := make([]byte, r.config.SaltLength)
	if _, err := rand.Read(salt); err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "生成盐值失败", err)
	}

	// 3. 使用PBKDF2从密码派生密钥加密密钥（KEK）
	kek := pbkdf2.Key([]byte(password), salt, r.config.KeyDerivationRounds, 32, sha256.New)

	// 4. 使用KEK加密DEK
	encryptedDEK, err := r.encryptDEK(dek, kek)
	if err != nil {
		return types.WrapError(types.ErrCodeEncryptionFailed, "加密DEK失败", err)
	}

	// 5. 创建包装密钥结构
	wrappedKey := types.WrappedKeyData{
		Salt:         salt,
		EncryptedDEK: encryptedDEK,
		Version:      1,
	}

	// 6. 序列化并存储到keyring
	wrappedData, err := json.Marshal(wrappedKey)
	if err != nil {
		return types.WrapError(types.ErrCodeStorageFailed, "序列化包装密钥失败", err)
	}

	encodedData := base64.StdEncoding.EncodeToString(wrappedData)
	if err := keyring.Set(r.serviceName, r.keyName, encodedData); err != nil {
		return types.WrapError(types.ErrCodeKeyringFailed, "存储到keyring失败", err)
	}

	// 7. 更新缓存
	r.cache.Set(r.keyName, true)

	return nil
}

// Verify 验证密码
func (r *KeyringRepository) Verify(password string) bool {
	_, err := r.getDataEncryptionKey(password)
	return err == nil
}

// Exists 检查密码是否存在
func (r *KeyringRepository) Exists() bool {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 1. 先检查缓存
	if cached, found := r.cache.Get(r.keyName); found {
		return cached
	}

	// 2. 缓存未命中，查询keyring
	_, err := keyring.Get(r.serviceName, r.keyName)
	exists := err == nil

	// 3. 更新缓存
	r.cache.Set(r.keyName, exists)

	return exists
}

// Remove 删除密码
func (r *KeyringRepository) Remove() error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if err := keyring.Delete(r.serviceName, r.keyName); err != nil {
		return types.WrapError(types.ErrCodeKeyringFailed, "从keyring删除失败", err)
	}

	// 更新缓存
	r.cache.Set(r.keyName, false)

	return nil
}

// GetWrappedKey 获取包装的密钥数据
func (r *KeyringRepository) GetWrappedKey() (*types.WrappedKeyData, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	// 从keyring获取包装的密钥数据
	encodedData, err := keyring.Get(r.serviceName, r.keyName)
	if err != nil {
		return nil, types.WrapError(types.ErrCodeKeyringFailed, "从keyring获取密钥失败", err)
	}

	// Base64解码
	wrappedData, err := base64.StdEncoding.DecodeString(encodedData)
	if err != nil {
		return nil, types.WrapError(types.ErrCodeStorageFailed, "解码包装密钥失败", err)
	}

	// 反序列化包装密钥
	var wrappedKey types.WrappedKeyData
	if err := json.Unmarshal(wrappedData, &wrappedKey); err != nil {
		return nil, types.WrapError(types.ErrCodeStorageFailed, "反序列化包装密钥失败", err)
	}

	return &wrappedKey, nil
}

// 私有方法
func (r *KeyringRepository) getDataEncryptionKey(password string) ([]byte, error) {
	wrappedKey, err := r.GetWrappedKey()
	if err != nil {
		return nil, err
	}

	// 使用PBKDF2从密码派生KEK
	kek := pbkdf2.Key([]byte(password), wrappedKey.Salt, r.config.KeyDerivationRounds, 32, sha256.New)

	// 解密DEK
	dek, err := r.decryptDEK(wrappedKey.EncryptedDEK, kek)
	if err != nil {
		return nil, types.WrapError(types.ErrCodeDecryptionFailed, "解密DEK失败", err)
	}

	return dek, nil
}

func (r *KeyringRepository) encryptDEK(dek, kek []byte) ([]byte, error) {
	// 使用AES-GCM加密DEK
	block, err := aes.NewCipher(kek)
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return nil, fmt.Errorf("生成nonce失败: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, dek, nil)
	return ciphertext, nil
}

func (r *KeyringRepository) decryptDEK(encryptedDEK, kek []byte) ([]byte, error) {
	// 使用AES-GCM解密DEK
	block, err := aes.NewCipher(kek)
	if err != nil {
		return nil, fmt.Errorf("创建AES cipher失败: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("创建GCM失败: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(encryptedDEK) < nonceSize {
		return nil, fmt.Errorf("加密数据长度不足")
	}

	nonce, ciphertext := encryptedDEK[:nonceSize], encryptedDEK[nonceSize:]

	dek, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("解密失败: %w", err)
	}

	return dek, nil
}

// ExistenceCache 存在性缓存
type ExistenceCache struct {
	cache   map[string]cacheEntry
	ttl     time.Duration
	enabled bool
	mu      sync.RWMutex
}

type cacheEntry struct {
	value     bool
	timestamp time.Time
}

// NewExistenceCache 创建存在性缓存
func NewExistenceCache(ttl time.Duration) *ExistenceCache {
	return &ExistenceCache{
		cache:   make(map[string]cacheEntry),
		ttl:     ttl,
		enabled: true,
	}
}

// Get 获取缓存值
func (c *ExistenceCache) Get(key string) (bool, bool) {
	if !c.enabled {
		return false, false
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	entry, exists := c.cache[key]
	if !exists {
		return false, false
	}

	// 检查是否过期
	if time.Since(entry.timestamp) > c.ttl {
		return false, false
	}

	return entry.value, true
}

// Set 设置缓存值
func (c *ExistenceCache) Set(key string, value bool) {
	if !c.enabled {
		return
	}

	c.mu.Lock()
	defer c.mu.Unlock()

	c.cache[key] = cacheEntry{
		value:     value,
		timestamp: time.Now(),
	}
}

// SetEnabled 设置缓存是否启用
func (c *ExistenceCache) SetEnabled(enabled bool) {
	c.mu.Lock()
	defer c.mu.Unlock()

	c.enabled = enabled
	if !enabled {
		c.cache = make(map[string]cacheEntry)
	}
}
