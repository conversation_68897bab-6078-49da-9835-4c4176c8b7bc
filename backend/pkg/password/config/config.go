package config

import (
	"fmt"
	"os"
	"strconv"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 密码模块统一配置
type Config struct {
	Password   PasswordConfig   `yaml:"password"`
	Cache      CacheConfig      `yaml:"cache"`
	Encryption EncryptionConfig `yaml:"encryption"`
	Validation ValidationConfig `yaml:"validation"`
	Events     EventsConfig     `yaml:"events"`
}

// PasswordConfig 密码相关配置
type PasswordConfig struct {
	ServiceName string `yaml:"service_name"`
	KeyName     string `yaml:"key_name"`
}

// CacheConfig 缓存相关配置
type CacheConfig struct {
	TTL             time.Duration `yaml:"ttl"`
	MaxRetries      int           `yaml:"max_retries"`
	RetryInterval   time.Duration `yaml:"retry_interval"`
	CleanupInterval time.Duration `yaml:"cleanup_interval"`
}

// EncryptionConfig 加密相关配置
type EncryptionConfig struct {
	Algorithm           string      `yaml:"algorithm"`
	KeyDerivationRounds int         `yaml:"key_derivation_rounds"`
	SaltLength          int         `yaml:"salt_length"`
	CacheConfig         CacheConfig `yaml:"cache"`
}

// ValidationConfig 验证相关配置
type ValidationConfig struct {
	StrengthRequirement  PasswordStrengthRequirement `yaml:"strength_requirement"`
	EnableDataValidation bool                        `yaml:"enable_data_validation"`
	SampleSize           int                         `yaml:"sample_size"`
}

// PasswordStrengthRequirement 密码强度要求
type PasswordStrengthRequirement struct {
	MinLength        int  `yaml:"min_length"`
	RequireUppercase bool `yaml:"require_uppercase"`
	RequireLowercase bool `yaml:"require_lowercase"`
	RequireNumbers   bool `yaml:"require_numbers"`
	RequireSymbols   bool `yaml:"require_symbols"`
}

// EventsConfig 事件相关配置
type EventsConfig struct {
	Enabled       bool          `yaml:"enabled"`
	MaxRetries    int           `yaml:"max_retries"`
	RetryInterval time.Duration `yaml:"retry_interval"`
	Timeout       time.Duration `yaml:"timeout"`
}

// ConfigLoader 配置加载器
type ConfigLoader struct {
	configPath string
}

// NewConfigLoader 创建配置加载器
func NewConfigLoader(configPath string) *ConfigLoader {
	return &ConfigLoader{configPath: configPath}
}

// Load 加载配置
func (l *ConfigLoader) Load() (*Config, error) {
	// 1. 设置默认配置
	config := l.getDefaultConfig()

	// 2. 如果配置文件存在，则加载配置文件
	if _, err := os.Stat(l.configPath); err == nil {
		if err := l.loadFromFile(config); err != nil {
			return nil, fmt.Errorf("加载配置文件失败: %w", err)
		}
	}

	// 3. 从环境变量覆盖配置
	l.loadFromEnv(config)

	// 4. 验证配置
	if err := l.validateConfig(config); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return config, nil
}

// getDefaultConfig 获取默认配置
func (l *ConfigLoader) getDefaultConfig() *Config {
	return &Config{
		Password: PasswordConfig{
			ServiceName: "a8.tools",
			KeyName:     "app_password",
		},
		Cache: CacheConfig{
			TTL:             5 * time.Minute,
			MaxRetries:      3,
			RetryInterval:   100 * time.Millisecond,
			CleanupInterval: 10 * time.Minute,
		},
		Encryption: EncryptionConfig{
			Algorithm:           "AES-GCM",
			KeyDerivationRounds: 100000,
			SaltLength:          32,
			CacheConfig: CacheConfig{
				TTL:             5 * time.Minute,
				MaxRetries:      3,
				RetryInterval:   100 * time.Millisecond,
				CleanupInterval: 10 * time.Minute,
			},
		},
		Validation: ValidationConfig{
			StrengthRequirement: PasswordStrengthRequirement{
				MinLength:        8,
				RequireUppercase: false,
				RequireLowercase: false,
				RequireNumbers:   false,
				RequireSymbols:   false,
			},
			EnableDataValidation: true,
			SampleSize:           10,
		},
		Events: EventsConfig{
			Enabled:       true,
			MaxRetries:    3,
			RetryInterval: 100 * time.Millisecond,
			Timeout:       5 * time.Second,
		},
	}
}

// loadFromFile 从文件加载配置
func (l *ConfigLoader) loadFromFile(config *Config) error {
	data, err := os.ReadFile(l.configPath)
	if err != nil {
		return err
	}

	return yaml.Unmarshal(data, config)
}

// loadFromEnv 从环境变量加载配置
func (l *ConfigLoader) loadFromEnv(config *Config) {
	// 缓存TTL
	if ttl := os.Getenv("PASSWORD_CACHE_TTL"); ttl != "" {
		if duration, err := time.ParseDuration(ttl); err == nil {
			config.Cache.TTL = duration
		}
	}

	// 密钥派生轮数
	if rounds := os.Getenv("PASSWORD_KEY_DERIVATION_ROUNDS"); rounds != "" {
		if r, err := strconv.Atoi(rounds); err == nil {
			config.Encryption.KeyDerivationRounds = r
		}
	}

	// 密码最小长度
	if minLen := os.Getenv("PASSWORD_MIN_LENGTH"); minLen != "" {
		if length, err := strconv.Atoi(minLen); err == nil {
			config.Validation.StrengthRequirement.MinLength = length
		}
	}

	// 事件系统开关
	if enabled := os.Getenv("PASSWORD_EVENTS_ENABLED"); enabled != "" {
		if e, err := strconv.ParseBool(enabled); err == nil {
			config.Events.Enabled = e
		}
	}
}

// validateConfig 验证配置
func (l *ConfigLoader) validateConfig(config *Config) error {
	// 验证缓存配置
	if config.Cache.TTL <= 0 {
		return fmt.Errorf("缓存TTL必须大于0")
	}

	// 验证加密配置
	if config.Encryption.KeyDerivationRounds < 10000 {
		return fmt.Errorf("密钥派生轮数不能少于10000")
	}

	if config.Encryption.SaltLength < 16 {
		return fmt.Errorf("盐值长度不能少于16字节")
	}

	// 验证密码强度配置
	if config.Validation.StrengthRequirement.MinLength < 1 {
		return fmt.Errorf("密码最小长度不能少于1")
	}

	return nil
}

// LoadDefaultConfig 加载默认配置
func LoadDefaultConfig() *Config {
	loader := NewConfigLoader("")
	return loader.getDefaultConfig()
}

// LoadConfigFromFile 从文件加载配置
func LoadConfigFromFile(configPath string) (*Config, error) {
	loader := NewConfigLoader(configPath)
	return loader.Load()
}
