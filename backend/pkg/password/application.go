package password

import (
	"log/slog"
	"time"

	"a8.tools/backend/pkg/password/config"
	"a8.tools/backend/pkg/password/domain"
)

// PasswordApplicationServiceImpl 密码应用服务实现
// 同时作为 Wails 绑定服务，直接暴露给前端
type PasswordApplicationServiceImpl struct {
	passwordDomain   *domain.PasswordDomain
	encryptionDomain *domain.EncryptionDomain
	cacheDomain      *domain.CacheDomain
	eventPublisher   EventPublisher
	config           *config.Config
	logger           *slog.Logger
}

// NewPasswordApplicationService 创建密码应用服务
func NewPasswordApplicationService(
	passwordDomain *domain.PasswordDomain,
	encryptionDomain *domain.EncryptionDomain,
	cacheDomain *domain.CacheDomain,
	eventPublisher EventPublisher,
	config *config.Config,
	logger *slog.Logger,
) *PasswordApplicationServiceImpl {
	return &PasswordApplicationServiceImpl{
		passwordDomain:   passwordDomain,
		encryptionDomain: encryptionDomain,
		cacheDomain:      cacheDomain,
		eventPublisher:   eventPublisher,
		config:           config,
		logger:           logger,
	}
}

//go:wails:bind
func NewPasswordApplicationServiceForWails() *PasswordApplicationServiceImpl {
	// 从全局容器获取应用服务
	container := GetGlobalContainer()
	if container == nil {
		// 如果容器未初始化，返回一个空的服务
		// 实际初始化会在 Wails 应用启动后进行
		return &PasswordApplicationServiceImpl{}
	}

	appService := container.GetApplicationService()
	if impl, ok := appService.(*PasswordApplicationServiceImpl); ok {
		return impl
	}

	// 如果类型不匹配，返回空服务
	return &PasswordApplicationServiceImpl{}
}

// SetPassword 设置密码
func (s *PasswordApplicationServiceImpl) SetPassword(password string) error {
	s.logger.Info("开始设置应用密码")

	// 1. 设置密码
	if err := s.passwordDomain.SetPassword(password); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	// 2. 缓存密码
	if err := s.cacheDomain.CachePassword(password); err != nil {
		// 密码设置成功但缓存失败，记录警告但不返回错误
		s.logger.Warn("密码缓存失败", "error", err)
		s.eventPublisher.PublishError(err)
	}

	// 3. 发布成功事件
	s.eventPublisher.PublishPasswordSet()
	s.logger.Info("应用密码设置成功")

	return nil
}

// VerifyPassword 验证密码
func (s *PasswordApplicationServiceImpl) VerifyPassword(password string) error {
	return s.passwordDomain.VerifyPassword(password)
}

// HasPassword 检查是否已设置密码
func (s *PasswordApplicationServiceImpl) HasPassword() bool {
	return s.passwordDomain.HasPassword()
}

// UnlockApplication 解锁应用
func (s *PasswordApplicationServiceImpl) UnlockApplication(password string) error {
	s.logger.Info("开始解锁应用")

	// 1. 验证密码
	if err := s.passwordDomain.VerifyPassword(password); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	// 2. 缓存密码
	if err := s.cacheDomain.CachePassword(password); err != nil {
		s.eventPublisher.PublishError(err)
		return WrapError(ErrCodeCacheFailed, "密码缓存失败", err)
	}

	// 3. 验证加密功能
	if err := s.encryptionDomain.ValidateEncryption(); err != nil {
		s.logger.Warn("加密功能验证失败", "error", err)
		// 不阻止解锁，但记录警告
	}

	// 4. 发布解锁成功事件
	s.eventPublisher.PublishApplicationUnlocked()
	s.logger.Info("应用解锁成功")

	return nil
}

// LockApplication 锁定应用
func (s *PasswordApplicationServiceImpl) LockApplication() error {
	s.logger.Info("开始锁定应用")

	// 1. 清除密码缓存
	s.cacheDomain.ClearCache()

	// 2. 清除加密器缓存
	s.encryptionDomain.ClearEncryptionCache()

	// 3. 发布锁定事件
	s.eventPublisher.PublishApplicationLocked()
	s.logger.Info("应用锁定成功")

	return nil
}

// IsUnlocked 检查应用是否已解锁
func (s *PasswordApplicationServiceImpl) IsUnlocked() bool {
	return s.cacheDomain.IsPasswordCached()
}

// ChangePassword 修改密码
func (s *PasswordApplicationServiceImpl) ChangePassword(oldPassword, newPassword string) error {
	s.logger.Info("开始修改密码")

	// 1. 验证密码变更的前置条件
	if err := s.passwordDomain.ValidatePasswordChange(oldPassword, newPassword); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	// 2. 执行密码变更
	if err := s.passwordDomain.ChangePassword(oldPassword, newPassword); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	// 3. 更新缓存
	if err := s.cacheDomain.CachePassword(newPassword); err != nil {
		s.logger.Warn("新密码缓存失败", "error", err)
	}

	// 4. 清除加密器缓存（强制重新创建）
	s.encryptionDomain.ClearEncryptionCache()

	// 5. 发布密码变更事件
	s.eventPublisher.PublishPasswordChanged()
	s.logger.Info("密码修改成功")

	return nil
}

// ChangePasswordWithProgress 修改密码（带进度反馈）
func (s *PasswordApplicationServiceImpl) ChangePasswordWithProgress(oldPassword, newPassword string, progressCallback ProgressCallback) error {
	s.logger.Info("开始修改密码（带进度反馈）")

	if progressCallback == nil {
		progressCallback = func(stage string, progress float64, message string) {
			s.eventPublisher.PublishProgress(stage, progress, message)
		}
	}

	progressCallback("validation", 0.0, "验证密码")

	// 1. 验证密码变更的前置条件
	if err := s.passwordDomain.ValidatePasswordChange(oldPassword, newPassword); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	progressCallback("backup", 10.0, "创建数据备份")
	// TODO: 实现数据备份逻辑

	progressCallback("reencryption", 20.0, "重新加密数据")

	// 2. 重新加密数据
	if err := s.encryptionDomain.ReencryptData(oldPassword, newPassword, progressCallback); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	progressCallback("password_update", 90.0, "更新密码")

	// 3. 更新密码
	if err := s.passwordDomain.ChangePassword(oldPassword, newPassword); err != nil {
		s.eventPublisher.PublishError(err)
		return err
	}

	// 4. 更新缓存
	if err := s.cacheDomain.CachePassword(newPassword); err != nil {
		s.logger.Warn("新密码缓存失败", "error", err)
	}

	progressCallback("completion", 100.0, "密码修改完成")

	// 5. 发布密码变更事件
	s.eventPublisher.PublishPasswordChanged()
	s.logger.Info("密码修改成功（带进度反馈）")

	return nil
}

// GetStatus 获取密码状态
func (s *PasswordApplicationServiceImpl) GetStatus() *PasswordStatus {
	cacheStatus := s.cacheDomain.GetCacheStatus()

	return &PasswordStatus{
		HasPassword: s.passwordDomain.HasPassword(),
		IsUnlocked:  cacheStatus.IsValid,
		LastUnlock:  cacheStatus.LastUpdate,
		CacheValid:  cacheStatus.IsValid,
		CacheExpiry: cacheStatus.ExpiresAt,
	}
}

// GetCacheStats 获取缓存统计信息
func (s *PasswordApplicationServiceImpl) GetCacheStats() *CacheStats {
	cacheStatus := s.cacheDomain.GetCacheStatus()

	return &CacheStats{
		HasPassword: cacheStatus.HasPassword,
		IsValid:     cacheStatus.IsValid,
		LastUpdate:  cacheStatus.LastUpdate,
		TTL:         cacheStatus.TTL,
		AccessCount: cacheStatus.AccessCount,
		HitCount:    cacheStatus.HitCount,
	}
}

// ClearCache 清除缓存
func (s *PasswordApplicationServiceImpl) ClearCache() error {
	s.cacheDomain.ClearCache()
	s.encryptionDomain.ClearEncryptionCache()
	return nil
}

// RefreshCache 刷新缓存
func (s *PasswordApplicationServiceImpl) RefreshCache() error {
	return s.cacheDomain.RefreshCache()
}

// GetPasswordStrength 获取密码强度评估
func (s *PasswordApplicationServiceImpl) GetPasswordStrength(password string) (*domain.PasswordStrengthResult, error) {
	return s.passwordDomain.GetPasswordStrength(password)
}

// GetEncryptor 获取加密器
func (s *PasswordApplicationServiceImpl) GetEncryptor() (interface{}, error) {
	return s.encryptionDomain.GetEncryptor()
}

// ValidateEncryption 验证加密功能
func (s *PasswordApplicationServiceImpl) ValidateEncryption() error {
	return s.encryptionDomain.ValidateEncryption()
}

// SetEventPublisher 设置事件发布器
func (s *PasswordApplicationServiceImpl) SetEventPublisher(publisher EventPublisher) {
	s.eventPublisher = publisher
}

// GetEncryptionStatus 获取加密状态
func (s *PasswordApplicationServiceImpl) GetEncryptionStatus() *domain.EncryptionStatus {
	return s.encryptionDomain.GetEncryptionStatus()
}

// ExtendCacheTime 延长缓存时间
func (s *PasswordApplicationServiceImpl) ExtendCacheTime(additionalTime time.Duration) error {
	return s.cacheDomain.ExtendCacheTime(additionalTime)
}

// GetCacheMetrics 获取缓存指标
func (s *PasswordApplicationServiceImpl) GetCacheMetrics() *domain.CacheMetrics {
	return s.cacheDomain.GetCacheMetrics()
}

// ValidatePasswordStrength 验证密码强度
func (s *PasswordApplicationServiceImpl) ValidatePasswordStrength(password string) error {
	validator := s.passwordDomain

	// 使用领域服务的验证器
	if err := validator.VerifyPassword(password); err != nil {
		return err
	}

	return nil
}

// ===== Wails 前端绑定相关类型和方法 =====

// PasswordStatusForWails 前端密码状态信息
type PasswordStatusForWails struct {
	HasPassword bool   `json:"has_password"`
	IsUnlocked  bool   `json:"is_unlocked"`
	LastUnlock  string `json:"last_unlock,omitempty"`
	CacheValid  bool   `json:"cache_valid"`
	CacheExpiry string `json:"cache_expiry,omitempty"`
}

// GetStatusForWails 获取前端格式的密码状态
func (s *PasswordApplicationServiceImpl) GetStatusForWails() (*PasswordStatusForWails, error) {
	status := s.GetStatus()
	return &PasswordStatusForWails{
		HasPassword: status.HasPassword,
		IsUnlocked:  status.IsUnlocked,
		LastUnlock:  status.LastUnlock.Format("2006-01-02 15:04:05"),
		CacheValid:  status.IsUnlocked,
		CacheExpiry: status.CacheExpiry.Format("2006-01-02 15:04:05"),
	}, nil
}

// VerifyPasswordForWails 验证密码（返回布尔值，适合前端）
func (s *PasswordApplicationServiceImpl) VerifyPasswordForWails(password string) bool {
	return s.VerifyPassword(password) == nil
}
