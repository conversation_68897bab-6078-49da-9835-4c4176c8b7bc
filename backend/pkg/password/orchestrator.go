package password

import (
	"fmt"
	"log/slog"
	"time"

	"a8.tools/backend/pkg/password/domain"
)

// PasswordOrchestrator 密码变更协调器
type PasswordOrchestrator struct {
	passwordDomain   *domain.PasswordDomain
	encryptionDomain *domain.EncryptionDomain
	cacheDomain      *domain.CacheDomain
	eventPublisher   EventPublisher
	logger           *slog.Logger
}

// NewPasswordOrchestrator 创建密码变更协调器
func NewPasswordOrchestrator(
	passwordDomain *domain.PasswordDomain,
	encryptionDomain *domain.EncryptionDomain,
	cacheDomain *domain.CacheDomain,
	eventPublisher EventPublisher,
	logger *slog.Logger,
) *PasswordOrchestrator {
	return &PasswordOrchestrator{
		passwordDomain:   passwordDomain,
		encryptionDomain: encryptionDomain,
		cacheDomain:      cacheDomain,
		eventPublisher:   eventPublisher,
		logger:           logger,
	}
}

// ChangePasswordOperation 密码变更操作
type ChangePasswordOperation struct {
	OldPassword      string
	NewPassword      string
	ProgressCallback ProgressCallback
	BackupEnabled    bool
	ValidateData     bool
	RollbackOnError  bool
}

// ChangePasswordResult 密码变更结果
type ChangePasswordResult struct {
	Success       bool                    `json:"success"`
	StartTime     time.Time               `json:"start_time"`
	EndTime       time.Time               `json:"end_time"`
	Duration      time.Duration           `json:"duration"`
	StagesCompleted []string              `json:"stages_completed"`
	Error         error                   `json:"error,omitempty"`
	BackupPath    string                  `json:"backup_path,omitempty"`
	Metrics       *ChangePasswordMetrics  `json:"metrics,omitempty"`
}

// ChangePasswordMetrics 密码变更指标
type ChangePasswordMetrics struct {
	RecordsProcessed int           `json:"records_processed"`
	RecordsUpdated   int           `json:"records_updated"`
	RecordsFailed    int           `json:"records_failed"`
	ProcessingTime   time.Duration `json:"processing_time"`
	BackupSize       int64         `json:"backup_size"`
}

// ExecutePasswordChange 执行密码变更
func (o *PasswordOrchestrator) ExecutePasswordChange(operation *ChangePasswordOperation) *ChangePasswordResult {
	result := &ChangePasswordResult{
		StartTime:       time.Now(),
		StagesCompleted: make([]string, 0),
		Metrics:         &ChangePasswordMetrics{},
	}

	o.logger.Info("开始执行密码变更操作")

	// 设置默认进度回调
	if operation.ProgressCallback == nil {
		operation.ProgressCallback = func(stage string, progress float64, message string) {
			o.eventPublisher.PublishProgress(stage, progress, message)
		}
	}

	// 执行变更流程
	if err := o.executeChangeStages(operation, result); err != nil {
		result.Success = false
		result.Error = err
		o.logger.Error("密码变更失败", "error", err)
		
		// 如果启用了回滚，尝试回滚
		if operation.RollbackOnError {
			o.rollbackChanges(operation, result)
		}
	} else {
		result.Success = true
		o.logger.Info("密码变更成功")
	}

	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	return result
}

// executeChangeStages 执行变更阶段
func (o *PasswordOrchestrator) executeChangeStages(operation *ChangePasswordOperation, result *ChangePasswordResult) error {
	stages := []struct {
		name     string
		progress float64
		execute  func() error
	}{
		{"validation", 5.0, func() error { return o.validateOperation(operation) }},
		{"backup", 15.0, func() error { return o.createBackup(operation, result) }},
		{"preparation", 25.0, func() error { return o.prepareForChange(operation) }},
		{"reencryption", 70.0, func() error { return o.reencryptData(operation, result) }},
		{"password_update", 85.0, func() error { return o.updatePassword(operation) }},
		{"cache_update", 90.0, func() error { return o.updateCache(operation) }},
		{"verification", 95.0, func() error { return o.verifyChange(operation) }},
		{"cleanup", 100.0, func() error { return o.cleanup(operation, result) }},
	}

	for _, stage := range stages {
		operation.ProgressCallback(stage.name, stage.progress, fmt.Sprintf("执行阶段: %s", stage.name))
		
		if err := stage.execute(); err != nil {
			return WrapError(ErrCodeValidationFailed, fmt.Sprintf("阶段 %s 失败", stage.name), err)
		}
		
		result.StagesCompleted = append(result.StagesCompleted, stage.name)
		o.logger.Debug("完成阶段", "stage", stage.name)
	}

	return nil
}

// validateOperation 验证操作
func (o *PasswordOrchestrator) validateOperation(operation *ChangePasswordOperation) error {
	return o.passwordDomain.ValidatePasswordChange(operation.OldPassword, operation.NewPassword)
}

// createBackup 创建备份
func (o *PasswordOrchestrator) createBackup(operation *ChangePasswordOperation, result *ChangePasswordResult) error {
	if !operation.BackupEnabled {
		return nil
	}

	// TODO: 实现数据备份逻辑
	// 1. 创建备份目录
	// 2. 导出加密数据
	// 3. 记录备份路径和大小
	
	backupPath := fmt.Sprintf("backup_%d.db", time.Now().Unix())
	result.BackupPath = backupPath
	result.Metrics.BackupSize = 0 // 实际备份大小

	o.logger.Info("创建数据备份", "path", backupPath)
	return nil
}

// prepareForChange 准备变更
func (o *PasswordOrchestrator) prepareForChange(operation *ChangePasswordOperation) error {
	// 1. 验证加密功能
	if err := o.encryptionDomain.ValidateEncryption(); err != nil {
		return WrapError(ErrCodeEncryptionFailed, "加密功能验证失败", err)
	}

	// 2. 检查缓存状态
	if !o.cacheDomain.IsPasswordCached() {
		return NewPasswordError(ErrCodeCacheExpired, "密码缓存已过期，请重新解锁", nil)
	}

	return nil
}

// reencryptData 重新加密数据
func (o *PasswordOrchestrator) reencryptData(operation *ChangePasswordOperation, result *ChangePasswordResult) error {
	startTime := time.Now()

	// 创建进度回调包装器
	progressWrapper := func(stage string, progress float64, message string) {
		// 将重加密进度映射到总进度的25%-70%区间
		totalProgress := 25.0 + (progress/100.0)*45.0
		operation.ProgressCallback("reencryption", totalProgress, message)
	}

	err := o.encryptionDomain.ReencryptData(operation.OldPassword, operation.NewPassword, progressWrapper)
	
	result.Metrics.ProcessingTime = time.Since(startTime)
	
	if err != nil {
		return WrapError(ErrCodeEncryptionFailed, "数据重新加密失败", err)
	}

	return nil
}

// updatePassword 更新密码
func (o *PasswordOrchestrator) updatePassword(operation *ChangePasswordOperation) error {
	return o.passwordDomain.ChangePassword(operation.OldPassword, operation.NewPassword)
}

// updateCache 更新缓存
func (o *PasswordOrchestrator) updateCache(operation *ChangePasswordOperation) error {
	// 清除旧缓存
	o.cacheDomain.ClearCache()
	
	// 设置新密码缓存
	if err := o.cacheDomain.CachePassword(operation.NewPassword); err != nil {
		o.logger.Warn("新密码缓存失败", "error", err)
		// 不阻止操作继续，但记录警告
	}

	// 清除加密器缓存
	o.encryptionDomain.ClearEncryptionCache()

	return nil
}

// verifyChange 验证变更
func (o *PasswordOrchestrator) verifyChange(operation *ChangePasswordOperation) error {
	if !operation.ValidateData {
		return nil
	}

	// 1. 验证新密码
	if err := o.passwordDomain.VerifyPassword(operation.NewPassword); err != nil {
		return WrapError(ErrCodePasswordIncorrect, "新密码验证失败", err)
	}

	// 2. 验证加密功能
	if err := o.encryptionDomain.ValidateEncryption(); err != nil {
		return WrapError(ErrCodeEncryptionFailed, "加密功能验证失败", err)
	}

	// 3. 验证缓存
	if err := o.cacheDomain.ValidateCache(); err != nil {
		o.logger.Warn("缓存验证失败", "error", err)
		// 不阻止操作，但记录警告
	}

	return nil
}

// cleanup 清理
func (o *PasswordOrchestrator) cleanup(operation *ChangePasswordOperation, result *ChangePasswordResult) error {
	// 发布密码变更事件
	o.eventPublisher.PublishPasswordChanged()
	
	// 记录成功日志
	o.logger.Info("密码变更操作完成",
		"duration", result.Duration,
		"stages", len(result.StagesCompleted),
	)

	return nil
}

// rollbackChanges 回滚变更
func (o *PasswordOrchestrator) rollbackChanges(operation *ChangePasswordOperation, result *ChangePasswordResult) {
	o.logger.Warn("开始回滚密码变更")

	// TODO: 实现回滚逻辑
	// 1. 如果有备份，恢复备份
	// 2. 恢复旧密码
	// 3. 清理临时文件
	// 4. 恢复缓存状态

	if result.BackupPath != "" {
		o.logger.Info("恢复数据备份", "path", result.BackupPath)
		// 实现备份恢复逻辑
	}

	// 尝试恢复旧密码缓存
	if err := o.cacheDomain.CachePassword(operation.OldPassword); err != nil {
		o.logger.Error("恢复旧密码缓存失败", "error", err)
	}

	o.logger.Warn("密码变更回滚完成")
}

// GetChangeHistory 获取变更历史
func (o *PasswordOrchestrator) GetChangeHistory() []*ChangePasswordResult {
	// TODO: 实现变更历史记录
	return make([]*ChangePasswordResult, 0)
}

// EstimateChangeTime 估算变更时间
func (o *PasswordOrchestrator) EstimateChangeTime() time.Duration {
	// TODO: 基于数据量和历史记录估算变更时间
	return 5 * time.Minute // 默认估算
}
