package password

import (
	"a8.tools/backend/pkg/password/types"
)

// 重新导出错误类型，保持向后兼容
type (
	PasswordError = types.PasswordError
	ErrorCode     = types.ErrorCode
)

// 重新导出错误代码常量
const (
	ErrCodePasswordNotSet       = types.ErrCodePasswordNotSet
	ErrCodePasswordIncorrect    = types.ErrCodePasswordIncorrect
	ErrCodePasswordWeak         = types.ErrCodePasswordWeak
	ErrCodeSamePassword         = types.ErrCodeSamePassword
	ErrCodeEmptyPassword        = types.ErrCodeEmptyPassword
	ErrCodePasswordTooShort     = types.ErrCodePasswordTooShort
	ErrCodeCacheExpired         = types.ErrCodeCacheExpired
	ErrCodeCacheFailed          = types.ErrCodeCacheFailed
	ErrCodeCacheInvalid         = types.ErrCodeCacheInvalid
	ErrCodeEncryptionFailed     = types.ErrCodeEncryptionFailed
	ErrCodeDecryptionFailed     = types.ErrCodeDecryptionFailed
	ErrCodeKeyDerivationFailed  = types.ErrCodeKeyDerivationFailed
	ErrCodeKeyInvalid           = types.ErrCodeKeyInvalid
	ErrCodeStorageFailed        = types.ErrCodeStorageFailed
	ErrCodeKeyringFailed        = types.ErrCodeKeyringFailed
	ErrCodeRepositoryFailed     = types.ErrCodeRepositoryFailed
	ErrCodeValidationFailed     = types.ErrCodeValidationFailed
	ErrCodeDataIntegrity        = types.ErrCodeDataIntegrity
	ErrCodeInvalidFormat        = types.ErrCodeInvalidFormat
	ErrCodeConfigInvalid        = types.ErrCodeConfigInvalid
	ErrCodeDependencyFailed     = types.ErrCodeDependencyFailed
	ErrCodeInitializationFailed = types.ErrCodeInitializationFailed
	ErrCodeUnknownError         = types.ErrCodeUnknownError
)

// 重新导出函数
var (
	NewPasswordError = types.NewPasswordError
	IsPasswordError  = types.IsPasswordError
	GetErrorCode     = types.GetErrorCode
	WrapError        = types.WrapError
)

// 重新导出预定义错误
var (
	ErrPasswordNotSet    = types.ErrPasswordNotSet
	ErrPasswordIncorrect = types.ErrPasswordIncorrect
	ErrCacheExpired      = types.ErrCacheExpired
	ErrEmptyPassword     = types.ErrEmptyPassword
)
