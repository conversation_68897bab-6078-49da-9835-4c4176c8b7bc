# 重构后密码系统工作流程分析

## 🎯 以"保存密码"为例的完整流程

### 📱 前端调用
```javascript
// 前端 JavaScript 调用
const result = await window.go.password.PasswordService.SetPassword("user-password-123");
```

### 🔄 完整调用链路

```
前端 JavaScript
    ↓ (Wails 绑定)
服务层 (PasswordService)
    ↓ (依赖注入容器)
应用层 (PasswordApplicationService)
    ↓ (协调多个领域服务)
领域层 (PasswordDomain + CacheDomain)
    ↓ (调用基础设施)
提供者层 (EncryptionProvider + EventPublisher)
    ↓ (数据持久化)
仓库层 (KeyringRepository)
    ↓ (系统调用)
操作系统 Keyring
```

## 🏗️ 分层详细分析

### 1. 服务层 (Services Layer)
**文件**: `backend/services/password/password_service.go`

```go
func (s *PasswordService) SetPassword(password string) error {
    // 1. 确保服务已初始化
    if err := s.ensureInitialized(); err != nil {
        return err
    }

    // 2. 验证密码格式
    if err := s.validatePassword(password); err != nil {
        return err
    }

    // 3. 委托给应用服务
    return s.appService.SetPassword(password)
}
```

**职责**:
- ✅ Wails 绑定和前端接口
- ✅ 基本参数验证
- ✅ 错误适配和类型转换
- ✅ 服务初始化检查

### 2. 应用层 (Application Layer)
**文件**: `backend/pkg/password/application.go`

```go
func (s *PasswordApplicationServiceImpl) SetPassword(password string) error {
    s.logger.Info("开始设置应用密码")

    // 1. 调用密码领域服务设置密码
    if err := s.passwordDomain.SetPassword(password); err != nil {
        s.eventPublisher.PublishError(err)
        return err
    }

    // 2. 调用缓存领域服务缓存密码
    if err := s.cacheDomain.CachePassword(password); err != nil {
        // 密码设置成功但缓存失败，记录警告但不返回错误
        s.logger.Warn("密码缓存失败", "error", err)
        s.eventPublisher.PublishError(err)
    }

    // 3. 发布成功事件
    s.eventPublisher.PublishPasswordSet()
    s.logger.Info("应用密码设置成功")

    return nil
}
```

**职责**:
- ✅ 协调多个领域服务
- ✅ 业务流程编排
- ✅ 事务管理和错误处理
- ✅ 事件发布和日志记录

### 3. 领域层 (Domain Layer)

#### 3.1 密码领域服务
**文件**: `backend/pkg/password/domain/password.go`

```go
func (d *PasswordDomain) SetPassword(password string) error {
    // 1. 验证密码强度
    if err := d.validator.ValidateStrength(password); err != nil {
        return types.WrapError(types.ErrCodePasswordWeak, "密码强度不足", err)
    }

    // 2. 验证密码格式
    if err := d.validator.ValidateFormat(password); err != nil {
        return types.WrapError(types.ErrCodeInvalidFormat, "密码格式无效", err)
    }

    // 3. 存储密码到仓库
    if err := d.repo.Store(password); err != nil {
        return types.WrapError(types.ErrCodeStorageFailed, "密码存储失败", err)
    }

    return nil
}
```

#### 3.2 缓存领域服务
**文件**: `backend/pkg/password/domain/cache.go`

```go
func (d *CacheDomain) CachePassword(password string) error {
    if password == "" {
        return types.ErrEmptyPassword
    }

    if err := d.cache.Set(password, d.config.TTL); err != nil {
        return types.WrapError(types.ErrCodeCacheFailed, "密码缓存失败", err)
    }

    return nil
}
```

**职责**:
- ✅ 核心业务逻辑实现
- ✅ 业务规则验证
- ✅ 领域对象管理
- ✅ 业务异常处理

### 4. 提供者层 (Provider Layer)

#### 4.1 密码验证器
**文件**: `backend/pkg/password/domain/validation.go`

```go
func (v *PasswordValidator) ValidateStrength(password string) error {
    result := v.calculatePasswordStrength(password)
    
    if result.Score < v.config.StrengthRequirement.MinScore {
        return types.NewPasswordError(types.ErrCodePasswordWeak, 
            "密码强度不足", nil)
    }
    
    return nil
}
```

#### 4.2 缓存提供者
**文件**: `backend/pkg/password/provider/cache.go`

```go
func (c *MemoryPasswordCache) Set(password string, ttl time.Duration) error {
    c.mu.Lock()
    defer c.mu.Unlock()

    c.password = password
    c.expiresAt = time.Now().Add(ttl)
    c.isValid = true
    c.accessCount++

    return nil
}
```

#### 4.3 事件发布器
**文件**: `backend/pkg/password/provider/events.go`

```go
func (p *WailsEventPublisher) PublishPasswordSet() {
    event := PasswordEvent{
        Type:      EventPasswordSet,
        Timestamp: time.Now(),
        Data: map[string]interface{}{
            "message": "应用密码已设置",
        },
    }

    p.publishEvent(EventPasswordSet, event)
}
```

**职责**:
- ✅ 基础设施服务实现
- ✅ 外部系统集成
- ✅ 技术细节封装
- ✅ 可替换的实现

### 5. 仓库层 (Repository Layer)
**文件**: `backend/pkg/password/repository/keyring.go`

```go
func (r *KeyringRepository) Store(password string) error {
    // 1. 生成随机盐值
    salt := make([]byte, r.config.SaltLength)
    if _, err := rand.Read(salt); err != nil {
        return types.WrapError(types.ErrCodeKeyDerivationFailed, "生成盐值失败", err)
    }

    // 2. 从密码派生密钥加密密钥 (KEK)
    kek := pbkdf2.Key([]byte(password), salt, r.config.Iterations, 
                      r.config.KeyLength, sha256.New)

    // 3. 生成数据加密密钥 (DEK)
    dek := make([]byte, 32)
    if _, err := rand.Read(dek); err != nil {
        return types.WrapError(types.ErrCodeKeyDerivationFailed, "生成DEK失败", err)
    }

    // 4. 使用KEK加密DEK
    encryptedDEK, err := r.encryptDEK(dek, kek)
    if err != nil {
        return types.WrapError(types.ErrCodeEncryptionFailed, "加密DEK失败", err)
    }

    // 5. 创建包装的密钥数据
    wrappedKey := types.WrappedKeyData{
        Salt:         salt,
        EncryptedDEK: encryptedDEK,
        Version:      1,
    }

    // 6. 序列化并存储到系统 Keyring
    data, err := json.Marshal(wrappedKey)
    if err != nil {
        return types.WrapError(types.ErrCodeStorageFailed, "序列化失败", err)
    }

    return keyring.Set(r.serviceName, r.keyName, string(data))
}
```

**职责**:
- ✅ 数据持久化
- ✅ 加密密钥管理
- ✅ 系统 Keyring 集成
- ✅ 数据序列化/反序列化

## 🔧 依赖注入容器的作用

### 容器初始化
**文件**: `backend/pkg/password/container.go`

```go
func (c *Container) initializeComponents() error {
    // 1. 初始化仓库层
    c.passwordRepo = repository.NewKeyringRepository(...)

    // 2. 初始化提供者层
    c.passwordCache = provider.NewMemoryPasswordCache(...)
    c.encryptionProvider = provider.NewEncryptionProvider(...)
    c.eventPublisher = provider.NewMockEventPublisher(...)

    // 3. 初始化验证器
    c.validator = domain.NewPasswordValidator(...)

    // 4. 初始化领域层
    c.passwordDomain = domain.NewPasswordDomain(c.passwordRepo, c.validator, ...)
    c.cacheDomain = domain.NewCacheDomain(c.passwordCache, ...)

    // 5. 初始化应用层
    c.applicationService = NewPasswordApplicationService(
        c.passwordDomain, c.encryptionDomain, c.cacheDomain, 
        c.eventPublisher, c.config, c.logger)

    return nil
}
```

**作用**:
- ✅ 管理组件生命周期
- ✅ 解决依赖关系
- ✅ 配置注入
- ✅ 单例管理

## 📊 数据流向图

```
用户输入密码 "user-password-123"
    ↓
[前端] JavaScript 调用
    ↓ (Wails 绑定)
[服务层] PasswordService.SetPassword()
    ↓ (参数验证)
[应用层] PasswordApplicationService.SetPassword()
    ↓ (业务流程编排)
[领域层] PasswordDomain.SetPassword()
    ↓ (密码验证)
[提供者层] PasswordValidator.ValidateStrength()
    ↓ (验证通过)
[仓库层] KeyringRepository.Store()
    ↓ (密钥派生和加密)
[系统] macOS Keychain 存储
    ↓ (存储成功)
[领域层] CacheDomain.CachePassword()
    ↓ (内存缓存)
[提供者层] EventPublisher.PublishPasswordSet()
    ↓ (事件发布)
[前端] 接收成功响应
```

## 🎯 重构后的优势

### 1. 职责清晰
- **服务层**: 只负责 Wails 绑定和基本验证
- **应用层**: 只负责业务流程编排
- **领域层**: 只负责核心业务逻辑
- **提供者层**: 只负责基础设施服务
- **仓库层**: 只负责数据持久化

### 2. 依赖倒置
- 高层模块不依赖低层模块
- 都依赖于抽象接口
- 便于测试和替换实现

### 3. 可测试性
```go
// 可以轻松模拟任何层的依赖
mockRepo := &MockPasswordRepository{}
mockValidator := &MockPasswordValidator{}
passwordDomain := domain.NewPasswordDomain(mockRepo, mockValidator, config)
```

### 4. 可扩展性
- 新增功能只需在对应层添加
- 不会影响其他层的实现
- 支持插件化扩展

### 5. 错误处理
- 统一的错误类型和错误码
- 错误在每层都有适当的包装
- 便于调试和监控

这种分层架构确保了代码的可维护性、可测试性和可扩展性，是现代软件开发的最佳实践。🎉
