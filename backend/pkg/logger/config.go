package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"a8.tools/backend/utils/file"
)

// Config 日志配置结构
type Config struct {
	// 日志级别
	Level Level

	// 输出目标
	OutputFile    string // 文件输出路径，为空则不输出到文件
	OutputConsole bool   // 是否输出到控制台

	// 日志格式配置
	TimeFormat string // 时间格式，默认为 "2006-01-02 15:04:05.000"

	// 日志轮转配置 (lumberjack.v2)
	MaxSize    int  // 单个日志文件最大大小（MB），默认 100MB
	MaxAge     int  // 日志文件最大保留天数，默认 30天
	MaxBackups int  // 最多保留的历史日志文件数，默认 10个
	Compress   bool // 是否压缩历史日志文件，默认 true
	LocalTime  bool // 是否使用本地时间命名备份文件，默认 false（使用UTC）

	// 异步写入配置
	AsyncWrite bool // 是否启用异步写入，默认 true
	BufferSize int  // 缓冲区大小，默认 1024

	// 调用者信息
	AddSource bool // 是否添加调用者信息（文件名和行号），默认 true
}

// DefaultConfig 返回默认配置
func DefaultConfig() Config {
	return Config{
		Level:         INFO,
		OutputFile:    "",
		OutputConsole: true,
		TimeFormat:    "2006-01-02 15:04:05.000",
		MaxSize:       100,
		MaxAge:        30,
		MaxBackups:    10,
		Compress:      true,
		LocalTime:     false,
		AsyncWrite:    true,
		BufferSize:    1024,
		AddSource:     true,
	}
}

// ConfigOption 配置选项函数类型
type ConfigOption func(*Config)

// WithLevel 设置日志级别
func WithLevel(level Level) ConfigOption {
	return func(c *Config) {
		c.Level = level
	}
}

// WithOutputFile 设置输出文件路径
func WithOutputFile(path string) ConfigOption {
	return func(c *Config) {
		c.OutputFile = path
	}
}

// WithOutputConsole 设置是否输出到控制台
func WithOutputConsole(enabled bool) ConfigOption {
	return func(c *Config) {
		c.OutputConsole = enabled
	}
}

// WithTimeFormat 设置时间格式
func WithTimeFormat(format string) ConfigOption {
	return func(c *Config) {
		c.TimeFormat = format
	}
}

// WithRotation 设置日志轮转参数
func WithRotation(maxSize, maxAge, maxBackups int, compress bool) ConfigOption {
	return func(c *Config) {
		c.MaxSize = maxSize
		c.MaxAge = maxAge
		c.MaxBackups = maxBackups
		c.Compress = compress
	}
}

// WithLocalTime 设置是否使用本地时间命名备份文件
func WithLocalTime(localTime bool) ConfigOption {
	return func(c *Config) {
		c.LocalTime = localTime
	}
}

// WithAsyncWrite 设置异步写入配置
func WithAsyncWrite(enabled bool, bufferSize int) ConfigOption {
	return func(c *Config) {
		c.AsyncWrite = enabled
		c.BufferSize = bufferSize
	}
}

// WithAddSource 设置是否添加调用者信息
func WithAddSource(enabled bool) ConfigOption {
	return func(c *Config) {
		c.AddSource = enabled
	}
}

// Validate 验证配置的有效性
func (c *Config) Validate() error {
	// 验证日志级别
	if c.Level < TRACE || c.Level > FATAL {
		return ErrInvalidLevel
	}

	// 验证输出配置
	if !c.OutputConsole && c.OutputFile == "" {
		return ErrInvalidOutput
	}

	// 验证文件路径
	if c.OutputFile != "" {
		// 确保目录存在
		dir := filepath.Dir(c.OutputFile)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return err
		}
	}

	// 验证轮转配置
	if c.MaxSize <= 0 {
		c.MaxSize = 100
	}
	if c.MaxAge <= 0 {
		c.MaxAge = 30
	}
	if c.MaxBackups <= 0 {
		c.MaxBackups = 10
	}

	// 验证异步写入配置
	if c.BufferSize <= 0 {
		c.BufferSize = 1024
	}

	// 验证时间格式
	if c.TimeFormat == "" {
		c.TimeFormat = "2006-01-02 15:04:05.000"
	}

	// 测试时间格式是否有效
	_, err := time.Parse(c.TimeFormat, time.Now().Format(c.TimeFormat))
	if err != nil {
		return ErrInvalidTimeFormat
	}

	return nil
}

// GetDefaultLogFile 获取默认日志文件路径，支持可选的子目录
// subpath: 可选的子目录路径，如果不为空则在logs目录下创建该子目录
func GetDefaultLogFile(subpath ...string) string {
	logDir, err := file.GetLogDir()
	if err != nil {
		return ""
	}

	// 如果提供了子路径，则创建子目录
	targetDir := logDir
	if len(subpath) > 0 && subpath[0] != "" {
		targetDir = filepath.Join(logDir, subpath[0])
		// 确保子目录存在
		if err := os.MkdirAll(targetDir, 0755); err != nil {
			// 如果创建子目录失败，回退到使用主logs目录
			targetDir = logDir
		}
	}

	// 生成带时间戳的日志文件名
	timestamp := time.Now().Format("20060102_150405")
	logFileName := fmt.Sprintf("%s.log", timestamp)
	return filepath.Join(targetDir, logFileName)
}
