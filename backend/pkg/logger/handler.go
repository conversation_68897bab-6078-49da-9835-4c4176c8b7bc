package logger

import (
	"context"
	"log/slog"
	"path/filepath"
	"runtime"
	"strings"
)

// SourceFixHandler 是一个 slog.Handler 的包装器，用于修正日志记录的调用者源信息。
// 当日志功能被封装在自定义包中时，slog 默认会将调用源记录为日志包内部的代码，
// 而不是真正的业务逻辑调用点。本 Handler 会向上追溯调用栈，找到并记录第一个
// 非日志包内的调用位置。

type SourceFixHandler struct {
	slog.Handler
	logPkgPath string // 日志包的路径，用于识别和跳过
}

// NewSourceFixHandler 创建一个新的 SourceFixHandler。
// 它需要一个底层的 slog.Handler（例如 slog.NewTextHandler 或 slog.NewJSONHandler）
// 和当前日志包的模块路径（例如 "your-module/pkg/logger"）。
func NewSourceFixHandler(h slog.Handler, logPkgPath string) *SourceFixHandler {
	return &SourceFixHandler{
		Handler:    h,
		logPkgPath: logPkgPath,
	}
}

// Handle 处理日志记录，并在需要时修正调用源信息。
func (h *SourceFixHandler) Handle(ctx context.Context, r slog.Record) error {
	// 只有在需要添加源信息时才进行处理
	if r.PC != 0 {
		// 查找真实的调用者
		if pc := h.findRealCaller(r.PC); pc != 0 {
			// 创建一个新的 Record 并替换 PC
			newRecord := slog.NewRecord(r.Time, r.Level, r.Message, pc)
			r.Attrs(func(attr slog.Attr) bool {
				newRecord.AddAttrs(attr)
				return true
			})
			r = newRecord
		}
	}
	return h.Handler.Handle(ctx, r)
}

// findRealCaller 查找真实的调用者，跳过logger包内部的调用
func (h *SourceFixHandler) findRealCaller(originalPC uintptr) uintptr {
	// 获取完整的调用栈
	const maxCallers = 64
	pcs := make([]uintptr, maxCallers)
	// 从调用栈的第1层开始，包含当前函数
	n := runtime.Callers(1, pcs)
	
	if n == 0 {
		return originalPC
	}
	
	// 遍历调用栈，寻找第一个非logger包的调用
	frames := runtime.CallersFrames(pcs[:n])
	for {
		frame, more := frames.Next()
		if !more {
			break
		}
		
		// 跳过runtime包和slog包的调用
		if h.isRuntimeCall(frame.File, frame.Function) {
			continue
		}
		
		// 跳过slog包的调用
		if h.isSlogCall(frame.File, frame.Function) {
			continue
		}
		
		// 跳过logger包内部的调用
		if !h.isLoggerInternalCall(frame.File, frame.Function) {
			return frame.PC
		}
	}
	
	// 如果没找到合适的调用者，返回原始PC
	return originalPC
}

// isRuntimeCall 判断是否是Go运行时内部调用
func (h *SourceFixHandler) isRuntimeCall(file, function string) bool {
	// 检查文件路径是否包含runtime包
	if strings.Contains(filepath.ToSlash(file), "/runtime/") {
		return true
	}
	
	// 检查函数名是否是runtime相关的函数
	runtimeFuncPatterns := []string{
		"runtime.",
		"main.main",
	}
	
	for _, pattern := range runtimeFuncPatterns {
		if strings.Contains(function, pattern) {
			return true
		}
	}
	
	return false
}

// isSlogCall 判断是否是slog包内部调用
func (h *SourceFixHandler) isSlogCall(file, function string) bool {
	// 检查文件路径是否包含slog包
	if strings.Contains(filepath.ToSlash(file), "/log/slog/") {
		return true
	}
	
	// 检查函数名是否是slog相关的函数
	slogFuncPatterns := []string{
		"log/slog.",
		"slog.",
	}
	
	for _, pattern := range slogFuncPatterns {
		if strings.Contains(function, pattern) {
			return true
		}
	}
	
	return false
}

// isLoggerInternalCall 判断是否是logger包内部的调用
func (h *SourceFixHandler) isLoggerInternalCall(file, function string) bool {
	// 检查文件路径是否包含logger包路径
	if strings.Contains(filepath.ToSlash(file), h.logPkgPath) {
		return true
	}
	
	// 检查函数名是否是logger相关的函数
	loggerFuncPatterns := []string{
		"logger.(*Logger).",  // 实例方法
		"logger.Trace",       // 全局函数
		"logger.Debug",
		"logger.Info", 
		"logger.Warn",
		"logger.Error",
		"logger.Fatal",
		"logger.With",
		"logger.WithGroup",
		".log",               // log方法
		".Handle",            // Handler方法
		".findRealCaller",    // 当前方法
	}
	
	for _, pattern := range loggerFuncPatterns {
		if strings.Contains(function, pattern) {
			return true
		}
	}
	
	return false
}

// WithAttrs 为 Handler 添加属性，并返回一个新的 SourceFixHandler 实例。
func (h *SourceFixHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &SourceFixHandler{
		Handler:    h.Handler.WithAttrs(attrs),
		logPkgPath: h.logPkgPath,
	}
}

// WithGroup 为 Handler 添加分组，并返回一个新的 SourceFixHandler 实例。
func (h *SourceFixHandler) WithGroup(name string) slog.Handler {
	return &SourceFixHandler{
		Handler:    h.Handler.WithGroup(name),
		logPkgPath: h.logPkgPath,
	}
}
