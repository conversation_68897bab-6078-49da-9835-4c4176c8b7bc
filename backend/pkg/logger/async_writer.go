package logger

import (
	"fmt"
	"io"
	"sync"
	"time"
)

// AsyncWriter 异步写入器
type AsyncWriter struct {
	writer     io.Writer
	buffer     chan []byte
	bufferSize int
	closed     bool
	mu         sync.RWMutex
	wg         sync.WaitGroup
	done       chan struct{}
	flush<PERSON>han  chan chan error
}

// NewAsyncWriter 创建新的异步写入器
func NewAsyncWriter(writer io.Writer, bufferSize int) *AsyncWriter {
	if bufferSize <= 0 {
		bufferSize = 1024
	}

	aw := &AsyncWriter{
		writer:     writer,
		buffer:     make(chan []byte, bufferSize),
		bufferSize: bufferSize,
		done:       make(chan struct{}),
		flushChan:  make(chan chan error, 1),
	}

	// 启动写入goroutine
	aw.wg.Add(1)
	go aw.writeLoop()

	return aw
}

// Write 实现 io.Writer 接口
func (aw *AsyncWriter) Write(p []byte) (n int, err error) {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	if aw.closed {
		return 0, ErrWriterClosed
	}

	// 创建数据的副本，避免并发修改
	data := make([]byte, len(p))
	copy(data, p)

	// 尝试将数据发送到缓冲区
	select {
	case aw.buffer <- data:
		return len(p), nil
	default:
		// 缓冲区满，使用同步写入作为回退
		return aw.syncWrite(data)
	}
}

// syncWrite 同步写入（当缓冲区满时使用）
func (aw *AsyncWriter) syncWrite(data []byte) (n int, err error) {
	if aw.writer == nil {
		return 0, fmt.Errorf("writer is nil")
	}

	return aw.writer.Write(data)
}

// writeLoop 写入循环goroutine
func (aw *AsyncWriter) writeLoop() {
	defer aw.wg.Done()

	for {
		select {
		case data := <-aw.buffer:
			if len(data) > 0 {
				// 执行实际写入
				if _, err := aw.writer.Write(data); err != nil {
					// 写入失败，记录错误（简化处理）
					fmt.Printf("AsyncWriter: write error: %v\n", err)
				}
			}

		case respChan := <-aw.flushChan:
			// 处理flush请求
			err := aw.flushBuffer()
			respChan <- err

		case <-aw.done:
			// 收到关闭信号，处理剩余的数据
			aw.drainBuffer()
			return
		}
	}
}

// flushBuffer 刷新缓冲区中的所有数据
func (aw *AsyncWriter) flushBuffer() error {
	for {
		select {
		case data := <-aw.buffer:
			if len(data) > 0 {
				if _, err := aw.writer.Write(data); err != nil {
					return err
				}
			}
		default:
			// 缓冲区为空
			return nil
		}
	}
}

// drainBuffer 排空缓冲区（在关闭时使用）
func (aw *AsyncWriter) drainBuffer() {
	for {
		select {
		case data := <-aw.buffer:
			if len(data) > 0 {
				if _, err := aw.writer.Write(data); err != nil {
					fmt.Printf("AsyncWriter: drain error: %v\n", err)
				}
			}
		default:
			// 缓冲区为空
			return
		}
	}
}

// Flush 刷新所有缓冲的数据
func (aw *AsyncWriter) Flush() error {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	if aw.closed {
		return ErrWriterClosed
	}

	// 创建响应channel
	respChan := make(chan error, 1)

	// 发送flush请求
	select {
	case aw.flushChan <- respChan:
		// 等待响应
		select {
		case err := <-respChan:
			return err
		case <-time.After(5 * time.Second):
			return fmt.Errorf("flush timeout")
		}
	case <-time.After(1 * time.Second):
		return fmt.Errorf("flush request timeout")
	}
}

// Close 关闭异步写入器
func (aw *AsyncWriter) Close() error {
	aw.mu.Lock()
	defer aw.mu.Unlock()

	if aw.closed {
		return nil
	}

	aw.closed = true

	// 发送关闭信号
	close(aw.done)

	// 等待写入goroutine完成
	aw.wg.Wait()

	// 关闭channels
	close(aw.buffer)
	close(aw.flushChan)

	// 如果底层writer支持Close，则关闭它
	if closer, ok := aw.writer.(io.Closer); ok {
		return closer.Close()
	}

	return nil
}

// GetStats 获取异步写入器统计信息
func (aw *AsyncWriter) GetStats() map[string]interface{} {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	return map[string]interface{}{
		"buffer_size":     aw.bufferSize,
		"buffer_used":     len(aw.buffer),
		"buffer_capacity": cap(aw.buffer),
		"closed":          aw.closed,
	}
}

// GetBufferUsage 获取缓冲区使用情况
func (aw *AsyncWriter) GetBufferUsage() (used, capacity int) {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	return len(aw.buffer), cap(aw.buffer)
}

// IsBufferFull 检查缓冲区是否已满
func (aw *AsyncWriter) IsBufferFull() bool {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	return len(aw.buffer) >= cap(aw.buffer)
}

// GetBufferSize 获取缓冲区大小
func (aw *AsyncWriter) GetBufferSize() int {
	return aw.bufferSize
}

// IsClosed 检查写入器是否已关闭
func (aw *AsyncWriter) IsClosed() bool {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	return aw.closed
}

// SetBufferSize 设置缓冲区大小（需要重新创建）
func (aw *AsyncWriter) SetBufferSize(size int) error {
	aw.mu.Lock()
	defer aw.mu.Unlock()

	if aw.closed {
		return ErrWriterClosed
	}

	if size <= 0 {
		return fmt.Errorf("buffer size must be positive")
	}

	// 简化实现：不支持动态调整缓冲区大小
	return fmt.Errorf("buffer size cannot be changed after creation")
}

// GetUnderlyingWriter 获取底层writer
func (aw *AsyncWriter) GetUnderlyingWriter() io.Writer {
	return aw.writer
}

// WriteString 写入字符串（便利方法）
func (aw *AsyncWriter) WriteString(s string) (n int, err error) {
	return aw.Write([]byte(s))
}

// WriteByte 写入单个字节（便利方法）
func (aw *AsyncWriter) WriteByte(b byte) error {
	_, err := aw.Write([]byte{b})
	return err
}

// WriteRune 写入单个rune（便利方法）
func (aw *AsyncWriter) WriteRune(r rune) (n int, err error) {
	return aw.Write([]byte(string(r)))
}

// FlushWithTimeout 带超时的刷新
func (aw *AsyncWriter) FlushWithTimeout(timeout time.Duration) error {
	aw.mu.RLock()
	defer aw.mu.RUnlock()

	if aw.closed {
		return ErrWriterClosed
	}

	// 创建响应channel
	respChan := make(chan error, 1)

	// 发送flush请求
	select {
	case aw.flushChan <- respChan:
		// 等待响应
		select {
		case err := <-respChan:
			return err
		case <-time.After(timeout):
			return fmt.Errorf("flush timeout after %v", timeout)
		}
	case <-time.After(timeout):
		return fmt.Errorf("flush request timeout after %v", timeout)
	}
}

// MultiAsyncWriter 多个异步写入器的组合
type MultiAsyncWriter struct {
	writers []io.Writer
	mu      sync.RWMutex
}

// NewMultiAsyncWriter 创建多个异步写入器的组合
func NewMultiAsyncWriter(writers ...io.Writer) *MultiAsyncWriter {
	return &MultiAsyncWriter{
		writers: writers,
	}
}

// Write 实现 io.Writer 接口
func (maw *MultiAsyncWriter) Write(p []byte) (n int, err error) {
	maw.mu.RLock()
	defer maw.mu.RUnlock()

	for _, writer := range maw.writers {
		if _, err := writer.Write(p); err != nil {
			return 0, err
		}
	}

	return len(p), nil
}

// Close 关闭所有写入器
func (maw *MultiAsyncWriter) Close() error {
	maw.mu.Lock()
	defer maw.mu.Unlock()

	var lastErr error
	for _, writer := range maw.writers {
		if closer, ok := writer.(io.Closer); ok {
			if err := closer.Close(); err != nil {
				lastErr = err
			}
		}
	}

	return lastErr
}

// AddWriter 添加写入器
func (maw *MultiAsyncWriter) AddWriter(writer io.Writer) {
	maw.mu.Lock()
	defer maw.mu.Unlock()

	maw.writers = append(maw.writers, writer)
}

// RemoveWriter 移除写入器
func (maw *MultiAsyncWriter) RemoveWriter(writer io.Writer) {
	maw.mu.Lock()
	defer maw.mu.Unlock()

	for i, w := range maw.writers {
		if w == writer {
			maw.writers = append(maw.writers[:i], maw.writers[i+1:]...)
			break
		}
	}
}

// GetWriters 获取所有写入器
func (maw *MultiAsyncWriter) GetWriters() []io.Writer {
	maw.mu.RLock()
	defer maw.mu.RUnlock()

	// 返回副本
	writers := make([]io.Writer, len(maw.writers))
	copy(writers, maw.writers)
	return writers
}
