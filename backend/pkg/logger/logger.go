package logger

import (
	"context"
	"fmt"
	"io"
	"log/slog"
	"os"
	"sync"
	"time"

	"gopkg.in/natefinch/lumberjack.v2"
)

// Logger 主要的日志记录器结构
type Logger struct {
	config      Config
	slogger     *slog.Logger
	lumberjack  *lumberjack.Logger
	asyncWriter *AsyncWriter
	mu          sync.RWMutex
	closed      bool
}

// 全局默认日志器
var (
	defaultLogger *Logger
	defaultMu     sync.RWMutex
)

// New 创建一个新的 Logger 实例
func New(config Config, options ...ConfigOption) (*Logger, error) {
	// 应用配置选项
	for _, option := range options {
		option(&config)
	}

	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("invalid config: %w", err)
	}

	logger := &Logger{
		config: config,
	}

	// 设置输出writers
	var writers []io.Writer

	// 控制台输出
	if config.OutputConsole {
		writers = append(writers, os.Stdout)
	}

	// 文件输出
	if config.OutputFile != "" {
		// 初始化lumberjack日志轮转器
		lumberjackLogger := &lumberjack.Logger{
			Filename:   config.OutputFile,
			MaxSize:    config.MaxSize,
			MaxAge:     config.MaxAge,
			MaxBackups: config.MaxBackups,
			Compress:   config.Compress,
			LocalTime:  config.LocalTime,
		}
		logger.lumberjack = lumberjackLogger

		// 根据是否启用异步写入来决定writer
		if config.AsyncWrite {
			asyncWriter := NewAsyncWriter(lumberjackLogger, config.BufferSize)
			logger.asyncWriter = asyncWriter
			writers = append(writers, asyncWriter)
		} else {
			writers = append(writers, lumberjackLogger)
		}
	}

	// 创建多writer
	var output io.Writer
	if len(writers) == 1 {
		output = writers[0]
	} else {
		output = io.MultiWriter(writers...)
	}

	// 创建自定义handler
	handlerOpts := &slog.HandlerOptions{
		Level:       config.Level.ToSlogLevel(),
		AddSource:   config.AddSource,
		ReplaceAttr: logger.replaceAttr,
	}

	// 使用TextHandler作为基础
	baseHandler := slog.NewTextHandler(output, handlerOpts)
	
	// 如果启用了AddSource，使用SourceFixHandler来修正调用者信息
	var handler slog.Handler = baseHandler
	if config.AddSource {
		// 使用当前包路径作为logger包路径
		handler = NewSourceFixHandler(baseHandler, "a8.tools/backend/pkg/logger")
	}

	// 创建slog.Logger
	logger.slogger = slog.New(handler)

	return logger, nil
}

// SetDefault 设置默认日志器
func SetDefault(logger *Logger) {
	defaultMu.Lock()
	defer defaultMu.Unlock()
	defaultLogger = logger
}

// Default 获取默认日志器
func Default() *Logger {
	defaultMu.RLock()
	defer defaultMu.RUnlock()
	return defaultLogger
}

// InitDefault 初始化默认日志器
func InitDefault(config Config, options ...ConfigOption) error {
	logger, err := New(config, options...)
	if err != nil {
		return err
	}
	SetDefault(logger)
	return nil
}

// replaceAttr 自定义属性替换函数
func (l *Logger) replaceAttr(groups []string, a slog.Attr) slog.Attr {
	// 自定义时间格式
	if a.Key == slog.TimeKey {
		if t, ok := a.Value.Any().(time.Time); ok {
			return slog.String(slog.TimeKey, t.Format(l.config.TimeFormat))
		}
	}

	// 自定义级别显示
	if a.Key == slog.LevelKey {
		if level, ok := a.Value.Any().(slog.Level); ok {
			customLevel := FromSlogLevel(level)
			return slog.String(slog.LevelKey, customLevel.String())
		}
	}

	return a
}

// Trace 记录 TRACE 级别日志
func (l *Logger) Trace(msg string, args ...any) {
	l.log(TRACE, msg, args...)
}

// Debug 记录 DEBUG 级别日志
func (l *Logger) Debug(msg string, args ...any) {
	l.log(DEBUG, msg, args...)
}

// Info 记录 INFO 级别日志
func (l *Logger) Info(msg string, args ...any) {
	l.log(INFO, msg, args...)
}

// Warn 记录 WARN 级别日志
func (l *Logger) Warn(msg string, args ...any) {
	l.log(WARN, msg, args...)
}

// Error 记录 ERROR 级别日志
func (l *Logger) Error(msg string, args ...any) {
	l.log(ERROR, msg, args...)
}

// Fatal 记录 FATAL 级别日志并退出程序
func (l *Logger) Fatal(msg string, args ...any) {
	l.log(FATAL, msg, args...)
	l.Close()
	os.Exit(1)
}

// log 内部日志记录方法
func (l *Logger) log(level Level, msg string, args ...any) {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.closed {
		return
	}

	// 检查级别是否启用
	if !l.config.Level.IsEnabled(level) {
		return
	}

	// 使用slog记录日志，调用者信息由SourceFixHandler处理
	ctx := context.Background()

	// 使用slog记录日志
	l.slogger.Log(ctx, level.ToSlogLevel(), msg, args...)
}

// With 创建一个带有预设字段的子logger
func (l *Logger) With(args ...any) *Logger {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.closed {
		return l
	}

	newLogger := &Logger{
		config:      l.config,
		slogger:     l.slogger.With(args...),
		lumberjack:  l.lumberjack,
		asyncWriter: l.asyncWriter,
	}

	return newLogger
}

// WithGroup 创建一个带有组名的子logger
func (l *Logger) WithGroup(name string) *Logger {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.closed {
		return l
	}

	newLogger := &Logger{
		config:      l.config,
		slogger:     l.slogger.WithGroup(name),
		lumberjack:  l.lumberjack,
		asyncWriter: l.asyncWriter,
	}

	return newLogger
}

// SetLevel 动态设置日志级别
func (l *Logger) SetLevel(level Level) {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.closed {
		return
	}

	l.config.Level = level

	// 重新创建handler以更新级别
	// 注意：这是一个简化实现，实际使用中可能需要更复杂的逻辑
}

// GetLevel 获取当前日志级别
func (l *Logger) GetLevel() Level {
	l.mu.RLock()
	defer l.mu.RUnlock()
	return l.config.Level
}

// IsEnabled 检查指定级别是否启用
func (l *Logger) IsEnabled(level Level) bool {
	l.mu.RLock()
	defer l.mu.RUnlock()
	return l.config.Level.IsEnabled(level)
}

// Close 关闭日志器，刷新所有缓冲的日志
func (l *Logger) Close() error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if l.closed {
		return nil
	}

	l.closed = true

	// 关闭异步写入器
	if l.asyncWriter != nil {
		if err := l.asyncWriter.Close(); err != nil {
			return fmt.Errorf("failed to close async writer: %w", err)
		}
	}

	// 关闭lumberjack轮转器
	if l.lumberjack != nil {
		if err := l.lumberjack.Close(); err != nil {
			return fmt.Errorf("failed to close lumberjack: %w", err)
		}
	}

	return nil
}

// Flush 刷新所有缓冲的日志
func (l *Logger) Flush() error {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.closed {
		return ErrWriterClosed
	}

	if l.asyncWriter != nil {
		return l.asyncWriter.Flush()
	}

	return nil
}

// GetConfig 获取当前配置
func (l *Logger) GetConfig() Config {
	l.mu.RLock()
	defer l.mu.RUnlock()
	return l.config
}

// Rotate 手动触发日志轮转
func (l *Logger) Rotate() error {
	l.mu.RLock()
	defer l.mu.RUnlock()

	if l.closed {
		return ErrWriterClosed
	}

	if l.lumberjack != nil {
		return l.lumberjack.Rotate()
	}

	return nil
}

// GetLogStats 获取日志统计信息
func (l *Logger) GetLogStats() map[string]interface{} {
	l.mu.RLock()
	defer l.mu.RUnlock()

	stats := map[string]interface{}{
		"level":          l.config.Level.String(),
		"output_console": l.config.OutputConsole,
		"output_file":    l.config.OutputFile,
		"async_write":    l.config.AsyncWrite,
		"add_source":     l.config.AddSource,
		"closed":         l.closed,
	}

	if l.lumberjack != nil {
		stats["max_size"] = l.lumberjack.MaxSize
		stats["max_age"] = l.lumberjack.MaxAge
		stats["max_backups"] = l.lumberjack.MaxBackups
		stats["compress"] = l.lumberjack.Compress
		stats["local_time"] = l.lumberjack.LocalTime
	}

	return stats
}

// 全局便利函数，使用默认日志器
func Trace(msg string, args ...any) {
	if defaultLogger != nil {
		defaultLogger.Trace(msg, args...)
	}
}

func Debug(msg string, args ...any) {
	if defaultLogger != nil {
		defaultLogger.Debug(msg, args...)
	}
}

func Info(msg string, args ...any) {
	if defaultLogger != nil {
		defaultLogger.Info(msg, args...)
	}
}

func Warn(msg string, args ...any) {
	if defaultLogger != nil {
		defaultLogger.Warn(msg, args...)
	}
}

func Error(msg string, args ...any) {
	if defaultLogger != nil {
		defaultLogger.Error(msg, args...)
	}
}

func Fatal(msg string, args ...any) {
	if defaultLogger != nil {
		defaultLogger.Fatal(msg, args...)
	}
	os.Exit(1)
}

// With 使用默认日志器创建子logger
func With(args ...any) *Logger {
	if defaultLogger != nil {
		return defaultLogger.With(args...)
	}
	return nil
}

// WithGroup 使用默认日志器创建带组名的子logger
func WithGroup(name string) *Logger {
	if defaultLogger != nil {
		return defaultLogger.WithGroup(name)
	}
	return nil
}

// SetLevel 设置默认日志器的级别
func SetLevel(level Level) {
	if defaultLogger != nil {
		defaultLogger.SetLevel(level)
	}
}

// GetLevel 获取默认日志器的级别
func GetLevel() Level {
	if defaultLogger != nil {
		return defaultLogger.GetLevel()
	}
	return INFO
}

// IsEnabled 检查默认日志器的级别是否启用
func IsEnabled(level Level) bool {
	if defaultLogger != nil {
		return defaultLogger.IsEnabled(level)
	}
	return false
}

// Close 关闭默认日志器
func Close() error {
	if defaultLogger != nil {
		return defaultLogger.Close()
	}
	return nil
}

// Flush 刷新默认日志器
func Flush() error {
	if defaultLogger != nil {
		return defaultLogger.Flush()
	}
	return nil
}

// Rotate 手动触发默认日志器轮转
func Rotate() error {
	if defaultLogger != nil {
		return defaultLogger.Rotate()
	}
	return nil
}

// GetLogStats 获取默认日志器统计信息
func GetLogStats() map[string]interface{} {
	if defaultLogger != nil {
		return defaultLogger.GetLogStats()
	}
	return map[string]interface{}{"error": "no default logger"}
}
