package logger

import (
	"errors"
	"fmt"
	"log/slog"
	"strings"
)

// Level 日志级别类型
type Level int

// 定义六个日志级别
const (
	TRACE Level = -8 // -8
	DEBUG Level = -4 // -4
	INFO  Level = 0  // 0
	WARN  Level = 4  // 4
	ERROR Level = 8  // 8
	FATAL Level = 12 // 12
)

// 日志级别名称映射
var levelNames = map[Level]string{
	TRACE: "TRACE",
	DEBUG: "DEBUG",
	INFO:  "INFO",
	WARN:  "WARN",
	ERROR: "ERROR",
	FATAL: "FATAL",
}

// 字符串到级别的映射
var stringToLevel = map[string]Level{
	"TRACE": TRACE,
	"DEBUG": DEBUG,
	"INFO":  INFO,
	"WARN":  WARN,
	"ERROR": ERROR,
	"FATAL": FATAL,
}

// String 返回日志级别的字符串表示
func (l Level) String() string {
	if name, ok := levelNames[l]; ok {
		return name
	}
	return fmt.Sprintf("LEVEL(%d)", int(l))
}

// ParseLevel 从字符串解析日志级别
func ParseLevel(s string) (Level, error) {
	if level, ok := stringToLevel[strings.ToUpper(s)]; ok {
		return level, nil
	}
	return INFO, fmt.Errorf("unknown log level: %s", s)
}

// ToSlogLevel 将自定义级别转换为 slog.Level
func (l Level) ToSlogLevel() slog.Level {
	return slog.Level(l)
}

// FromSlogLevel 从 slog.Level 转换为自定义级别
func FromSlogLevel(level slog.Level) Level {
	return Level(level)
}

// IsEnabled 检查给定级别是否在当前级别下被启用
func (l Level) IsEnabled(level Level) bool {
	return level >= l
}

// 预定义错误
var (
	ErrInvalidLevel      = errors.New("invalid log level")
	ErrInvalidOutput     = errors.New("invalid output configuration: must enable console or file output")
	ErrInvalidTimeFormat = errors.New("invalid time format")
	ErrLoggerNotInit     = errors.New("logger not initialized")
	ErrWriterClosed      = errors.New("writer is closed")
)

// LevelAttr 创建一个日志级别属性
func LevelAttr(level Level) slog.Attr {
	return slog.String("level", level.String())
}

// 颜色常量（用于控制台输出）
const (
	colorReset  = "\033[0m"
	colorRed    = "\033[31m"
	colorYellow = "\033[33m"
	colorBlue   = "\033[34m"
	colorGray   = "\033[37m"
	colorCyan   = "\033[36m"
)

// GetLevelColor 获取日志级别对应的颜色
func GetLevelColor(level Level) string {
	switch level {
	case TRACE:
		return colorGray
	case DEBUG:
		return colorCyan
	case INFO:
		return colorBlue
	case WARN:
		return colorYellow
	case ERROR:
		return colorRed
	case FATAL:
		return colorRed
	default:
		return colorReset
	}
}

// GetAllLevels 获取所有可用的日志级别
func GetAllLevels() []Level {
	return []Level{TRACE, DEBUG, INFO, WARN, ERROR, FATAL}
}

// GetLevelNames 获取所有级别名称
func GetLevelNames() []string {
	levels := GetAllLevels()
	names := make([]string, len(levels))
	for i, level := range levels {
		names[i] = level.String()
	}
	return names
}

// IsValidLevel 检查是否为有效的日志级别
func IsValidLevel(level Level) bool {
	_, ok := levelNames[level]
	return ok
}

// GetDefaultLevel 获取默认日志级别
func GetDefaultLevel() Level {
	return INFO
}

// GetMinLevel 获取最小日志级别
func GetMinLevel() Level {
	return TRACE
}

// GetMaxLevel 获取最大日志级别
func GetMaxLevel() Level {
	return FATAL
}

// CompareLevels 比较两个日志级别
// 返回值：-1 表示 a < b，0 表示 a == b，1 表示 a > b
func CompareLevels(a, b Level) int {
	if a < b {
		return -1
	} else if a > b {
		return 1
	}
	return 0
}
