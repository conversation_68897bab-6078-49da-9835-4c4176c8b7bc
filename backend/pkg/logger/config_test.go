package logger

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetDefaultLogFile(t *testing.T) {
	// 测试不带子路径的情况
	t.Run("WithoutSubpath", func(t *testing.T) {
		logFile := GetDefaultLogFile()
		assert.NotEmpty(t, logFile)

		// 验证文件名格式
		fileName := filepath.Base(logFile)
		assert.True(t, strings.HasPrefix(fileName, "app_"))
		assert.True(t, strings.HasSuffix(fileName, ".log"))

		// 验证时间戳格式 (20060102_150405)
		timestamp := strings.TrimPrefix(fileName, "app_")
		timestamp = strings.TrimSuffix(timestamp, ".log")
		_, err := time.Parse("20060102_150405", timestamp)
		assert.NoError(t, err, "时间戳格式应该正确")
	})

	// 测试带空字符串子路径的情况
	t.Run("WithEmptySubpath", func(t *testing.T) {
		logFile1 := GetDefaultLogFile()
		logFile2 := GetDefaultLogFile("")

		// 两个调用应该返回相同的目录结构
		assert.Equal(t, filepath.Dir(logFile1), filepath.Dir(logFile2))
	})

	// 测试带有效子路径的情况
	t.Run("WithValidSubpath", func(t *testing.T) {
		subpath := "database"
		logFile := GetDefaultLogFile(subpath)
		assert.NotEmpty(t, logFile)

		// 验证路径包含子目录
		assert.Contains(t, logFile, subpath)

		// 验证子目录确实被创建
		subDir := filepath.Dir(logFile)
		_, err := os.Stat(subDir)
		assert.NoError(t, err, "子目录应该被创建")

		// 验证文件名格式
		fileName := filepath.Base(logFile)
		assert.True(t, strings.HasPrefix(fileName, "app_"))
		assert.True(t, strings.HasSuffix(fileName, ".log"))

		// 清理测试创建的目录
		defer func() {
			os.RemoveAll(subDir)
		}()
	})

	// 测试嵌套子路径
	t.Run("WithNestedSubpath", func(t *testing.T) {
		subpath := "modules/database"
		logFile := GetDefaultLogFile(subpath)
		assert.NotEmpty(t, logFile)

		// 验证路径包含嵌套子目录
		assert.Contains(t, logFile, "modules")
		assert.Contains(t, logFile, "database")

		// 验证嵌套目录确实被创建
		subDir := filepath.Dir(logFile)
		_, err := os.Stat(subDir)
		assert.NoError(t, err, "嵌套子目录应该被创建")

		// 清理测试创建的目录
		defer func() {
			// 清理整个modules目录
			modulesDir := filepath.Join(filepath.Dir(subDir), "../modules")
			os.RemoveAll(modulesDir)
		}()
	})

	// 测试多个参数（只使用第一个）
	t.Run("WithMultipleParams", func(t *testing.T) {
		logFile := GetDefaultLogFile("first", "second", "third")
		assert.NotEmpty(t, logFile)

		// 应该只使用第一个参数
		assert.Contains(t, logFile, "first")
		assert.NotContains(t, logFile, "second")
		assert.NotContains(t, logFile, "third")

		// 清理
		defer func() {
			subDir := filepath.Dir(logFile)
			os.RemoveAll(subDir)
		}()
	})

	// 测试特殊字符的子路径
	t.Run("WithSpecialCharacters", func(t *testing.T) {
		subpath := "test-dir_2025"
		logFile := GetDefaultLogFile(subpath)
		assert.NotEmpty(t, logFile)

		// 验证特殊字符被正确处理
		assert.Contains(t, logFile, subpath)

		// 清理
		defer func() {
			subDir := filepath.Dir(logFile)
			os.RemoveAll(subDir)
		}()
	})
}

func TestGetDefaultLogFileTimestamp(t *testing.T) {
	// 测试时间戳的一致性
	t.Run("TimestampConsistency", func(t *testing.T) {
		// 在很短的时间内调用多次，时间戳应该相同或相近
		logFile1 := GetDefaultLogFile("test1")
		logFile2 := GetDefaultLogFile("test2")

		fileName1 := filepath.Base(logFile1)
		fileName2 := filepath.Base(logFile2)

		// 提取时间戳
		timestamp1 := strings.TrimPrefix(fileName1, "app_")
		timestamp1 = strings.TrimSuffix(timestamp1, ".log")

		timestamp2 := strings.TrimPrefix(fileName2, "app_")
		timestamp2 = strings.TrimSuffix(timestamp2, ".log")

		// 在短时间内，时间戳应该相同
		assert.Equal(t, timestamp1, timestamp2, "短时间内调用的时间戳应该相同")

		// 清理
		defer func() {
			os.RemoveAll(filepath.Dir(logFile1))
			os.RemoveAll(filepath.Dir(logFile2))
		}()
	})
}

func TestGetDefaultLogFileErrorHandling(t *testing.T) {
	// 这个测试比较难模拟，因为 file.GetLogDir() 的错误情况不容易触发
	// 但我们可以测试函数在各种输入下不会崩溃
	t.Run("NoErrorWithVariousInputs", func(t *testing.T) {
		testCases := []string{
			"",
			"simple",
			"nested/path",
			"with spaces",
			"with-dashes",
			"with_underscores",
			"MixedCase",
			"123numbers",
		}

		for _, testCase := range testCases {
			t.Run("subpath_"+testCase, func(t *testing.T) {
				// 函数不应该崩溃
				logFile := GetDefaultLogFile(testCase)
				if testCase == "" {
					// 空字符串应该返回默认行为
					assert.NotEmpty(t, logFile)
				} else {
					// 非空字符串应该包含在路径中或返回有效路径
					assert.NotEmpty(t, logFile)
					if testCase != "" {
						// 清理可能创建的目录
						defer func() {
							if logFile != "" {
								subDir := filepath.Dir(logFile)
								os.RemoveAll(subDir)
							}
						}()
					}
				}
			})
		}
	})
}

func TestGetDefaultLogFileDirectoryCreation(t *testing.T) {
	t.Run("DirectoryCreationPermissions", func(t *testing.T) {
		subpath := "permission_test"
		logFile := GetDefaultLogFile(subpath)
		require.NotEmpty(t, logFile)

		subDir := filepath.Dir(logFile)

		// 验证目录被创建
		info, err := os.Stat(subDir)
		require.NoError(t, err)
		require.True(t, info.IsDir())

		// 验证目录权限（0755）
		mode := info.Mode()
		expectedPerm := os.FileMode(0755)
		assert.Equal(t, expectedPerm, mode.Perm(), "目录权限应该是0755")

		// 清理
		defer func() {
			os.RemoveAll(subDir)
		}()
	})
}
