package logger

import (
	"fmt"
	"log/slog"
)

// ExampleGetDefaultLogFile 演示 GetDefaultLogFile 函数的各种用法
func ExampleGetDefaultLogFile() {
	// 1. 基本用法：不带子路径
	basicLogFile := GetDefaultLogFile()
	fmt.Printf("基本日志文件路径: %s\n", basicLogFile)

	// 2. 使用子目录：为数据库日志创建专门的子目录
	dbLogFile := GetDefaultLogFile("database")
	fmt.Printf("数据库日志文件路径: %s\n", dbLogFile)

	// 3. 使用嵌套子目录：为模块创建分层的日志目录
	moduleLogFile := GetDefaultLogFile("modules/proxy")
	fmt.Printf("代理模块日志文件路径: %s\n", moduleLogFile)

	// 4. 为不同服务创建专门的日志目录
	authLogFile := GetDefaultLogFile("services/auth")
	fmt.Printf("认证服务日志文件路径: %s\n", authLogFile)

	// 5. 空字符串参数等同于不传参数
	defaultLogFile := GetDefaultLogFile("")
	fmt.Printf("默认日志文件路径: %s\n", defaultLogFile)
}

// ExampleCreateModuleLogger 演示如何为特定模块创建专门的 logger
func ExampleCreateModuleLogger() {
	// 为数据库模块创建专门的 logger
	dbLogFile := GetDefaultLogFile("database")
	dbConfig := DefaultConfig()
	dbConfig.OutputFile = dbLogFile
	dbConfig.OutputConsole = false // 只输出到文件
	dbConfig.Level = DEBUG

	dbLogger, err := New(dbConfig)
	if err != nil {
		fmt.Printf("创建数据库 logger 失败: %v\n", err)
		return
	}
	defer dbLogger.Close()

	// 使用数据库 logger
	dbLogger.Info("数据库模块日志",
		slog.String("operation", "connect"),
		slog.String("database", "app.db"),
	)

	// 为 API 模块创建专门的 logger
	apiLogFile := GetDefaultLogFile("api")
	apiConfig := DefaultConfig()
	apiConfig.OutputFile = apiLogFile
	apiConfig.OutputConsole = true // 同时输出到控制台和文件
	apiConfig.Level = INFO

	apiLogger, err := New(apiConfig)
	if err != nil {
		fmt.Printf("创建 API logger 失败: %v\n", err)
		return
	}
	defer apiLogger.Close()

	// 使用 API logger
	apiLogger.Info("API 模块日志",
		slog.String("method", "POST"),
		slog.String("endpoint", "/api/users"),
		slog.Int("status", 200),
	)

	fmt.Println("模块化 logger 创建完成")
}

// ExampleLogDirectoryStructure 演示推荐的日志目录结构
func ExampleLogDirectoryStructure() {
	// 推荐的日志目录结构示例
	logPaths := map[string]string{
		"应用主日志":  GetDefaultLogFile(),
		"数据库日志":  GetDefaultLogFile("database"),
		"认证服务日志": GetDefaultLogFile("services/auth"),
		"代理服务日志": GetDefaultLogFile("services/proxy"),
		"前端模块日志": GetDefaultLogFile("modules/frontend"),
		"后端模块日志": GetDefaultLogFile("modules/backend"),
		"错误日志":   GetDefaultLogFile("errors"),
		"性能日志":   GetDefaultLogFile("performance"),
		"安全审计日志": GetDefaultLogFile("security/audit"),
		"开发调试日志": GetDefaultLogFile("debug"),
	}

	fmt.Println("推荐的日志目录结构:")
	for description, path := range logPaths {
		fmt.Printf("  %s: %s\n", description, path)
	}
}

// CreateLoggerForModule 为指定模块创建专门的 logger（实用函数）
func CreateLoggerForModule(moduleName string, level Level, outputConsole bool) (*Logger, error) {
	logFile := GetDefaultLogFile(moduleName)

	config := DefaultConfig()
	config.OutputFile = logFile
	config.OutputConsole = outputConsole
	config.Level = level
	config.AsyncWrite = true

	return New(config)
}

// CreateLoggerForService 为指定服务创建专门的 logger（实用函数）
func CreateLoggerForService(serviceName string) (*Logger, error) {
	return CreateLoggerForModule("services/"+serviceName, INFO, true)
}
