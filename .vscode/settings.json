{
    "go.formatTool": "goimports",
    // go-specific settings
    "[go]": {
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
        "source.organizeImports": "always"
      },
      "editor.defaultFormatter": "golang.go",
    },
    "[go.mod]": {
      "editor.formatOnSave": true,
      "editor.codeActionsOnSave": {
        "source.organizeImports": "always"
      },
      "editor.defaultFormatter": "golang.go",
    },
    "[javascript]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "editor.formatOnSave": true,
      "prettier.configPath": "frontend/prettier.config.mjs",
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
      }
    },
    "[javascriptreact]": {
      "editor.defaultFormatter": "esbenp.prettier-vscode",
      "editor.formatOnSave": true,
      "prettier.configPath": "frontend/prettier.config.mjs",
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
      }
    },
    "eslint.workingDirectories": [
      "frontend"
    ],
    // i18n Ally 配置
    "i18n-ally.localesPaths": [
      "frontend/src/locales/langs"
    ],
    "i18n-ally.keystyle": "nested",
    "i18n-ally.defaultNamespace": "common",
    "i18n-ally.enabledParsers": ["json"],
    "i18n-ally.sourceLanguage": "en",
    "i18n-ally.displayLanguage": "cn",
    "i18n-ally.enabledFrameworks": ["react", "i18next"],
    "i18n-ally.pathMatcher": "{locale}/{namespace}.json",
    "i18n-ally.namespace": true,
    "i18n-ally.sortKeys": true,
    "i18n-ally.keepFulfilled": true,
    "i18n-ally.extract.autoDetect": true,
    "i18n-ally.extract.keygenStyle": "camelCase"
  }
  