#!/bin/bash
# save as: create-worktree.sh

if [ $# -eq 0 ]; then
    echo "Usage: $0 <branch-name> [base-branch]"
    exit 1
fi


BRANCH_NAME=$1
BASE_BRANCH=${2:-main}
REPO_NAME=$(basename $(git rev-parse --show-toplevel))
WORKTREE_PATH="../worktree/${REPO_NAME}-${BRANCH_NAME}"


# Create worktree
git worktree add -b "$BRANCH_NAME" "$WORKTREE_PATH" "$BASE_BRANCH"

# Setup environment
cd "$WORKTREE_PATH"


echo "Worktree created at: $WORKTREE_PATH"
echo "Ready for Claude Code!"