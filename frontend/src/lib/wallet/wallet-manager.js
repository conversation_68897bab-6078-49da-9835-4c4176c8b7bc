import { bip39 } from '@okxweb3/crypto-lib';

import {
  InvalidMnemonicError,
  MissingParameterError,
  UnsupportedCoinTypeError,
  PrivateKeyGenerationError,
} from './wallet-errors.js';
import logger from '../tools/logger.js';
import { createCache } from '../tools/cache.js';
import walletFactory from './wallet-factory.js';
import { SUPPORTED_COINTYPES } from '../../global-config.js';
import { processItems, processIndexTasks } from '../tools/parallel.js';

// 创建钱包缓存实例
const walletCache = createCache({
  namespace: 'wallet',
  expirationTime: 30 * 60 * 1000, // 30分钟
});

/**
 * 根据 coinType 懒加载并获取对应的 Wallet 对象
 * @param {string} coinType
 * @returns {Wallet | null}
 */
async function _getWalletByCoinType(coinType) {
  if (!coinType) return null;

  const cacheKey = coinType.toLowerCase();

  const cachedWallet = walletCache.get(cacheKey);
  if (cachedWallet) return cachedWallet;

  try {
    // 使用工厂创建钱包
    const wallet = await walletFactory.createWallet(cacheKey);
    walletCache.set(cacheKey, wallet);
    return wallet;
  } catch (error) {
    logger.error(`加载 ${coinType} 钱包失败: ${error.message}`);
    return null;
  }
}

/**
 * 获取支持的所有CoinType
 * @returns {string[]} - 支持的CoinType列表
 */
export function getSupportedCoinTypes() {
  return SUPPORTED_COINTYPES.map((coinType) => coinType.value);
}

/**
 * 判断指定的 CoinType 是否被支持
 * @param {string} coinType - 要检查的 CoinType
 * @returns {boolean} - 是否支持该 CoinType
 */
export function isCoinTypeSupported(coinType) {
  if (!coinType) return false;
  return getSupportedCoinTypes().includes(coinType.toLowerCase());
}

/**
 * 生成指定强度的助记词
 * @param {number} strength - 助记词强度，必须是 128、160、192、224 或 256
 * @param {number} num - 助记词个数
 * @returns {string} - 生成的助记词
 * @throws {Error} - 如果强度参数无效
 */
export function generateMnemonic(strength = 128, num = 1) {
  strength = Number(strength);
  num = Number(num);
  try {
    // 验证强度参数
    if (![128, 160, 192, 224, 256].includes(strength)) {
      throw new Error('无效的助记词强度，必须是 128、160、192、224 或 256');
    }
    // 验证 num 参数
    if (!Number.isInteger(num) || num < 1) {
      throw new Error('num 参数必须为大于等于 1 的整数');
    }
    // 批量生成助记词
    if (num > 1) {
      const mnemonics = [];
      for (let i = 0; i < num; i++) {
        mnemonics.push(bip39.generateMnemonic(strength));
      }
      return mnemonics;
    } else {
      return bip39.generateMnemonic(strength);
    }
  } catch (error) {
    throw new Error(`生成助记词失败: ${error.message}`);
  }
}

/**
 * 验证助记词是否有效
 * @param {string} mnemonic - 要验证的助记词
 * @returns {boolean} - 是否有效
 */
export function validateMnemonic(mnemonic) {
  if (!mnemonic) return false;

  return bip39.validateMnemonic(mnemonic);
}

/**
 * 根据 mnemonic 生成指定 coinType 的地址
 * @param {string} coinType - 要生成地址的 CoinType
 * @param {string} mnemonic - 助记词
 * @param {Object} option - 选项
 * @returns {Object} - 生成的地址
 */
export async function generateAddress(coinType, mnemonic, option = {}) {
  if (!coinType || !mnemonic) {
    const missing = [!coinType && 'coinType', !mnemonic && 'mnemonic'].filter(Boolean);
    throw new MissingParameterError(missing);
  }
  if (!isCoinTypeSupported(coinType)) {
    throw new UnsupportedCoinTypeError(coinType);
  }

  if (!validateMnemonic(mnemonic)) {
    throw new InvalidMnemonicError(`无效的助记词格式: ${logger.maskSensitiveData(mnemonic)}`);
  }

  // 懒加载并获取钱包对象
  const wallet = await _getWalletByCoinType(coinType);
  if (!wallet) {
    throw new UnsupportedCoinTypeError(coinType);
  }

  try {
    // 获取 privateKey
    const privateKey = await wallet.getDerivedPrivateKey(mnemonic, option);

    // 验证私钥是否有效
    if (!privateKey) {
      throw new PrivateKeyGenerationError(coinType, '生成的私钥无效');
    }

    // 生成地址
    const addressResult = await wallet.getNewAddress({ privateKey });

    // 标准化返回格式，确保与后端Address模型匹配
    return {
      coinType,
      chain: coinType,
      address: addressResult.address,
      privateKey,
      publicKey: addressResult.publicKey || '',
    };
  } catch (error) {
    logger.error(`生成 ${coinType} 地址失败: ${error.message}`);
    throw error;
  }
}

/**
 * 根据 mnemonic 生成所有 支持的 coinType 的地址
 * @param {string} mnemonic - 助记词
 * @param {Object} option - 选项
 * @param {Object} parallelOptions - 并行处理选项
 * @returns {Object[]} - 生成的地址
 */
export async function generateAllSupportedCoinTypeAddresses(
  mnemonic,
  option = {},
  parallelOptions = {}
) {
  const coinTypes = getSupportedCoinTypes();

  const results = await processItems(
    coinTypes,
    async (coinType) => {
      try {
        return await generateAddress(coinType, mnemonic, option);
      } catch (error) {
        logger.error(`生成 ${coinType} 地址失败: ${error.message}`);
        // 返回错误信息而不是抛出异常，这样其他币种仍能正常生成
        return {
          chain: coinType,
          address: null,
          privateKey: null,
          publicKey: null,
          error: error.message,
        };
      }
    },
    {
      parallel: true,
      batchSize: 5, // 每批处理5个币种
      taskName: '批量生成地址',
      ...parallelOptions,
    }
  );

  // 过滤掉失败的结果，只返回成功生成的地址
  return results.filter((result) => result && !result.error);
}

/**
 * 根据 mnemonics 生成指定 coinType 的地址
 * @param {string[]} mnemonics - 助记词数组
 * @param {string} coinType - 要生成地址的 CoinType
 * @param {Object} option - 选项
 * @param {Object} parallelOptions - 并行处理选项
 * @returns {Object[]} - 生成的地址
 */
export async function generateAddressByMnemonics(
  mnemonics,
  coinType,
  option = {},
  parallelOptions = {}
) {
  return processItems(
    mnemonics,
    async (mnemonic) => await generateAddress(coinType, mnemonic, option),
    {
      parallel: true,
      batchSize: 10, // 每批处理10个助记词
      taskName: '批量助记词生成地址',
      ...parallelOptions,
    }
  );
}

/**
 * 根据 mnemonic 和 index 生成指定数量指定 coinType 的地址
 * @param {string} mnemonic - 助记词
 * @param {string} coinType - 要生成地址的 CoinType
 * @param {number} count - 生成地址的数量
 * @param {number} index - 起始地址的索引
 * @param {Object} option - 选项
 * @param {Object} parallelOptions - 并行处理选项
 * @returns {Object[]} - 生成的地址数组
 */
export async function generateAddressesByMnemonic(
  mnemonic,
  coinType,
  count,
  index,
  option = {},
  parallelOptions = {}
) {
  // 参数验证
  if (!Number.isInteger(count) || count <= 0) {
    throw new Error('count必须是正整数');
  }

  if (!Number.isInteger(index) || index < 0) {
    throw new Error('index必须是非负整数');
  }

  return processIndexTasks(
    count,
    async (i) => await generateAddress(coinType, mnemonic, { ...option, index: index + i }),
    {
      parallel: true,
      batchSize: 20, // 每批处理20个索引
      taskName: '批量索引生成地址',
      ...parallelOptions,
    }
  );
}
