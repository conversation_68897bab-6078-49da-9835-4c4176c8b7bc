/**
 * 钱包工厂 - 统一管理钱包实例的创建
 */

/**
 * 钱包工厂类 - 负责创建各种钱包实例
 */
class WalletFactory {
  /**
   * 异步创建钱包实例
   * @param {string} coinType - 币种类型
   * @returns {Promise<import('./base-wallet.js').BaseWallet>} - 钱包实例
   * @throws {Error} - 如果币种不支持或创建失败
   */
  async createWallet(coinType) {
    if (!coinType) {
      throw new Error('币种类型不能为空');
    }

    const type = coinType.toLowerCase();

    try {
      // 使用动态导入替代 import.meta.glob 以支持所有环境
      const module = await import(`./${type}/index.js`);
      const WalletClass = module.default;

      if (!WalletClass) {
        throw new Error(`找不到钱包类: ${type}`);
      }

      return new WalletClass();
    } catch (error) {
      // 提供更详细的错误信息
      if (
        error.code === 'ERR_MODULE_NOT_FOUND' ||
        error.message.includes('Failed to resolve module')
      ) {
        throw new Error(`不支持的币种类型: ${coinType}`);
      }
      throw new Error(`创建钱包失败 (${coinType}): ${error.message}`);
    }
  }
}

// 创建单例实例
const walletFactory = new WalletFactory();

export default walletFactory;
