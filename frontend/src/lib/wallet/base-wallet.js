export class BaseWallet {
  constructor(walletInstance) {
    this.wallet = walletInstance;
  }

  async getDerivedPrivateKey(mnemonic, options = {}) {
    const pathParams = {
      index: options.index ?? 0,
      ...(options.segwitType !== undefined && { segwitType: options.segwitType }),
    };
    const hdPath = await this.wallet.getDerivedPath(pathParams);
    return await this.wallet.getDerivedPrivateKey({ mnemonic, hdPath });
  }

  async validPrivateKey(privateKey) {
    return await this.wallet.validPrivateKey({ privateKey });
  }

  async getNewAddress(params) {
    const newAddressParams = {
      segwitType: 'segwit',
      ...(typeof params === 'string' ? { privateKey: params } : params),
    };

    // For Cosmos wallets, ensure hrp parameter is set if not provided
    if (this.wallet.getPrefix && !newAddressParams.hrp) {
      try {
        newAddressParams.hrp = this.wallet.getPrefix();
      } catch {
        // If getPrefix throws (like CommonCosmosWallet), use default 'cosmos'
        if (this.wallet.constructor.name.includes('CommonCosmos')) {
          newAddressParams.hrp = 'cosmos';
        }
      }
    }

    return await this.wallet.getNewAddress(newAddressParams);
  }

  async validAddress(params) {
    const addressParams = typeof params === 'string' ? { address: params } : params;
    return await this.wallet.validAddress(addressParams);
  }
}
