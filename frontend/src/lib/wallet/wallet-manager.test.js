import { bip39 } from '@okxweb3/crypto-lib';
import { it, vi, expect, describe, afterEach, beforeEach } from 'vitest';

import {
  InvalidMnemonicError,
  MissingParameterError,
  UnsupportedCoinTypeError,
} from './wallet-errors.js';
import {
  generateAddress,
  generateMnemonic,
  validateMnemonic,
  isCoinTypeSupported,
  getSupportedCoinTypes,
  generateAddressByMnemonics,
  generateAddressesByMnemonic,
  generateAllSupportedCoinTypeAddresses,
} from './wallet-manager.js';
import walletFactory from './wallet-factory.js';
// Mock dependencies
import { createCache } from '../tools/cache.js';
import { SUPPORTED_COINTYPES } from '../../global-config.js';

const walletCache = createCache({
  namespace: 'wallet',
});

vi.mock('../tools/cache.js', () => ({
  createCache: vi.fn(() => ({
    get: vi.fn(),
    set: vi.fn(),
    clear: vi.fn(),
  })),
}));

vi.mock('@okxweb3/crypto-lib', () => ({
  bip39: {
    generateMnemonic: vi.fn(),
    validateMnemonic: vi.fn(),
  },
}));

vi.mock('./wallet-factory.js');
vi.mock('../tools/logger.js', () => ({
  default: {
    error: vi.fn(),
    maskSensitiveData: (data) => data,
  },
}));

const mockWallet = {
  getDerivedPrivateKey: vi.fn(),
  getNewAddress: vi.fn(),
};

afterEach(() => {
  walletCache.clear();
});

describe('wallet-manager', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    walletFactory.createWallet.mockResolvedValue(mockWallet);
  });

  describe('getSupportedCoinTypes', () => {
    it('should return the list of supported coin types', () => {
      const expectedCoinTypes = SUPPORTED_COINTYPES.map((c) => c.value);
      expect(getSupportedCoinTypes()).toEqual(expectedCoinTypes);
    });
  });

  describe('isCoinTypeSupported', () => {
    it('should return true for a supported coin type', () => {
      expect(isCoinTypeSupported('btc')).toBe(true);
    });

    it('should return false for an unsupported coin type', () => {
      expect(isCoinTypeSupported('doge')).toBe(false);
    });

    it('should return false for a null or undefined coin type', () => {
      expect(isCoinTypeSupported(null)).toBe(false);
      expect(isCoinTypeSupported(undefined)).toBe(false);
    });
  });

  describe('generateMnemonic', () => {
    it('should generate a single mnemonic with default strength', () => {
      bip39.generateMnemonic.mockReturnValue('test mnemonic');
      const mnemonic = generateMnemonic();
      expect(bip39.generateMnemonic).toHaveBeenCalledWith(128);
      expect(mnemonic).toBe('test mnemonic');
    });

    it('should generate multiple mnemonics', () => {
      bip39.generateMnemonic.mockReturnValue('test mnemonic');
      const mnemonics = generateMnemonic(128, 3);
      expect(bip39.generateMnemonic).toHaveBeenCalledTimes(3);
      expect(mnemonics).toEqual(['test mnemonic', 'test mnemonic', 'test mnemonic']);
    });

    it('should throw an error for invalid strength', () => {
      expect(() => generateMnemonic(100)).toThrow('无效的助记词强度');
    });
  });

  describe('validateMnemonic', () => {
    it('should return true for a valid mnemonic', () => {
      bip39.validateMnemonic.mockReturnValue(true);
      expect(validateMnemonic('valid mnemonic')).toBe(true);
    });

    it('should return false for an invalid mnemonic', () => {
      bip39.validateMnemonic.mockReturnValue(false);
      expect(validateMnemonic('invalid mnemonic')).toBe(false);
    });
  });

  describe('generateAddress', () => {
    const mnemonic = 'test mnemonic';
    const coinType = 'btc';

    it('should generate an address successfully', async () => {
      bip39.validateMnemonic.mockReturnValue(true);
      mockWallet.getDerivedPrivateKey.mockResolvedValue('private_key');
      mockWallet.getNewAddress.mockResolvedValue({ address: 'btc_address' });

      const result = await generateAddress(coinType, mnemonic);

      expect(walletFactory.createWallet).toHaveBeenCalledWith('btc');
      expect(mockWallet.getDerivedPrivateKey).toHaveBeenCalledWith(mnemonic, {});
      expect(mockWallet.getNewAddress).toHaveBeenCalledWith({ privateKey: 'private_key' });
      expect(result).toEqual({
        coinType: 'btc',
        chain: 'btc',
        address: 'btc_address',
        privateKey: 'private_key',
        publicKey: '',
      });
    });

    it('should throw MissingParameterError if coinType or mnemonic is missing', async () => {
      await expect(generateAddress(null, mnemonic)).rejects.toThrow(MissingParameterError);
      await expect(generateAddress(coinType, null)).rejects.toThrow(MissingParameterError);
    });

    it('should throw UnsupportedCoinTypeError for unsupported coin types', async () => {
      await expect(generateAddress('doge', mnemonic)).rejects.toThrow(UnsupportedCoinTypeError);
    });

    it('should throw InvalidMnemonicError for invalid mnemonics', async () => {
      bip39.validateMnemonic.mockReturnValue(false);
      await expect(generateAddress(coinType, 'invalid mnemonic')).rejects.toThrow(
        InvalidMnemonicError
      );
    });
  });

  describe('generateAllSupportedCoinTypeAddresses', () => {
    it('should generate addresses for all supported coin types', async () => {
      bip39.validateMnemonic.mockReturnValue(true);
      mockWallet.getDerivedPrivateKey.mockResolvedValue('private_key');
      mockWallet.getNewAddress.mockResolvedValue({ address: 'mock_address' });

      const results = await generateAllSupportedCoinTypeAddresses('test mnemonic');
      const supportedCoinTypes = getSupportedCoinTypes();

      expect(results.length).toBe(supportedCoinTypes.length);
      expect(walletFactory.createWallet).toHaveBeenCalledTimes(supportedCoinTypes.length);
    });
  });

  describe('generateAddressByMnemonics', () => {
    it('should generate addresses for a list of mnemonics', async () => {
      const mnemonics = ['m1', 'm2'];
      bip39.validateMnemonic.mockReturnValue(true);
      mockWallet.getDerivedPrivateKey.mockResolvedValue('private_key');
      mockWallet.getNewAddress.mockResolvedValue({ address: 'mock_address' });

      const results = await generateAddressByMnemonics(mnemonics, 'btc');

      expect(results.length).toBe(2);
      expect(mockWallet.getDerivedPrivateKey).toHaveBeenCalledTimes(2);
    });
  });

  describe('generateAddressesByMnemonic', () => {
    it('should generate a specified number of addresses', async () => {
      const mnemonic = 'test mnemonic';
      const count = 3;
      const index = 0;
      bip39.validateMnemonic.mockReturnValue(true);
      mockWallet.getDerivedPrivateKey.mockResolvedValue('private_key');
      mockWallet.getNewAddress.mockResolvedValue({ address: 'mock_address' });

      const results = await generateAddressesByMnemonic(mnemonic, 'btc', count, index);

      expect(results.length).toBe(count);
      expect(mockWallet.getDerivedPrivateKey).toHaveBeenCalledTimes(count);
      expect(mockWallet.getDerivedPrivateKey).toHaveBeenCalledWith(mnemonic, { index: 0 });
      expect(mockWallet.getDerivedPrivateKey).toHaveBeenCalledWith(mnemonic, { index: 1 });
      expect(mockWallet.getDerivedPrivateKey).toHaveBeenCalledWith(mnemonic, { index: 2 });
    });

    it('should throw an error for invalid count or index', async () => {
      await expect(generateAddressesByMnemonic('m', 'btc', 0, 0)).rejects.toThrow(
        'count必须是正整数'
      );
      await expect(generateAddressesByMnemonic('m', 'btc', 1, -1)).rejects.toThrow(
        'index必须是非负整数'
      );
    });
  });
});
