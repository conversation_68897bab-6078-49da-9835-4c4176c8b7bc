import {
  TokenTON,
  NetworkSui,
  NetworkTron,
  NetworkAptos,
  NetworkSolana,
  NetworkBitcoin,
  NetworkEthereum,
  NetworkCosmosHub,
} from '@web3icons/react';
import { useState } from 'react';

// @mui
import {
  Box,
  Stack,
  Dialog,
  Button,
  Select,
  MenuItem,
  TextField,
  CardHeader,
  Typography,
  InputLabel,
  IconButton,
  FormControl,
  DialogActions,
  DialogContent,
  LinearProgress,
  CircularProgress,
} from '@mui/material';

import { useTranslate } from 'src/locales';
import { Wallet, WalletService } from 'src/bindings';
import { generateAddress, generateMnemonic } from 'src/lib/wallet/wallet-manager.js';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

export function WalletNewDialog({ open, onClose, onSuccess }) {
  const { t } = useTranslate();
  const MAX_WALLET_COUNT = 1000;
  const DEFAULT_WALLET_COUNT = 10;
  const DEFAULT_MNEMONIC_LENGTH = 128;
  const DEFAULT_NETWORK = 'eth';

  const [mnemonicLength, setMnemonicLength] = useState(DEFAULT_MNEMONIC_LENGTH);
  const [network, setNetwork] = useState(DEFAULT_NETWORK);
  const [walletCount, setWalletCount] = useState(DEFAULT_WALLET_COUNT);
  const [walletCountError, setWalletCountError] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentProgress, setCurrentProgress] = useState({ current: 0, total: 0 });
  const [error, setError] = useState('');

  // 重置表单状态
  const resetForm = () => {
    setMnemonicLength(DEFAULT_MNEMONIC_LENGTH);
    setWalletCount(DEFAULT_WALLET_COUNT);
    setWalletCountError(false);
    setIsGenerating(false);
    setCurrentProgress({ current: 0, total: 0 });
    setError('');
  };

  // 处理对话框关闭
  const handleClose = () => {
    if (!isGenerating) {
      resetForm();
      onClose();
    }
  };

  const handleMnemonicLengthChange = (event) => {
    setMnemonicLength(event.target.value);
  };

  const handleNetworkChange = (event) => {
    setNetwork(event.target.value);
  };

  const handleWalletCountChange = (event) => {
    let value = parseInt(event.target.value, 10);

    // 清空时设为1
    if (isNaN(value) || value < 1) {
      value = 1;
      setWalletCountError(false);
    } else if (value > MAX_WALLET_COUNT) {
      setWalletCountError(true);
    } else {
      setWalletCountError(false);
    }

    setWalletCount(value);
  };

  const handleGenerateWallets = async () => {
    if (!mnemonicLength || !walletCount || walletCountError) return;

    setIsGenerating(true);
    setError('');
    setCurrentProgress({ current: 0, total: parseInt(walletCount, 10) });
    const totalCount = parseInt(walletCount, 10);

    try {
      // 生成助记词
      const mnemonics = generateMnemonic(mnemonicLength, totalCount);

      // 保证 mnemonics 是数组
      const mnemonicArray = Array.isArray(mnemonics) ? mnemonics : [mnemonics];

      // 批量创建钱包
      const wallets = [];

      for (let i = 0; i < mnemonicArray.length; i++) {
        try {
          const mnemonic = mnemonicArray[i];

          // 更新进度
          setCurrentProgress({ current: i + 1, total: totalCount });

          // 添加小延迟确保UI更新
          await new Promise((resolve) => setTimeout(resolve, 10));

          const addr = await generateAddress(network, mnemonic);

          // 创建钱包对象
          const wallet = new Wallet({
            network,
            mnemonic,
            addresses: addr.address,
            privateKey: addr.privateKey,
            publicKey: addr.publicKey,
            group: 'Generated',
          });

          wallets.push(wallet);
        } catch (walletError) {
          throw new Error(`生成第 ${i + 1} 个钱包失败: ${walletError.message}`);
        }
      }

      // 使用 BatchCreate 方法批量创建钱包
      await WalletService.BatchCreate(wallets);

      // 显示成功消息
      toast.success(t('wallet:createSuccess', { count: totalCount }));

      // 通知父组件数据已更新
      if (onSuccess) {
        onSuccess();
      }

      // 关闭对话框
      handleClose();
    } catch (err) {
      console.error(err);
      const errorMsg = t('wallet:generateFailed');
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsGenerating(false);
      setCurrentProgress({ current: 0, total: 0 });
    }
  };

  return (
    <Dialog
      open={open}
      onClose={!isGenerating ? handleClose : undefined}
      maxWidth="md"
      fullWidth
      disableEscapeKeyDown={isGenerating}
    >
      <CardHeader
        title={t('wallet:generate_title')}
        subheader={t('wallet:generate_tip')}
        action={
          <IconButton aria-label="关闭" onClick={handleClose} disabled={isGenerating}>
            <Iconify icon="solar:close-circle-bold" />
          </IconButton>
        }
      />

      <DialogContent sx={{ pt: 3 }}>
        <Stack spacing={4}>
          <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 3 }}>
            <FormControl fullWidth>
              <InputLabel id="network-label">{t('wallet:choice_network')}</InputLabel>
              <Select
                labelId="network-label"
                value={network}
                label={t('wallet:choice_network')}
                onChange={handleNetworkChange}
                disabled={isGenerating}
              >
                <MenuItem key="ethereum" value="eth">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkEthereum size={24} variant="branded" />
                    <Typography>Ethereum</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="solana" value="sol">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkSolana size={24} variant="branded" />
                    <Typography>Solana</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="bitcoin" value="btc">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkBitcoin size={24} variant="branded" />
                    <Typography>Bitcoin</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="sui" value="sui">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkSui size={24} variant="branded" />
                    <Typography>Sui</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="tron" value="trx">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkTron size={24} variant="branded" />
                    <Typography>Tron</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="aptos" value="aptos">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkAptos size={24} variant="branded" />
                    <Typography>Aptos</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="ton" value="ton">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <TokenTON size={24} variant="branded" />
                    <Typography>TON</Typography>
                  </Stack>
                </MenuItem>
                <MenuItem key="cosmos" value="cosmos">
                  <Stack direction="row" spacing={1.5} alignItems="center">
                    <NetworkCosmosHub size={24} variant="branded" />
                    <Typography>Cosmos</Typography>
                  </Stack>
                </MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel id="mnemonic-length-label">
                {t('wallet:choice_mnemonic_length')}
              </InputLabel>
              <Select
                labelId="mnemonic-length-label"
                value={mnemonicLength}
                label={t('wallet:choice_mnemonic_length')}
                onChange={handleMnemonicLengthChange}
                disabled={isGenerating}
              >
                <MenuItem value={128}>12 {t('wallet:words')}</MenuItem>
                <MenuItem value={160}>15 {t('wallet:words')}</MenuItem>
                <MenuItem value={192}>18 {t('wallet:words')}</MenuItem>
                <MenuItem value={224}>21 {t('wallet:words')}</MenuItem>
                <MenuItem value={256}>24 {t('wallet:words')}</MenuItem>
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <TextField
                label={t('wallet:generate_wallet_count')}
                type="number"
                value={walletCount}
                onChange={handleWalletCountChange}
                error={walletCountError}
                helperText={
                  walletCountError
                    ? t('wallet:maxWalletCountError', { max: MAX_WALLET_COUNT })
                    : t('wallet:walletCountHelper', { max: MAX_WALLET_COUNT })
                }
                disabled={isGenerating}
                slotProps={{
                  input: {
                    min: 1,
                    max: MAX_WALLET_COUNT,
                  },
                }}
              />
            </FormControl>
          </Box>

          {/* 错误信息显示 */}
          {error && (
            <Box
              sx={{
                p: 2,
                bgcolor: 'error.lighter',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'error.main',
              }}
            >
              <Typography variant="body2" color="error.main">
                {error}
              </Typography>
            </Box>
          )}

          {/* 进度显示 */}
          {isGenerating && (
            <Box sx={{ width: '100%' }}>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 1,
                }}
              >
                <Typography variant="body2" color="text.secondary">
                  {t('wallet:generatingProgress')}
                </Typography>
                <Typography variant="body2" color="primary">
                  {currentProgress.current} / {currentProgress.total}
                </Typography>
              </Box>
              <LinearProgress
                variant="determinate"
                value={
                  currentProgress.total > 0
                    ? (currentProgress.current / currentProgress.total) * 100
                    : 0
                }
                sx={{ height: 8, borderRadius: 4 }}
              />
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                {t('wallet:generating_progress', {
                  current: currentProgress.current,
                  total: currentProgress.total,
                })}
              </Typography>
            </Box>
          )}
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} color="inherit" disabled={isGenerating}>
          {t('common:action.cancel')}
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleGenerateWallets}
          disabled={!mnemonicLength || !walletCount || walletCountError || isGenerating}
          startIcon={
            isGenerating ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              <Iconify icon="hugeicons:wallet-02" />
            )
          }
        >
          {isGenerating ? t('wallet:generating') : t('wallet:immediate_generation')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
