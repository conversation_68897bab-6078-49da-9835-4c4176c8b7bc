import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useMemo, useState, forwardRef, useCallback, useImperativeHandle } from 'react';

import { Box, Chip, Button, Tooltip, IconButton } from '@mui/material';

import { useListErrorHandler } from 'src/hooks/use-service-error-handler';

import { fDateTime } from 'src/utils/format-time';
import { maskString, maskMnemonic } from 'src/utils/format-text';

import { useTranslate } from 'src/locales';
import { WalletService } from 'src/bindings';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { TableView } from 'src/components/table/table-view';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { WalletEmpty } from './wallet-empty';
import { WalletEditDialog } from './wallet-edit-dialog';

export const WalletList = forwardRef(function WalletList({ onCreateClick, onImportClick }, ref) {
  const { t } = useTranslate();
  const [selectedWallet, setSelectedWallet] = useState(null);
  const tableRef = useRef(null);

  // 使用统一的错误处理 - 移除onInitializationRequired以保持稳定性
  const { handleListData } = useListErrorHandler();

  const editDialog = useBoolean(false);
  const deleteDialog = useBoolean(false);

  // 暴露给父组件的方法
  useImperativeHandle(
    ref,
    () => ({
      refetch: () => {
        tableRef.current?.refetch();
      },
    }),
    []
  );

  // 数据获取函数 - 纯函数，只负责数据获取
  const dataFetcher = useCallback(
    async (page, pageSize, orderBy, order, searchValue) => {
      try {
        // 安全获取搜索值
        const searchText = typeof searchValue === 'string' ? searchValue : '';
        const trimmedSearchText = searchText?.trim() || '';

        console.log('==wallet-list== dataFetcher:', page, pageSize, orderBy, order, searchValue);

        // 构建分页参数，支持排序
        const pageable = {
          page: page + 1, // 后端从1开始
          pageSize,
          sort: orderBy || 'id',
          order: order || 'desc',
        };

        let queryBuilder = null;

        // 如果有搜索词，构建查询条件
        if (trimmedSearchText) {
          queryBuilder = {
            conditions: [
              {
                field: 'mnemonic',
                operator: 'LIKE',
                value: `%${trimmedSearchText}%`,
                logic: 'OR',
              },
              {
                field: 'addresses',
                operator: 'LIKE',
                value: `%${trimmedSearchText}%`,
                logic: 'OR',
              },
            ],
            logic: 'OR',
          };
        }

        // 使用统一错误处理包装服务调用
        const result = await handleListData(WalletService.Page(pageable, queryBuilder));

        const data = result?.data || [];
        const total = result?.total || 0;

        return [data, total];
      } catch (error) {
        console.error('==wallet-list== dataFetcher error:', error);
        return [[], 0];
      }
    },
    [handleListData]
  );

  const handleCopy = useCallback(
    (text) => {
      navigator.clipboard.writeText(text);
      toast.success('Address ' + t('common:tips.copied'));
    },
    [t]
  );

  // 处理编辑
  const handleEdit = useCallback(
    (wallet, e) => {
      e.stopPropagation();
      setSelectedWallet(wallet);
      editDialog.onTrue();
    },
    [editDialog]
  );

  // 处理删除
  const handleDelete = useCallback(
    (wallet, e) => {
      e.stopPropagation();
      setSelectedWallet(wallet);
      deleteDialog.onTrue();
    },
    [deleteDialog]
  );

  // 确认删除单个钱包
  const confirmDelete = async () => {
    if (!selectedWallet) return;

    try {
      await WalletService.DeleteByID(selectedWallet.id);
      toast.success(t('common:message.deleteSuccess'));
      tableRef.current?.refetch(); // 刷新列表
      deleteDialog.onFalse();
      setSelectedWallet(null);
    } catch (error) {
      console.error('删除钱包失败:', error);
      toast.error(t('common:message.deleteFailed'));
    }
  };

  // 批量删除钱包
  const handleBatchDelete = useCallback(
    async (selectedIds) => {
      if (!selectedIds || selectedIds.length === 0) return;

      try {
        await WalletService.BatchDelete(selectedIds);
        toast.success(t('common:message.deleteSuccess', { count: selectedIds.length }));
        tableRef.current?.refetch(); // 刷新列表
      } catch (error) {
        console.error('批量删除钱包失败:', error);
        toast.error(t('common:message.deleteFailed'));
      }
    },
    [t]
  );

  // 处理编辑完成
  const handleEditComplete = () => {
    editDialog.onFalse();
    setSelectedWallet(null);
    tableRef.current?.refetch(); // 刷新列表
  };

  // 表格列定义 - 使用 useMemo 优化性能并支持高亮搜索词
  const columns = useMemo(
    () => [
      {
        id: 'id',
        label: 'ID',
        width: 80,
        minWidth: 80,
        align: 'center',
        sortable: true,
      },
      {
        id: 'network',
        label: t('wallet:lable_network'),
        width: 120,
        minWidth: 120,
        noWrap: true,
        renderCell: (row) => <Box sx={{ fontWeight: 500 }}>{row.network}</Box>,
      },
      {
        id: 'addresses',
        label: t('wallet:lable_address'),
        width: 220,
        minWidth: 200,
        noWrap: true,
        renderCell: (row) => (
          <Box sx={{ fontWeight: 500 }} onClick={() => handleCopy(row.addresses)}>
            {maskString(row.addresses, 12, 5)}
          </Box>
        ),
      },
      {
        id: 'privateKey',
        label: t('wallet:lable_privatekey'),
        width: 220,
        minWidth: 200,
        noWrap: true,
        isHidden: true,
        renderCell: (row) => <Box sx={{ fontWeight: 500 }}>{maskString(row.privateKey, 8, 5)}</Box>,
      },
      {
        id: 'publicKey',
        label: t('wallet:lable_publicKey'),
        width: 220,
        minWidth: 200,
        noWrap: true,
        isHidden: true,
        renderCell: (row) => <Box sx={{ fontWeight: 500 }}>{maskString(row.publicKey, 8, 5)}</Box>,
      },
      {
        id: 'mnemonic',
        label: 'Mnemonic',
        width: 320,
        minWidth: 300,
        noWrap: true,
        sortable: false,
        isHidden: true,
        renderCell: (row) => <Box sx={{ fontWeight: 500 }}>{maskMnemonic(row.mnemonic)}</Box>,
      },
      {
        id: 'created_at',
        label: t('common:createdAt'),
        width: 160,
        minWidth: 160,
        noWrap: true,
        sortable: true,
        renderCell: (row) => fDateTime(row.created_at, 'YYYY/MM/DD HH:mm'),
      },
      {
        id: 'group',
        label: t('wallet:label_group'),
        width: 120,
        minWidth: 100,
        noWrap: true,
        sortable: false,
        renderCell: (row) =>
          row.group ? (
            <Chip label={row.group} size="small" variant="soft" color="primary" />
          ) : (
            <Box sx={{ color: 'text.secondary' }}>-</Box>
          ),
      },
      {
        id: 'remark',
        label: t('wallet:label_remark'),
        width: 300,
        minWidth: 200,
        noWrap: true,
        renderCell: (row) => (
          <Box
            sx={{
              color: row.remark ? 'text.primary' : 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {row.remark || '-'}
          </Box>
        ),
      },
      {
        id: 'action',
        label: t('common:action.action'),
        width: 120,
        minWidth: 120,
        align: 'center',
        renderCell: (row) => (
          <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
            <Tooltip title={t('common:action.edit')}>
              <IconButton
                size="small"
                color="primary"
                onClick={(e) => handleEdit(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'primary.lighter' },
                }}
              >
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            </Tooltip>
            <Tooltip title={t('common:action.delete')}>
              <IconButton
                size="small"
                color="error"
                onClick={(e) => handleDelete(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'error.lighter' },
                }}
              >
                <Iconify icon="solar:trash-bin-trash-bold" width={16} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [t, handleEdit, handleDelete, handleCopy]
  );

  return (
    <>
      <TableView
        ref={tableRef}
        columns={columns}
        dataFetcher={dataFetcher}
        onDelete={handleBatchDelete}
        tableId="wallet-list"
        emptyComponent={WalletEmpty}
        emptyProps={{
          onCreateClick,
          onImportClick,
        }}
      />

      {/* Edit Dialog */}
      {editDialog.value && (
        <WalletEditDialog
          open={editDialog.value}
          onClose={editDialog.onFalse}
          wallet={selectedWallet}
          onSuccess={handleEditComplete}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        title={t('common:action.delete')}
        content={
          <>
            {t('common:tips.deleteConfirm')}
            <Box component="span" sx={{ fontWeight: 'bold' }}>
              {selectedWallet?.label || selectedWallet?.remark || `ID: ${selectedWallet?.id}`}
            </Box>
            ?
          </>
        }
        action={
          <Button variant="contained" color="inherit" onClick={confirmDelete} autoFocus>
            {t('common:action.delete')}
          </Button>
        }
      />
    </>
  );
});
