import { useBoolean } from 'minimal-shared/hooks';
import { useRef, useMemo, useState, useCallback } from 'react';

import { Box, Button, Tooltip, IconButton } from '@mui/material';

import { useListErrorHandler } from 'src/hooks/use-service-error-handler';

import { fDateTime } from 'src/utils/format-time';
import { highlightText } from 'src/utils/highlight-text';

import { MailService } from 'src/bindings';
import { useTranslate } from 'src/locales';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { TableView } from 'src/components/table/table-view';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { AccountEditDialog } from '../account-edit-dialog';

export function ListView() {
  const { t } = useTranslate();
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [currentSearchTerm, setCurrentSearchTerm] = useState('');
  const tableRef = useRef(null);

  const deleteDialog = useBoolean(false);
  const editDialog = useBoolean(false);

  // 使用统一的错误处理 - 移除onInitializationRequired以保持稳定性
  const { handleListData } = useListErrorHandler();

  // 获取邮箱数据 - 重构为新架构的 dataFetcher
  const dataFetcher = useCallback(
    async (page, pageSize, orderBy, order, searchValue) => {
      try {
        // 更新当前搜索词状态，用于高亮显示
        setCurrentSearchTerm(searchValue?.trim() || '');

        let serviceCall;

        if (searchValue && searchValue.trim()) {
          // 有搜索词时使用 SearchPageList，在 email 和 remark 字段中进行 OR 查询
          const searchFields = ['email', 'remark'];
          serviceCall = MailService.SearchPageList(
            page + 1, // 后端从1开始
            pageSize,
            searchValue.trim(),
            searchFields
          );
        } else {
          // 无搜索词时使用 PageList
          serviceCall = MailService.PageList(page + 1, pageSize);
        }

        // 使用统一错误处理包装服务调用
        const result = await handleListData(serviceCall);

        return [result?.data || [], result?.total || 0];
      } catch (error) {
        console.error('邮箱数据获取失败:', error);
        return [[], 0];
      }
    },
    [handleListData]
  );

  // 批量删除邮箱
  const handleBatchDelete = useCallback(
    async (selectedIds) => {
      if (!selectedIds || selectedIds.length === 0) return;

      try {
        // 使用后端的批量删除方法
        await MailService.BatchDelete(selectedIds);
        toast.success(t('common:message.deleteSuccess'));
        tableRef.current?.refetch(); // 刷新列表
      } catch (error) {
        console.error('批量删除邮箱失败:', error);
        toast.error(t('common:message.deleteFailed'));
      }
    },
    [t]
  );

  // 处理编辑操作
  const handleEdit = useCallback(
    (account, e) => {
      e.stopPropagation();
      setSelectedAccount(account);
      editDialog.onTrue();
    },
    [editDialog]
  );

  // 处理编辑完成
  const handleEditComplete = () => {
    editDialog.onFalse();
    setSelectedAccount(null);
    tableRef.current?.refetch(); // 刷新列表
  };

  // 处理单个删除操作
  const handleDelete = useCallback(
    (account, e) => {
      e.stopPropagation();
      setSelectedAccount(account);
      deleteDialog.onTrue();
    },
    [deleteDialog]
  );

  // 确认删除单个账号
  const confirmDelete = async () => {
    if (!selectedAccount) return;

    try {
      await MailService.DeleteByID(selectedAccount.id);
      toast.success(t('common:message.deleteSuccess'));
      tableRef.current?.refetch(); // 刷新列表
      deleteDialog.onFalse();
      setSelectedAccount(null);
    } catch (error) {
      console.error('删除邮箱失败:', error);
      toast.error(t('common:message.deleteFailed'));
    }
  };

  // 表格列定义 - 使用 useMemo 优化性能并支持高亮搜索词
  const columns = useMemo(
    () => [
      {
        id: 'id',
        label: 'ID',
        width: 80,
        minWidth: 80,
        align: 'center',
      },
      {
        id: 'email',
        label: t('account:account'),
        width: 240,
        minWidth: 200,
        sortable: true,
        renderCell: (row) => (
          <Box sx={{ fontWeight: 500 }}>{highlightText(row.email || '-', currentSearchTerm)}</Box>
        ),
      },
      {
        id: 'imap',
        label: 'IMAP',
        width: 160,
        minWidth: 140,
        sortable: true,
        renderCell: (row) => <Box sx={{ fontWeight: 500 }}>{row.imap || '-'}</Box>,
      },
      {
        id: 'smtp',
        label: 'SMTP',
        width: 160,
        minWidth: 140,
        sortable: true,
        renderCell: (row) => <Box sx={{ fontWeight: 500 }}>{row.smtp || '-'}</Box>,
      },
      {
        id: 'password',
        label: t('account:password'),
        width: 180,
        minWidth: 150,
        renderCell: () => (
          <Box sx={{ fontFamily: 'monospace', fontSize: '0.875rem' }}>{'*'.repeat(8)}</Box>
        ),
      },
      {
        id: 'created_at',
        label: t('common:createdAt'),
        width: 160,
        minWidth: 160,
        sortable: true,
        renderCell: (row) => fDateTime(row.created_at, 'YYYY/MM/DD HH:mm'),
      },
      {
        id: 'remark',
        label: t('account:remark'),
        width: 200,
        minWidth: 150,
        renderCell: (row) => (
          <Box
            sx={{
              color: row.remark ? 'text.primary' : 'text.secondary',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
            }}
          >
            {row.remark || '-'}
          </Box>
        ),
      },
      {
        id: 'action',
        label: t('common:action.action'),
        width: 120,
        minWidth: 120,
        align: 'center',
        renderCell: (row) => (
          <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
            <Tooltip title={t('common:action.edit')}>
              <IconButton
                size="small"
                color="primary"
                onClick={(e) => handleEdit(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'primary.lighter' },
                }}
              >
                <Iconify icon="solar:pen-bold" width={16} />
              </IconButton>
            </Tooltip>
            <Tooltip title={t('common:action.delete')}>
              <IconButton
                size="small"
                color="error"
                onClick={(e) => handleDelete(row, e)}
                sx={{
                  '&:hover': { backgroundColor: 'error.lighter' },
                }}
              >
                <Iconify icon="solar:trash-bin-trash-bold" width={16} />
              </IconButton>
            </Tooltip>
          </Box>
        ),
      },
    ],
    [t, currentSearchTerm, handleEdit, handleDelete]
  );

  return (
    <>
      <TableView
        tableId="mail-list"
        ref={tableRef}
        columns={columns}
        dataFetcher={dataFetcher}
        onDelete={handleBatchDelete}
        enableSelection
        noDataText={t('common:tips.no_data')}
      />

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialog.value}
        onClose={deleteDialog.onFalse}
        title={t('common:action.delete')}
        content={
          <>
            {t('common:tips.deleteConfirm')}
            <Box component="span" sx={{ fontWeight: 'bold' }}>
              {selectedAccount?.email || `ID: ${selectedAccount?.id}`}
            </Box>
            ?
          </>
        }
        action={
          <Button variant="contained" color="inherit" onClick={confirmDelete} autoFocus>
            {t('common:action.delete')}
          </Button>
        }
      />

      {/* Edit Dialog */}
      {editDialog.value && (
        <AccountEditDialog
          open={editDialog.value}
          onClose={editDialog.onFalse}
          account={selectedAccount}
          accountType="mail"
          onSuccess={handleEditComplete}
        />
      )}
    </>
  );
}
