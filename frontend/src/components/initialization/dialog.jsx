import { useState, useEffect } from 'react';

import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  CardHeader,
  IconButton,
  StepContent,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';

import { useAppPassword } from 'src/hooks/use-app-password';

import { useTranslate } from 'src/locales';
import { allLangs } from 'src/locales/all-langs';
import { LanguagePopover } from 'src/layouts/components/language-popover';

import { Iconify } from 'src/components/iconify';
import { PasswordSetButton } from 'src/components/password';

import { WalletNewDialog } from 'src/sections/wallet/wallet-new-dialog';
import { WalletImportDialog } from 'src/sections/wallet/wallet-import-dialog';

// ----------------------------------------------------------------------

const STEPS = [
  {
    id: 'passcode',
    labelKey: 'init:passcode',
    description: 'settings:security.passwordTip',
    icon: 'ic:round-vpn-key',
    required: true,
  },
  {
    id: 'wallet',
    labelKey: 'init:wallet',
    description: 'init:walletDescription',
    icon: 'hugeicons:wallet-02',
    required: true,
  },
  {
    id: 'profile',
    labelKey: 'init:profile',
    description: 'init:profileDescription',
    icon: 'material-symbols:chrome-reader-mode',
    required: true,
  },
  {
    id: 'proxy',
    labelKey: 'init:proxy',
    description: 'init:proxyDescription',
    icon: 'mdi:ip',
    required: true,
  },
  {
    id: 'social',
    labelKey: 'init:social',
    description: 'init:socialDescription',
    icon: 'solar:users-group-rounded-bold',
    required: false,
  },
];

export default function InitializationDialog({
  open,
  onComplete,
  onClose: onCloseDialog,
  ...other
}) {
  const { t } = useTranslate();
  const { t: tInit } = useTranslate('init');
  const [activeStep, setActiveStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState(new Set());
  const [showCloseConfirm, setShowCloseConfirm] = useState(false);
  const [newWalletDialogOpen, setNewWalletDialogOpen] = useState(false);
  const [importWalletDialogOpen, setImportWalletDialogOpen] = useState(false);
  const [importedWalletCount, setImportedWalletCount] = useState(0);

  // 使用正确的应用密码hook
  const { hasPassword, refreshPasswordStatus } = useAppPassword();

  // 初始化时检查密码状态
  useEffect(() => {
    if (hasPassword) {
      // 如果已设置密码
      setCompletedSteps((prev) => new Set([...prev, 0]));
      setActiveStep(1); // 打开wallet步骤
    }
  }, [hasPassword]);

  const handlePasscodeSet = async () => {
    console.log('[InitializationDialog] handlePasscodeSet: 开始设置密码');

    // 密码设置成功，标记passcode步骤为完成
    setCompletedSteps((prev) => new Set([...prev, 0]));

    // 刷新密码状态，确保状态同步
    console.log('[InitializationDialog] handlePasscodeSet: 刷新密码状态');
    await refreshPasswordStatus();

    // 切换到wallet步骤
    console.log('[InitializationDialog] handlePasscodeSet: 切换到wallet步骤');
    setActiveStep(1);
  };

  const onCreateWallet = () => {
    setNewWalletDialogOpen(true);
  };

  const onImportWallet = () => {
    setImportWalletDialogOpen(true);
  };

  const handleCloseNewWalletDialog = () => {
    setNewWalletDialogOpen(false);
  };

  const handleCloseImportWalletDialog = () => {
    setImportWalletDialogOpen(false);
  };

  const handleWalletSuccess = (count = 1) => {
    // 钱包创建/导入成功后，标记wallet步骤为完成
    setCompletedSteps((prev) => new Set([...prev, 1]));
    // 记录成功导入的钱包数量
    setImportedWalletCount(count);
    // 延迟切换到profile步骤，让用户看到成功消息
    setTimeout(() => {
      setActiveStep(2);
    }, 2000);
  };

  // 处理关闭按钮点击
  const handleCloseClick = () => {
    setShowCloseConfirm(true);
  };

  // 确认关闭
  const handleConfirmClose = () => {
    setShowCloseConfirm(false);
    // 用户主动关闭，只调用关闭回调，不调用完成回调
    onCloseDialog?.();
  };

  // 取消关闭
  const handleCancelClose = () => {
    setShowCloseConfirm(false);
  };

  // 检查步骤是否已完成
  const isStepCompleted = (stepIndex) => {
    const step = STEPS[stepIndex];
    if (!step) return false;

    // passcode步骤：检查本地完成状态或全局密码设置状态
    if (step.id === 'passcode') {
      return completedSteps.has(stepIndex) || hasPassword;
    }

    // 其他步骤仅基于本次会话状态
    return completedSteps.has(stepIndex);
  };

  // 检查步骤是否可访问（密码步骤完成后才能访问其他步骤）
  const canAccessStep = (stepIndex) => {
    // 第0步（密码步骤）始终可访问
    if (stepIndex === 0) return true;

    // 其他步骤需要密码已设置（检查全局状态或本地完成状态）
    // 这样在密码刚设置完成但hasPassword还未更新时也能正确工作
    return hasPassword || completedSteps.has(0);
  };

  // 处理步骤点击（展开/折叠）
  const handleStepClick = (stepIndex) => {
    // 如果步骤不可访问，不做任何操作
    if (!canAccessStep(stepIndex)) {
      return;
    }

    if (activeStep === stepIndex) {
      // 如果当前步骤已展开，关闭它并打开下一个步骤
      const nextStep = stepIndex + 1;
      if (nextStep < STEPS.length && canAccessStep(nextStep)) {
        setActiveStep(nextStep);
      }
    } else {
      // 如果当前步骤未展开，展开它
      setActiveStep(stepIndex);
    }
  };

  // 检查是否可以完成整个初始化流程
  const canFinishInitialization = () =>
    STEPS.filter((step) => step.required).every((_, index) => isStepCompleted(index));

  // 完成初始化
  const handleFinishInitialization = async () => {
    if (canFinishInitialization()) {
      await onComplete?.();
    }
  };

  // 渲染步骤内容
  const renderStepContent = (stepIndex) => {
    const step = STEPS[stepIndex];

    switch (step.id) {
      case 'passcode':
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              <span dangerouslySetInnerHTML={{ __html: t(step.description) }} />
            </Typography>
            <PasswordSetButton onSuccess={handlePasscodeSet} />
          </Box>
        );

      case 'wallet':
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              <span dangerouslySetInnerHTML={{ __html: t(step.description) }} />
            </Typography>
            {t('wallet:please')}{' '}
            <Button variant="outlined" sx={{ m: 0.5 }} onClick={onCreateWallet}>
              {t('wallet:create')}
            </Button>{' '}
            {t('wallet:or')}{' '}
            <Button variant="outlined" sx={{ m: 0.5 }} onClick={onImportWallet}>
              {t('wallet:import')}
            </Button>{' '}
            {t('wallet:wallet')}
            {isStepCompleted(1) && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                {tInit('walletSuccess')} {importedWalletCount || 0}
              </Typography>
            )}
          </Box>
        );

      case 'profile':
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t(step.description)}
            </Typography>
          </Box>
        );

      case 'social':
        return (
          <Box sx={{ py: 2 }}>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              {t(step.description)}
            </Typography>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog fullScreen open={open} disableEscapeKeyDown {...other}>
      {/* 左上角语言切换按钮 */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          zIndex: 1,
          p: 4,
        }}
      >
        <LanguagePopover
          data={allLangs}
          showLabel
          slotProps={{
            arrow: { placement: 'top-left' },
            paper: {
              sx: {
                marginTop: '10px !important', // 强制左边距
              },
            },
          }}
        />
      </Box>

      {/* 右上角关闭按钮 */}
      <CardHeader
        sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          zIndex: 1,
          p: 4,
        }}
        action={
          <IconButton onClick={handleCloseClick}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        }
      />
      <DialogContent
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '100vh',
          p: 4,
        }}
      >
        {/* 标题 */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h4" sx={{ mb: 1 }}>
            {t('init:welcome')}
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {t('init:welcomeDescription')}
          </Typography>
        </Box>

        {/* Stepper */}
        <Box sx={{ width: '100%', maxWidth: 620 }}>
          <Stepper
            orientation="vertical"
            activeStep={activeStep}
            sx={{
              '& .MuiStepConnector-root': {
                marginLeft: '20px', // 对齐40px图标的中心点
              },
              '& .MuiStepConnector-line': {
                minHeight: 40,
                borderLeftWidth: 2,
                borderColor: 'divider',
                transition: 'border-color 0.3s ease',
              },
              // 确保最后一个步骤没有连接线
              '& .MuiStep-root:last-child .MuiStepConnector-root': {
                display: 'none',
              },
            }}
          >
            {STEPS.map((step, index) => (
              <Step
                key={step.id}
                completed={isStepCompleted(index)}
                expanded={activeStep === index}
              >
                <StepLabel
                  onClick={() => handleStepClick(index)}
                  sx={{
                    cursor: canAccessStep(index) ? 'pointer' : 'not-allowed',
                    opacity: canAccessStep(index) ? 1 : 0.5,
                    '& .MuiStepLabel-label': {
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                    },
                  }}
                  icon={
                    <Box
                      sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        bgcolor: isStepCompleted(index)
                          ? 'text.disabled'
                          : canAccessStep(index)
                            ? 'primary.main'
                            : 'action.disabledBackground',
                        color: 'common.white',
                        transition: 'all 0.3s ease',
                      }}
                    >
                      {isStepCompleted(index) ? (
                        <Iconify icon="solar:check-circle-bold" width={20} />
                      ) : !canAccessStep(index) ? (
                        <Iconify icon="solar:lock-keyhole-linear" width={20} />
                      ) : (
                        <Iconify icon={step.icon} width={20} />
                      )}
                    </Box>
                  }
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {t(step.labelKey)}
                    {!step.required && (
                      <Chip
                        label={t('init:optional')}
                        size="small"
                        variant="outlined"
                        color="default"
                      />
                    )}
                  </Box>
                </StepLabel>

                <StepContent
                  sx={{
                    borderLeft: '2px solid',
                    borderColor: 'divider',
                    ml: 2.5,
                    pl: 3,
                  }}
                >
                  {renderStepContent(index)}
                </StepContent>
              </Step>
            ))}
          </Stepper>

          {/* 完成初始化按钮 */}

          <Box sx={{ textAlign: 'center', mt: 4 }}>
            <Button
              disabled={!canFinishInitialization()}
              variant="contained"
              size="large"
              onClick={handleFinishInitialization}
              startIcon={<Iconify icon="mingcute:rocket-fill" />}
              sx={{ minWidth: 200 }}
            >
              {t('init:finishInitialization')}
            </Button>
          </Box>
        </Box>
      </DialogContent>

      {/* 关闭确认对话框 */}
      <Dialog
        open={showCloseConfirm}
        onClose={handleCancelClose}
        aria-labelledby="close-dialog-title"
        aria-describedby="close-dialog-description"
      >
        <DialogTitle id="close-dialog-title">{t('init:ensureCloseTitle')}</DialogTitle>
        <DialogContent>
          <Typography id="close-dialog-description">{t('init:ensureCloseDescription')}</Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelClose} color="primary">
            {t('init:cancel')}
          </Button>
          <Button onClick={handleConfirmClose} variant="contained" color="primary">
            {t('init:ensureCloseConfirm')}
          </Button>
        </DialogActions>
      </Dialog>

      {/* 钱包创建对话框 */}
      {newWalletDialogOpen && (
        <WalletNewDialog
          open={newWalletDialogOpen}
          onClose={handleCloseNewWalletDialog}
          onSuccess={handleWalletSuccess}
        />
      )}

      {/* 钱包导入对话框 */}
      {importWalletDialogOpen && (
        <WalletImportDialog
          open={importWalletDialogOpen}
          onClose={handleCloseImportWalletDialog}
          onSuccess={handleWalletSuccess}
        />
      )}
    </Dialog>
  );
}
