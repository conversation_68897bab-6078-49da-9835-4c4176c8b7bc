# 认证流程管理系统测试用例 (Auth Flow System Test Cases)

本文档详细描述了 A8Tools 认证流程管理系统的各种测试场景、流程步骤和期望结果。

## 📋 测试环境配置

### 环境变量设置
```javascript
// 开发模式
CONFIG.auth.skip = true

// 生产模式
CONFIG.auth.skip = false
```

### 测试前准备
1. 清除浏览器存储数据（localStorage, sessionStorage, indexedDB）
2. 确保前后端服务正常运行
3. 打开浏览器开发者工具查看控制台日志

---

## 🧪 测试场景分类

### 场景 A：开发模式 (auth.skip = true)
### 场景 B：生产模式 (auth.skip = false)
### 场景 C：安全性测试
### 场景 D：异常情况处理

---

## 📝 详细测试用例

### 场景 A1：开发模式 - 首次使用（无密码）

**前置条件**：
- `CONFIG.auth.skip = true`
- 清除所有浏览器存储
- 应用从未设置过密码

**测试步骤**：
1. 访问 `http://localhost:3030`
2. 观察页面显示
3. 点击"开始初始化"按钮
4. 设置密码（例如：123456）
5. 完成钱包等其他初始化步骤
6. 点击"完成"按钮

**期望结果**：
```
✅ 显示初始化提示对话框
✅ 点击开始后显示完整初始化对话框
✅ 密码设置成功后继续显示初始化对话框（不跳转到锁屏）
✅ 完成所有步骤后直接进入主界面
✅ 控制台日志显示：
   - [PasswordFlowManager] 渲染: 显示初始化对话框
   - [InitializationDialog] handlePasscodeSet: 开始设置密码
   - [PasswordFlowManager] 渲染: 正在初始化，跳过锁屏检查
   - [PasswordFlowManager] 渲染: 刚完成初始化，跳过锁屏直接进入应用
```

**验证点**：
- ❌ 不应该在设置密码后立即跳转到锁屏
- ❌ 不应该要求用户重复输入密码
- ✅ 应该直接进入主界面

---

### 场景 A2：开发模式 - 已设置密码

**前置条件**：
- `CONFIG.auth.skip = true`
- 已完成初始化并设置密码
- 重新打开应用或刷新页面

**测试步骤**：
1. 访问 `http://localhost:3030`
2. 观察页面显示
3. 输入正确密码
4. 点击"解锁"按钮

**期望结果**：
```
✅ 直接显示锁屏页面
✅ 输入正确密码后进入主界面
✅ 控制台日志显示：
   - [PasswordFlowManager] 渲染: 正常锁屏流程
   - [LockGuard] 设置锁定状态
```

**验证点**：
- ✅ 应该显示锁屏页面
- ✅ 密码验证成功后进入主界面
- ❌ 不应该显示初始化对话框

---

### 场景 B1：生产模式 - 首次登录且无密码

**前置条件**：
- `CONFIG.auth.skip = false`
- 清除所有浏览器存储
- 应用从未设置过密码

**测试步骤**：
1. 访问 `http://localhost:3030`
2. 完成登录认证
3. 观察登录后的页面显示
4. 点击"开始初始化"按钮
5. 设置密码
6. 完成所有初始化步骤
7. 点击"完成"按钮

**期望结果**：
```
✅ 登录成功后显示初始化提示对话框
✅ 完成初始化后直接进入主界面（跳过锁屏）
✅ 控制台日志显示：
   - [PasswordFlowManager] 渲染: 显示初始化对话框
   - [PasswordFlowManager] 渲染: 刚完成初始化，跳过锁屏直接进入应用
```

**验证点**：
- ✅ 登录后应该显示初始化流程
- ✅ 初始化完成后直接进入主界面
- ❌ 不应该在初始化完成后要求重新输入密码

---

### 场景 B2：生产模式 - 首次登录且已设置密码

**前置条件**：
- `CONFIG.auth.skip = false`
- 已设置密码但未登录
- 清除认证状态但保留密码设置

**测试步骤**：
1. 访问 `http://localhost:3030`
2. 完成登录认证
3. 观察登录后的页面显示

**期望结果**：
```
✅ 登录成功后直接进入主界面（跳过锁屏 5分钟）
✅ 控制台日志显示：
   - [PasswordFlowManager] 渲染: 跳过锁屏，直接进入应用
   - [SkipLockScreenGuard] 启动 5分钟计时器
```

**验证点**：
- ✅ 登录后应该直接进入主界面
- ✅ 5分钟内不应该要求密码验证
- ❌ 不应该显示锁屏页面

---

### 场景 B3：生产模式 - 重新打开应用

**前置条件**：
- `CONFIG.auth.skip = false`
- 已登录且已设置密码
- 关闭浏览器后重新打开

**测试步骤**：
1. 访问 `http://localhost:3030`
2. 观察页面显示
3. 输入密码解锁

**期望结果**：
```
✅ 显示锁屏页面
✅ 输入正确密码后进入主界面
✅ 控制台日志显示：
   - [PasswordFlowManager] 渲染: 正常锁屏流程
```

**验证点**：
- ✅ 应该显示锁屏页面
- ✅ 需要密码验证才能进入主界面

---

### 场景 C1：安全性测试 - 初始化后离开应用

**前置条件**：
- 刚完成初始化，处于免锁屏状态
- `justCompletedInit = true`

**测试步骤**：
1. 完成初始化进入主界面
2. 切换到其他应用（Alt+Tab 或 Cmd+Tab）
3. 等待 2-3 秒后切换回来
4. 观察页面状态

**期望结果**：
```
✅ 切换回来后显示锁屏页面
✅ 需要重新输入密码
✅ 控制台日志显示：
   - [SkipLockScreenGuard] 清除所有跳过锁屏状态
   - [PasswordFlowManager] 渲染: 正常锁屏流程
```

**验证点**：
- ✅ 离开应用后应该立即清除免锁屏状态
- ✅ 返回后应该要求密码验证

---

### 场景 C2：安全性测试 - 初始化后超时

**前置条件**：
- 刚完成初始化，处于免锁屏状态
- `justCompletedInit = true`

**测试步骤**：
1. 完成初始化进入主界面
2. 保持应用活跃状态
3. 等待 5 分钟
4. 尝试进行需要密码验证的操作

**期望结果**：
```
✅ 5分钟后自动清除免锁屏状态
✅ 需要密码验证才能继续操作
✅ 控制台日志显示：
   - [SkipLockScreenGuard] 清除所有跳过锁屏状态
```

**验证点**：
- ✅ 5分钟后应该自动恢复锁屏保护
- ✅ 超时后的操作应该要求密码验证

---

### 场景 C3：安全性测试 - 窗口失去焦点

**前置条件**：
- 刚完成初始化，处于免锁屏状态
- `justCompletedInit = true`

**测试步骤**：
1. 完成初始化进入主界面
2. 点击浏览器外的其他窗口
3. 等待 2-3 秒后点击回应用窗口
4. 观察页面状态

**期望结果**：
```
✅ 窗口失去焦点后清除免锁屏状态
✅ 重新获得焦点后显示锁屏页面
✅ 控制台日志显示：
   - [SkipLockScreenGuard] 清除所有跳过锁屏状态
```

**验证点**：
- ✅ 失去焦点应该立即清除免锁屏状态
- ✅ 重新获得焦点后应该要求密码验证

---

### 场景 D1：异常情况 - 初始化过程中关闭对话框

**前置条件**：
- 正在进行初始化过程
- `isInitializing = true`

**测试步骤**：
1. 开始初始化流程
2. 在完成之前点击关闭按钮
3. 观察页面状态
4. 重新刷新页面

**期望结果**：
```
✅ 关闭对话框后重置所有初始化状态
✅ 刷新页面后重新显示初始化提示
✅ 控制台日志显示：
   - [PasswordFlowManager] handleInitClose: 关闭初始化对话框
   - 所有初始化状态被重置
```

**验证点**：
- ✅ 关闭对话框应该重置状态
- ✅ 重新访问应该能正常开始初始化

---

### 场景 D2：异常情况 - 密码设置失败

**前置条件**：
- 正在进行初始化过程
- 后端服务异常或网络问题

**测试步骤**：
1. 开始初始化流程
2. 尝试设置密码（模拟失败情况）
3. 观察错误处理
4. 重试密码设置

**期望结果**：
```
✅ 显示密码设置失败的错误信息
✅ 允许用户重试
✅ 不会跳转到主界面
✅ 保持在初始化对话框中
```

**验证点**：
- ✅ 应该显示明确的错误信息
- ✅ 应该允许用户重试
- ❌ 不应该在失败时跳转到主界面

---

### 场景 D3：异常情况 - 网络中断后恢复

**前置条件**：
- 应用正在运行
- 模拟网络中断

**测试步骤**：
1. 正常使用应用
2. 断开网络连接
3. 尝试进行需要网络的操作
4. 恢复网络连接
5. 重试操作

**期望结果**：
```
✅ 网络中断时显示相应错误信息
✅ 网络恢复后能正常工作
✅ 密码验证状态保持正确
```

**验证点**：
- ✅ 应该优雅处理网络异常
- ✅ 恢复后应该能正常工作
- ✅ 不应该影响密码验证流程

---

## 🔍 调试指南

### 关键日志标识
```javascript
// 初始化流程
[PasswordFlowManager] 渲染: 显示初始化对话框
[InitializationDialog] handlePasscodeSet: 开始设置密码
[PasswordFlowManager] 渲染: 正在初始化，跳过锁屏检查

// 完成初始化
[PasswordFlowManager] handleInitComplete: 初始化完成
[PasswordFlowManager] 渲染: 刚完成初始化，跳过锁屏直接进入应用

// 安全状态清除
[SkipLockScreenGuard] 清除所有跳过锁屏状态

// 正常锁屏流程
[PasswordFlowManager] 渲染: 正常锁屏流程
[LockGuard] 设置锁定状态
```

### 状态检查
在浏览器控制台中检查关键状态：
```javascript
// 检查密码状态
console.log('hasPassword:', hasPassword);

// 检查初始化状态
console.log('isInitializing:', isInitializing);
console.log('justCompletedInit:', justCompletedInit);

// 检查认证状态
console.log('authenticated:', authenticated);
console.log('justLoggedIn:', justLoggedIn);
```

### 常见问题排查

**问题 1：初始化后仍然显示锁屏**
- 检查 `justCompletedInit` 状态是否正确设置
- 确认 `SkipLockScreenGuard` 组件正确渲染
- 查看控制台是否有相关错误

**问题 2：免锁屏状态没有自动清除**
- 检查 `SkipLockScreenGuard` 的事件监听器
- 确认 `onClearState` 回调正确传递
- 验证计时器是否正常工作

**问题 3：初始化对话框不显示**
- 检查 `hasPassword` 状态
- 确认 `showInitDialog` 和 `isInitializing` 状态
- 验证渲染条件逻辑

---

## 📊 测试矩阵

| 场景 | 环境 | 密码状态 | 认证状态 | 期望行为 |
|------|------|----------|----------|----------|
| A1 | 开发 | 无 | 跳过 | 初始化 → 直接进入 |
| A2 | 开发 | 有 | 跳过 | 锁屏 → 验证 → 进入 |
| B1 | 生产 | 无 | 需登录 | 登录 → 初始化 → 直接进入 |
| B2 | 生产 | 有 | 需登录 | 登录 → 直接进入（5分钟） |
| B3 | 生产 | 有 | 已登录 | 锁屏 → 验证 → 进入 |

---

## 🎯 自动化测试建议

### 单元测试
```javascript
describe('PasswordFlowManager', () => {
  test('初始化完成后设置 justCompletedInit 状态', () => {
    // 测试 handleInitComplete 函数
  });
  
  test('justCompletedInit 状态下渲染 SkipLockScreenGuard', () => {
    // 测试渲染逻辑
  });
});
```

### 集成测试
```javascript
describe('初始化流程集成测试', () => {
  test('完整初始化流程不显示锁屏', () => {
    // 端到端测试初始化流程
  });
});
```

### E2E 测试
```javascript
describe('用户体验测试', () => {
  test('初始化后直接进入主界面', () => {
    // 使用 Playwright 或 Cypress 测试
  });
});
```

---

## 📋 测试检查清单

### 功能测试
- [ ] 开发模式初始化流程
- [ ] 生产模式初始化流程
- [ ] 正常锁屏流程
- [ ] 免锁屏时间窗口
- [ ] 状态清除机制

### 安全测试
- [ ] 离开应用后状态清除
- [ ] 超时后状态清除
- [ ] 失去焦点后状态清除
- [ ] 密码验证正确性

### 异常测试
- [ ] 网络异常处理
- [ ] 初始化中断处理
- [ ] 密码设置失败处理
- [ ] 状态恢复机制

### 用户体验测试
- [ ] 无重复密码输入
- [ ] 流程顺畅性
- [ ] 错误提示清晰
- [ ] 响应速度合理

---

**注意**：本文档应与代码实现保持同步，如发现测试结果与期望不符，请及时更新文档或修复代码。 