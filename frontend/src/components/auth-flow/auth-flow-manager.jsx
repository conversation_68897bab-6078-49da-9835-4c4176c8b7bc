import { useMemo } from 'react';

import { CONFIG } from 'src/global-config';

import { useAuthContext } from 'src/auth/hooks';

import { PasswordFlowManager } from './password-flow-manager';

// ----------------------------------------------------------------------

/**
 * 认证流程管理器
 *
 * 根据 CONFIG.auth.skip 配置决定是否跳过认证流程：
 * - auth.skip = true: 跳过认证，直接进入密码检查流程
 * - auth.skip = false: 需要认证，认证成功后进入密码检查流程
 */
export function AuthFlowManager({ children }) {
  const { authenticated, loading, justLoggedIn } = useAuthContext();

  const skipAuth = useMemo(() => CONFIG.auth.skip, []);

  // 开发/测试模式：跳过认证流程
  if (skipAuth) {
    return <PasswordFlowManager skipLockScreen={false}>{children}</PasswordFlowManager>;
  }

  // 认证状态加载中
  if (loading) {
    return null; // 或者返回一个加载组件
  }

  // 未认证：需要登录
  if (!authenticated) {
    // 在实际应用中，这里会重定向到登录页面
    // 由于当前 AuthGuard 会处理重定向，这里返回 null
    return null;
  }

  // 已认证：进入密码检查流程
  return <PasswordFlowManager skipLockScreen={justLoggedIn}>{children}</PasswordFlowManager>;
}
