import { useEffect } from 'react';

import { useAuthContext } from 'src/auth/hooks';

// ----------------------------------------------------------------------

/**
 * 跳过锁屏守卫组件
 *
 * 专门处理"刚登录"用户的锁屏跳过逻辑：
 * 1. 在指定时间后（5分钟）清除"刚登录"状态
 * 2. 监听页面可见性变化，用户离开应用时清除状态
 * 3. 提供安全的锁屏跳过机制
 * 4. 支持自定义状态清除回调
 */
export function SkipLockScreenGuard({ children, onClearState }) {
  const { clearJustLoggedIn } = useAuthContext();

  // 统一的状态清除函数
  const clearAllStates = () => {
    console.log('[SkipLockScreenGuard] 清除所有跳过锁屏状态');
    clearJustLoggedIn();
    onClearState?.();
  };

  useEffect(() => {
    // 5分钟后清除"刚登录"状态
    const timer = setTimeout(
      () => {
        clearAllStates();
      },
      5 * 60 * 1000
    ); // 5分钟

    return () => clearTimeout(timer);
  }, [clearJustLoggedIn, onClearState]);

  useEffect(() => {
    // 监听用户活动，如果用户离开应用再回来，清除状态
    const handleVisibilityChange = () => {
      if (document.hidden) {
        // 用户离开应用，清除所有状态
        clearAllStates();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [clearJustLoggedIn, onClearState]);

  useEffect(() => {
    // 监听窗口失去焦点事件，增强安全性
    const handleWindowBlur = () => {
      // 窗口失去焦点时，清除所有状态
      clearAllStates();
    };

    window.addEventListener('blur', handleWindowBlur);

    return () => {
      window.removeEventListener('blur', handleWindowBlur);
    };
  }, [clearJustLoggedIn, onClearState]);

  // 直接渲染子组件，不进行锁屏检查
  return <>{children}</>;
}
