import { useState, useEffect } from 'react';

import { useAppPassword } from 'src/hooks/use-app-password';

import { LockGuard } from 'src/components/auto-lock/lock-guard';
import InitializationDialog from 'src/components/initialization/dialog';
import InitializationLoading from 'src/components/initialization/loading';
import { InitializationPromptDialog } from 'src/components/initialization-prompt-dialog';

import { useAuthContext } from 'src/auth/hooks';

import { SkipLockScreenGuard } from './skip-lock-screen-guard';

// ----------------------------------------------------------------------

/**
 * 密码流程管理器
 *
 * 处理密码相关的流程：
 * 1. 检查是否已设置密码
 * 2. 未设置密码：显示初始化对话框
 * 3. 已设置密码：根据 skipLockScreen 决定是否跳过锁屏
 */
export function PasswordFlowManager({ children, skipLockScreen = false }) {
  const { clearJustLoggedIn } = useAuthContext();
  const { hasPassword, refreshPasswordStatus } = useAppPassword();

  const [showPromptDialog, setShowPromptDialog] = useState(false);
  const [showInitDialog, setShowInitDialog] = useState(false);
  const [isCheckingInit, setIsCheckingInit] = useState(false);
  const [skipInit, setSkipInit] = useState(false); // 用户取消初始化后直接进入主界面
  const [isInitializing, setIsInitializing] = useState(false); // 正在进行初始化过程
  const [justCompletedInit, setJustCompletedInit] = useState(false); // 刚完成初始化

  // 检查密码状态
  useEffect(() => {
    const checkPasswordStatus = async () => {
      setIsCheckingInit(true);

      try {
        // 刷新密码状态，确保获取最新状态
        const currentHasPassword = await refreshPasswordStatus();

        // 如果没有设置密码，显示初始化提示对话框
        if (!currentHasPassword) {
          setShowPromptDialog(true);
        }
      } catch (error) {
        console.error('检查密码状态失败:', error);
        // 出错时假设需要初始化
        setShowPromptDialog(true);
      }

      setIsCheckingInit(false);
    };

    // 延迟检查，确保设置已加载
    const timer = setTimeout(checkPasswordStatus, 200);

    return () => clearTimeout(timer);
  }, [refreshPasswordStatus]);

  // 处理用户确认进行初始化
  const handleConfirmInitialization = () => {
    console.log('[PasswordFlowManager] handleConfirmInitialization: 开始初始化');
    setShowPromptDialog(false);
    setShowInitDialog(true);
    setIsInitializing(true); // 标记开始初始化
  };

  // 处理用户取消初始化
  const handleCancelInitialization = () => {
    console.log('[PasswordFlowManager] handleCancelInitialization: 取消初始化');
    setShowPromptDialog(false);
    setSkipInit(true); // 设置跳过初始化，直接进入主界面
  };

  // 处理初始化完成
  const handleInitComplete = async () => {
    console.log('[PasswordFlowManager] handleInitComplete: 初始化完成');
    setShowInitDialog(false);
    setIsInitializing(false); // 标记初始化完成
    setJustCompletedInit(true); // 标记刚完成初始化，给用户免锁屏时间

    // 刷新密码状态，确保状态同步
    await refreshPasswordStatus();

    // 初始化完成后，清除"刚登录"状态
    clearJustLoggedIn();

    // 注意：5分钟后的状态清除由 SkipLockScreenGuard 组件处理
    // 这样可以确保在用户离开应用时也会清除状态，提供更好的安全性
  };

  // 处理初始化对话框关闭
  const handleInitClose = () => {
    console.log('[PasswordFlowManager] handleInitClose: 关闭初始化对话框');
    setShowInitDialog(false);
    setIsInitializing(false); // 取消初始化时也要重置状态
    setJustCompletedInit(false); // 取消时也要重置状态
  };

  // 如果正在检查初始化状态，显示加载状态
  if (isCheckingInit) {
    return <InitializationLoading />;
  }

  // 添加详细的状态日志
  console.log('[PasswordFlowManager] 当前状态:', {
    hasPassword,
    skipInit,
    showInitDialog,
    isInitializing,
    showPromptDialog,
    justCompletedInit,
  });

  // 如果没有设置密码，且未跳过初始化，显示初始化相关对话框
  // 或者正在进行初始化过程（即使已设置密码），也要继续显示初始化对话框
  if ((!hasPassword && !skipInit) || (showInitDialog && isInitializing)) {
    console.log('[PasswordFlowManager] 渲染: 显示初始化对话框');
    return (
      <>
        {/* 初始化提示对话框 - 让用户选择是否进行初始化 */}
        <InitializationPromptDialog
          open={showPromptDialog}
          onClose={handleCancelInitialization}
          onConfirm={handleConfirmInitialization}
        />

        {/* 完整的初始化对话框 - 只有用户确认后才显示 */}
        <InitializationDialog
          open={showInitDialog}
          onComplete={handleInitComplete}
          onClose={handleInitClose}
        />
      </>
    );
  }

  // 如果没有设置密码，但用户选择跳过初始化，直接进入主界面
  if (!hasPassword && skipInit) {
    console.log('[PasswordFlowManager] 渲染: 跳过初始化，直接进入主界面');
    return <>{children}</>;
  }

  // 如果正在初始化，暂时跳过锁屏检查
  if (isInitializing) {
    console.log('[PasswordFlowManager] 渲染: 正在初始化，跳过锁屏检查');
    return <>{children}</>;
  }

  // 如果刚完成初始化，给用户免锁屏时间，直接进入应用
  if (justCompletedInit) {
    console.log('[PasswordFlowManager] 渲染: 刚完成初始化，跳过锁屏直接进入应用');
    return (
      <SkipLockScreenGuard onClearState={() => setJustCompletedInit(false)}>
        {children}
      </SkipLockScreenGuard>
    );
  }

  // 已设置密码，根据 skipLockScreen 决定流程
  if (skipLockScreen) {
    console.log('[PasswordFlowManager] 渲染: 跳过锁屏，直接进入应用');
    // 跳过锁屏，直接进入应用
    return <SkipLockScreenGuard>{children}</SkipLockScreenGuard>;
  }

  // 正常锁屏流程 - 需要确保应用启动时进入锁定状态
  console.log('[PasswordFlowManager] 渲染: 正常锁屏流程');
  return <LockGuard forceInitialLock>{children}</LockGuard>;
}
