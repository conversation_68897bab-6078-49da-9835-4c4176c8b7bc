import { useNavigate } from 'react-router';
import { useRef, useState, useEffect, useCallback } from 'react';

import { useAppSettings } from 'src/hooks/use-app-settings';
import { useAppPassword } from 'src/hooks/use-app-password';

import { lockScreen } from 'src/components/auto-lock/lock-guard';

// ----------------------------------------------------------------------

// 默认的自动锁屏时间（毫秒）
const DEFAULT_IDLE_TIME = 1 * 60 * 1000; // 1分钟

// 全局的AutoLock重置回调
let globalResetAutoLock = null;

export default function AutoLock({ idleTime = DEFAULT_IDLE_TIME }) {
  const navigate = useNavigate();
  const { settings } = useAppSettings();
  const { hasPassword } = useAppPassword();
  const lastActivityRef = useRef(Date.now());
  const [resetTrigger, setResetTrigger] = useState(0);
  const [newAutoLockValue, setNewAutoLockValue] = useState(null);

  // 强制重置AutoLock（清除所有状态，重新开始）
  const forceReset = useCallback(
    (newValue = null) => {
      console.debug('[AutoLock] Force reset triggered, setting last activity to now');
      console.debug('[AutoLock] New value passed:', newValue);
      console.debug('[AutoLock] Current settings at reset time:', settings.security.autoLock);
      lastActivityRef.current = Date.now();
      // 如果传递了新值，保存它
      if (newValue !== null) {
        setNewAutoLockValue(newValue);
      }
      // 触发useEffect重新执行
      setResetTrigger((prev) => prev + 1);
    },
    [settings.security.autoLock]
  );

  // 注册全局重置回调
  useEffect(() => {
    globalResetAutoLock = forceReset;
    return () => {
      globalResetAutoLock = null;
    };
  }, [forceReset]);

  useEffect(() => {
    console.debug('[AutoLock] Effect triggered - settings changed');
    console.debug('[AutoLock] Has password:', hasPassword);
    console.debug('[AutoLock] Settings autoLock:', settings.security.autoLock);
    console.debug('[AutoLock] Full settings:', settings.security);

    // 重置活动时间
    lastActivityRef.current = Date.now();

    // 直接在这里计算，避免函数依赖问题
    let actualIdleTime = null;
    // 检查是否应该启用锁屏功能
    if (hasPassword) {
      // 优先使用传递的新值，否则使用settings中的值
      const autoLockMinutes =
        newAutoLockValue !== null ? newAutoLockValue : settings.security.autoLock;
      console.debug(
        '[AutoLock] Using autoLock value:',
        autoLockMinutes,
        '(from newValue:',
        newAutoLockValue,
        ', from settings:',
        settings.security.autoLock,
        ')'
      );

      // 如果autoLock设置为Never(-1)，不启用锁屏
      if (autoLockMinutes !== -1) {
        if (autoLockMinutes === 0) {
          actualIdleTime = 10 * 1000; // 10秒
        } else {
          actualIdleTime = autoLockMinutes * 60 * 1000;
        }
      }
    }

    // 如果新值与settings中的值相同，说明更新已完成，可以清除新值
    if (newAutoLockValue !== null && newAutoLockValue === settings.security.autoLock) {
      setNewAutoLockValue(null);
    }

    console.debug('[AutoLock] Calculated idle time:', actualIdleTime);

    // 如果不应该启用自动锁屏，直接返回
    if (actualIdleTime === null) {
      console.debug('[AutoLock] Auto lock disabled, exiting');
      return undefined;
    }

    // 添加用户活动事件监听器
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

    const handleActivity = () => {
      lastActivityRef.current = Date.now();
    };

    events.forEach((event) => {
      window.addEventListener(event, handleActivity);
    });

    console.debug('[AutoLock] Setting up timer with idle time:', actualIdleTime);

    // 设置定时器，定期检查用户是否空闲
    const timer = setInterval(() => {
      const currentTime = Date.now();
      const idleElapsed = currentTime - lastActivityRef.current;

      console.debug('[AutoLock] Timer check - elapsed:', idleElapsed, 'required:', actualIdleTime);

      if (idleElapsed >= actualIdleTime) {
        // 用户空闲时间超过设定值，锁定屏幕
        console.debug('[AutoLock] Triggering auto lock');
        lockScreen(navigate, window.location.pathname);
        clearInterval(timer);
      }
    }, 10000); // 每10秒检查一次

    // 清理函数
    return () => {
      // 移除事件监听器
      events.forEach((event) => {
        window.removeEventListener(event, handleActivity);
      });

      // 清除定时器
      clearInterval(timer);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigate, hasPassword, settings.security.autoLock, resetTrigger, newAutoLockValue]);

  // 此组件不渲染任何UI
  return null;
}

// 导出全局重置函数
export const resetAutoLock = (newValue = null) => {
  if (globalResetAutoLock) {
    globalResetAutoLock(newValue);
  }
};
