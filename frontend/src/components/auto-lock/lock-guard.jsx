import { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router';

import { paths } from 'src/routes/paths';

import { useAppSettings } from 'src/hooks/use-app-settings';
import { useAppPassword } from 'src/hooks/use-app-password';

import { SplashScreen } from 'src/components/loading-screen';

// ----------------------------------------------------------------------

// 锁屏状态存储键
export const LOCK_STORAGE_KEY = 'app_lock_status';
export const INITIAL_LOCK_TRIGGERED_KEY = 'app_initial_lock_triggered';

// 检查是否处于锁定状态
export const isLocked = () => {
  const lockStatus = sessionStorage.getItem(LOCK_STORAGE_KEY);
  return lockStatus === 'locked';
};

// 设置锁定状态
export const setLockStatus = (status) => {
  if (status) {
    sessionStorage.setItem(LOCK_STORAGE_KEY, 'locked');
  } else {
    sessionStorage.removeItem(LOCK_STORAGE_KEY);
  }
};

// 检查是否应该启用锁屏功能
export const shouldEnableLockScreen = (settings, hasPassword, forceEnable = false) => {
  // 如果没有设置密码，不启用锁屏
  if (!hasPassword) {
    return false;
  }

  // 如果强制启用，忽略autoLock设置
  if (forceEnable) {
    return true;
  }

  // 如果autoLock设置为Never(-1)，不启用锁屏
  if (settings.security.autoLock === -1) {
    return false;
  }

  return true;
};

// 锁定屏幕
export const lockScreen = (navigate, currentPath) => {
  console.debug('[LockGuard] Setting lock status to locked');
  setLockStatus(true);
  console.debug('[LockGuard] Lock status after setting:', sessionStorage.getItem(LOCK_STORAGE_KEY));
  console.debug('[LockGuard] Navigating to lock screen with return path:', currentPath);
  navigate(paths.lock, {
    state: { from: currentPath },
    replace: true,
  });
};

// 检查是否已触发过初始锁定
export const hasTriggeredInitialLock = () =>
  sessionStorage.getItem(INITIAL_LOCK_TRIGGERED_KEY) === 'true';

// 设置已触发初始锁定标记
export const setInitialLockTriggered = () => {
  sessionStorage.setItem(INITIAL_LOCK_TRIGGERED_KEY, 'true');
};

// 清除初始锁定标记（通常在应用重启时）
export const clearInitialLockTriggered = () => {
  sessionStorage.removeItem(INITIAL_LOCK_TRIGGERED_KEY);
};

// 解锁屏幕
export const unlockScreen = () => {
  console.debug('[LockGuard] Unlocking screen - before:', sessionStorage.getItem(LOCK_STORAGE_KEY));
  setLockStatus(false);
  console.debug('[LockGuard] Unlocking screen - after:', sessionStorage.getItem(LOCK_STORAGE_KEY));

  // 强制确保状态被清除
  sessionStorage.removeItem(LOCK_STORAGE_KEY);
  console.debug(
    '[LockGuard] Force unlock - final check:',
    sessionStorage.getItem(LOCK_STORAGE_KEY)
  );

  // 标记已经处理过初始锁定
  setInitialLockTriggered();
  console.debug('[LockGuard] Set initial lock triggered');
};

// ----------------------------------------------------------------------

export function LockGuard({ children, forceInitialLock = false }) {
  const navigate = useNavigate();
  const location = useLocation();
  const pathname = location.pathname;
  const { settings } = useAppSettings();
  const { hasPassword } = useAppPassword();

  const [isChecking, setIsChecking] = useState(true);

  const checkLockStatus = () => {
    console.debug('[LockGuard] Checking lock status...');
    console.debug('[LockGuard] Current path:', pathname);
    console.debug('[LockGuard] Has password:', hasPassword);
    console.debug('[LockGuard] Force initial lock:', forceInitialLock);
    console.debug('[LockGuard] Is locked:', isLocked());
    console.debug('[LockGuard] Auto lock setting:', settings.security.autoLock);

    // 如果当前已经在锁屏页面，不需要再次检查
    if (pathname === paths.lock) {
      console.debug('[LockGuard] Already on lock screen, skipping check');
      setIsChecking(false);
      return;
    }

    // 检查是否应该启用锁屏功能
    // 当 forceInitialLock 为 true 时，强制启用锁屏，忽略 autoLock 设置
    const persistedHasTriggered = hasTriggeredInitialLock();
    const effectiveForceInitialLock = forceInitialLock && !persistedHasTriggered;
    const shouldEnable = shouldEnableLockScreen(settings, hasPassword, effectiveForceInitialLock);
    console.debug('[LockGuard] Should enable lock screen:', shouldEnable);
    console.debug('[LockGuard] Effective force initial lock:', effectiveForceInitialLock);
    console.debug('[LockGuard] Has triggered initial lock (persisted):', persistedHasTriggered);

    if (!shouldEnable) {
      // 如果不应该启用锁屏，清除任何现有的锁定状态
      console.debug('[LockGuard] Lock screen disabled, clearing lock status');
      setLockStatus(false);
      setIsChecking(false);
      return;
    }

    // 如果强制初始锁定（且尚未触发过），或者检查到处于锁定状态
    if (effectiveForceInitialLock || isLocked()) {
      // 如果是首次强制锁定，标记已触发
      if (effectiveForceInitialLock) {
        console.debug('[LockGuard] Setting initial lock triggered to true');
        setInitialLockTriggered();
      }

      // 设置锁定状态并重定向到锁屏页面
      console.debug('[LockGuard] Locking screen due to effectiveForceInitialLock or isLocked()');
      lockScreen(navigate, pathname);
      return;
    }

    console.debug('[LockGuard] No lock needed, continuing to app');
    setIsChecking(false);
  };

  useEffect(() => {
    checkLockStatus();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathname, hasPassword, settings.security.autoLock, forceInitialLock]);

  if (isChecking) {
    return <SplashScreen />;
  }

  return <>{children}</>;
}
