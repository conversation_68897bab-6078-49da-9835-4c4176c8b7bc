import { useRef, useState, useEffect, useCallback } from 'react';

/**
 * 表格数据获取抽象 Hook
 * 负责统一管理表格数据的获取、状态和缓存
 *
 * @param {function} dataFetcher - 数据获取函数
 * @param {object} params - 查询参数 { page, pageSize, orderBy, order, searchValue }
 * @returns {object} 数据状态和控制方法
 */
export function useTableData(dataFetcher, params) {
  // 数据状态
  const [data, setData] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // 请求去重
  const requestIdRef = useRef(0);
  const abortControllerRef = useRef(null);

  // 使用ref保存最新的参数，避免fetchData频繁重新创建
  const paramsRef = useRef(params);
  paramsRef.current = params;

  // 数据获取函数
  const fetchData = useCallback(async () => {
    if (!dataFetcher || typeof dataFetcher !== 'function') {
      console.error('useTableData: dataFetcher is required and must be a function');
      return;
    }

    // 生成唯一请求ID
    const requestId = ++requestIdRef.current;

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    // 创建新的中断控制器
    abortControllerRef.current = new AbortController();

    try {
      setLoading(true);
      setError(null);

      // 调用数据获取函数，使用ref中的最新参数
      const currentParams = paramsRef.current;
      const [dataResult, totalResult] = await dataFetcher(
        currentParams.page,
        currentParams.pageSize,
        currentParams.orderBy,
        currentParams.order,
        currentParams.searchValue
      );

      // 确保是最新的请求结果
      if (requestId === requestIdRef.current) {
        setData(dataResult || []);
        setTotalCount(totalResult || 0);
      }
    } catch (err) {
      // 只处理最新请求的错误，忽略被中断的请求
      if (requestId === requestIdRef.current && err.name !== 'AbortError') {
        console.error('Table data fetch error:', err);
        setError(err);
        setData([]);
        setTotalCount(0);
      }
    } finally {
      if (requestId === requestIdRef.current) {
        setLoading(false);
      }
    }
  }, [dataFetcher]); // 只依赖dataFetcher，参数变化通过paramsRef.current获取

  // 手动刷新数据
  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  // 清理函数
  const cleanup = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
  }, []);

  // 参数变化时自动触发数据获取
  useEffect(() => {
    fetchData();
  }, [params.page, params.pageSize, params.orderBy, params.order, params.searchValue, fetchData]);

  return {
    // 数据状态
    data,
    totalCount,
    loading,
    error,

    // 控制方法
    refetch,
    cleanup,

    // 内部方法（供其他hook使用）
    fetchData,
  };
}
