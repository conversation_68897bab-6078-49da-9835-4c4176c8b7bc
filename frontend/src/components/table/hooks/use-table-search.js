import { useState, useCallback } from 'react';
import { useDebounce } from 'minimal-shared/hooks';

/**
 * 表格搜索功能Hook
 * @param {string} initialValue - 初始搜索值
 * @param {number} debounceDelay - 防抖延迟时间（毫秒）
 * @returns {Object} 搜索相关状态和方法
 */
export function useTableSearch(initialValue = '', debounceDelay = 300) {
  const [searchValue, setSearchValue] = useState(initialValue);
  const debouncedSearchValue = useDebounce(searchValue, debounceDelay);

  const handleSearch = useCallback((event) => {
    const value = event.target.value;
    setSearchValue(value);
  }, []);

  const handleClearSearch = useCallback(() => {
    setSearchValue('');
  }, []);

  const resetSearch = useCallback(() => {
    setSearchValue(initialValue);
  }, [initialValue]);

  return {
    searchValue,
    debouncedSearchValue,
    handleSearch,
    handleClearSearch,
    resetSearch,
    setSearchValue,
  };
}
