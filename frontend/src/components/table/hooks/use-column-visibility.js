import { useMemo, useState, useCallback } from 'react';

import { useLocalStorage } from 'src/hooks/use-local-storage';

// ----------------------------------------------------------------------

/**
 * 列可见性、顺序和持久化管理 Hook
 * @param {Array} columns - 原始列定义数组
 * @param {string} tableId - 表格的唯一ID，用于 localStorage 的 key
 */
export function useColumnVisibility(columns, tableId = 'default') {
  const [columnMenuAnchor, setColumnMenuAnchor] = useState(null);

  const storageKey = `table-column-settings-${tableId}`;

  const [storedSettings, setStoredSettings] = useLocalStorage(storageKey, {
    // visibility: {}, // 默认值由下面逻辑处理
    // order: [],
  });

  // 初始化或合并列顺序
  const columnOrder = useMemo(() => {
    const initialOrder = columns.map((c) => c.id);
    const savedOrder = storedSettings.order || initialOrder;
    const newColumns = initialOrder.filter((id) => !savedOrder.includes(id));
    return [...savedOrder, ...newColumns];
  }, [columns, storedSettings.order]);

  // 初始化或合并列可见性
  const columnVisibility = useMemo(() => {
    const initialVisibility = {};
    columns.forEach((c) => {
      if (c.isHidden) {
        initialVisibility[c.id] = false;
      } else {
        initialVisibility[c.id] = true; // 默认全部可见
      }
    });

    return { ...initialVisibility, ...(storedSettings.visibility || {}) };
  }, [columns, storedSettings.visibility]);

  // 更新 localStorage 的回调
  const handleColumnVisibilityChange = useCallback(
    (columnId) => {
      const newVisibility = {
        ...columnVisibility,
        [columnId]: !columnVisibility[columnId],
      };
      setStoredSettings({
        ...storedSettings,
        visibility: newVisibility,
      });
    },
    [columnVisibility, storedSettings, setStoredSettings]
  );

  const handleColumnReorder = useCallback(
    (newOrderedColumns) => {
      const newOrder = newOrderedColumns.map((c) => c.id);
      setStoredSettings({
        ...storedSettings,
        order: newOrder,
      });
    },
    [storedSettings, setStoredSettings]
  );

  // 菜单控制
  const handleColumnMenuOpen = useCallback((event) => {
    setColumnMenuAnchor(event.currentTarget);
  }, []);

  const handleColumnMenuClose = useCallback(() => {
    setColumnMenuAnchor(null);
  }, []);

  // 根据顺序和可见性计算最终显示的列
  const visibleColumns = useMemo(
    () =>
      columnOrder
        .map((id) => columns.find((c) => c.id === id))
        .filter((column) => column && columnVisibility[column.id] !== false),
    [columns, columnOrder, columnVisibility]
  );

  // 按顺序排列的所有列（用于列管理器）
  const orderedColumns = useMemo(
    () => columnOrder.map((id) => columns.find((c) => c.id === id)).filter(Boolean),
    [columns, columnOrder]
  );

  return {
    // state
    columnVisibility,
    columnOrder,
    visibleColumns,
    orderedColumns,

    // menu
    columnMenuAnchor,
    columnMenuOpen: Boolean(columnMenuAnchor),

    // handlers
    handleColumnVisibilityChange,
    handleColumnReorder,
    handleColumnMenuOpen,
    handleColumnMenuClose,
  };
}
