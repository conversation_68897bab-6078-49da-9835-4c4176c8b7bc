import { useEffect, useCallback } from 'react';

import { useTable } from './use-table';
import { useTableData } from './use-table-data';
import { useTableSearch } from './use-table-search';
import { useColumnVisibility } from './use-column-visibility';

/**
 * 表格业务逻辑集成 Hook
 * 整合所有表格相关的状态和逻辑，提供统一的接口
 *
 * @param {object} options - 配置选项
 * @param {function} options.dataFetcher - 数据获取函数
 * @param {object} options.tableConfig - 表格基础配置
 * @param {object} options.searchConfig - 搜索配置
 * @param {object} options.columnsConfig - 列配置
 * @param {string} options.tableId - 表格唯一标识
 * @param {function} options.onSelectionChange - 选择变化回调
 * @param {function} options.onDelete - 删除回调
 * @returns {object} 统一的表格状态和方法
 */
export function useTableLogic(options) {
  const {
    dataFetcher,
    tableConfig = {},
    searchConfig = {},
    columnsConfig = {},
    tableId = 'default',
    onSelectionChange,
    onDelete,
  } = options;

  // 基础表格状态
  const table = useTable({
    defaultDense: true,
    defaultRowsPerPage: 20,
    defaultCurrentPage: 0,
    defaultOrderBy: 'id',
    defaultOrder: 'desc',
    ...tableConfig,
  });

  // 搜索状态
  const search = useTableSearch(searchConfig.defaultValue || '', searchConfig.debounceDelay);

  // 列管理状态
  const columns = useColumnVisibility(columnsConfig.columns || [], tableId);

  // 数据获取状态
  const tableData = useTableData(dataFetcher, {
    page: table.page,
    pageSize: table.rowsPerPage,
    orderBy: table.orderBy,
    order: table.order,
    searchValue: search.debouncedSearchValue,
  });

  // 选择变化通知
  useEffect(() => {
    if (onSelectionChange) {
      onSelectionChange(table.selected);
    }
  }, [table.selected, onSelectionChange]);

  // 搜索重置页码
  useEffect(() => {
    table.onResetPage();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search.debouncedSearchValue, table.onResetPage]);

  // 数据获取现在由 use-table-data 内部的 useEffect 自动处理
  // 不需要在这里手动触发

  // 批量删除处理
  const handleBatchDelete = useCallback(async () => {
    if (!onDelete || table.selected.length === 0) return;

    try {
      await onDelete(table.selected);
      table.onSelectAllRows(false, []); // 清空选择
      tableData.refetch(); // 刷新数据
    } catch (error) {
      console.error('批量删除失败:', error);
    }
  }, [onDelete, table, tableData]);

  // 搜索处理（包含页码重置）
  const handleSearchWithReset = useCallback(
    (event) => {
      search.handleSearch(event);
      table.onResetPage();
    },
    [search, table]
  );

  // 清空搜索处理
  const handleClearSearchWithReset = useCallback(() => {
    search.handleClearSearch();
    table.onResetPage();
    table.onSelectAllRows(false, []); // 清空选中项
  }, [search, table]);

  // 暴露给父组件的方法
  const getImperativeHandle = useCallback(
    () => ({
      refetch: tableData.refetch,
      clearSelection: () => table.onSelectAllRows(false, []),
      getSelectedItems: () => table.selected,
    }),
    [tableData, table]
  );

  return {
    // 基础状态
    table,
    search,
    columns,
    data: tableData,

    // 统一的操作方法
    actions: {
      handleBatchDelete,
      handleSearchWithReset,
      handleClearSearchWithReset,
      refetch: tableData.refetch,
    },

    // 工具方法
    utils: {
      getImperativeHandle,
      cleanup: tableData.cleanup,
    },
  };
}
