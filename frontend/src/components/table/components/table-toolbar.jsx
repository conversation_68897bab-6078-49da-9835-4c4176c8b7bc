import { Box, Button, Checkbox, Typography } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import { TableSearchBar } from '../table-search-bar';
import { TableColumnManager } from '../table-column-manager';

/**
 * 表格工具栏组件
 * 包含搜索栏、列管理器、批量操作等
 */
export function TableToolbar({
  search,
  columns,
  selectedActions = [],
  onDelete,
  selected = [],
  tableData = [],
  enableSelection = true,
  onSelectAllRows,
  t,
}) {
  const hasSelected = selected.length > 0;

  return (
    <Box sx={{ position: 'relative' }}>
      {/* 搜索框区域 */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          gap: 2,
          // 当有选中项时，隐藏搜索框
          ...(enableSelection && hasSelected && { display: 'none' }),
        }}
      >
        <TableSearchBar
          value={search.searchValue}
          onChange={search.handleSearchWithReset}
          onClear={search.handleClearSearchWithReset}
          loading={search.loading}
          debouncedValue={search.debouncedSearchValue}
          totalCount={search.totalCount}
          placeholder={t('common:tips.search')}
          sx={{ flex: 1 }}
        />

        <TableColumnManager
          columns={columns.orderedColumns}
          visibility={columns.columnVisibility}
          onVisibilityChange={columns.handleColumnVisibilityChange}
          onColumnReorder={columns.handleColumnReorder}
          anchorEl={columns.columnMenuAnchor}
          open={columns.columnMenuOpen}
          onClose={columns.handleColumnMenuClose}
          onOpen={columns.handleColumnMenuOpen}
        />
      </Box>

      {/* 选中操作区域 */}
      {enableSelection && hasSelected && (
        <Box
          sx={{
            py: 2.5,
            px: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 2,
            bgcolor: 'primary.lighter',
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Checkbox
              indeterminate={!!selected.length && selected.length < tableData.length}
              checked={!!tableData.length && selected.length === tableData.length}
              onChange={(event) => {
                const allIds = tableData.map((row) => row.ID || row.id);
                onSelectAllRows(event.target.checked, allIds);
              }}
              slotProps={{
                input: {
                  id: 'deselect-all-checkbox',
                  'aria-label': 'Deselect all checkbox',
                },
              }}
            />
            <Typography
              variant="subtitle2"
              sx={{
                color: 'primary.main',
                fontWeight: 600,
              }}
            >
              {selected.length} selected
            </Typography>
          </Box>

          {/* 操作按钮组 */}
          <Box sx={{ display: 'flex', gap: 1 }}>
            {onDelete && (
              <Button
                size="small"
                color="error"
                variant="text"
                startIcon={<Iconify icon="solar:trash-bin-trash-bold" />}
                onClick={onDelete}
              >
                {t('common:action.delete')}
              </Button>
            )}
            {selectedActions.map((actionItem) => (
              <Button
                key={actionItem.key}
                size="small"
                color={actionItem.color || 'primary'}
                variant={actionItem.variant || 'text'}
                startIcon={actionItem.icon && <Iconify icon={actionItem.icon} />}
                onClick={() => actionItem.onClick(selected)}
                {...(actionItem.buttonProps || {})}
              >
                {actionItem.label}
              </Button>
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
}
