import {
  Box,
  Select,
  Switch,
  MenuItem,
  Pagination,
  Typography,
  FormControl,
  FormControlLabel,
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';

/**
 * 表格分页组件
 * 负责渲染分页控件、行数选择器、密度切换等
 */
export function TablePagination({ table, totalCount, t }) {
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'));
  const isXL = useMediaQuery(theme.breakpoints.up('xl'));
  const is2K = useMediaQuery('(min-width:1920px)');
  const is4K = useMediaQuery('(min-width:2560px)');

  // 分页选项
  const paginationOptions = (() => {
    if (is4K) return [25, 50, 75, 100];
    if (is2K) return [20, 30, 50, 75];
    if (isXL) return [15, 25, 40, 60];
    if (isDesktop) return [10, 20, 30, 50];
    return [5, 10, 15, 25];
  })();

  // 计算总页数
  const totalPages = Math.ceil(totalCount / table.rowsPerPage);

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2 },
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        borderTop: 1,
        borderColor: 'divider',
        flexDirection: { xs: 'column', md: 'row' },
        gap: { xs: 1, sm: 2 },
      }}
    >
      {/* 左侧控制区域 */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: { xs: 1, sm: 2 },
          flexDirection: { xs: 'column', sm: 'row' },
          width: { xs: '100%', md: 'auto' },
        }}
      >
        {/* Dense 开关和每页行数控制 */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 1, sm: 2 } }}>
          <FormControlLabel
            label="Dense"
            control={<Switch checked={table.dense} onChange={table.onChangeDense} size="small" />}
            sx={{ mr: 0 }}
          />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Typography variant="body2" color="text.secondary" sx={{ whiteSpace: 'nowrap' }}>
              {t('common:table.rowsPerPage')}:
            </Typography>
            <FormControl size="small" sx={{ minWidth: 60 }}>
              <Select
                value={table.rowsPerPage}
                onChange={table.onChangeRowsPerPage}
                sx={{
                  '& .MuiSelect-select': {
                    py: 0.5,
                    px: 1,
                  },
                }}
              >
                {paginationOptions.map((option) => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        </Box>

        {/* 数据统计信息 */}
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            whiteSpace: 'nowrap',
            display: { xs: 'none', sm: 'block' },
          }}
        >
          {t('common:table.showing')} {table.page * table.rowsPerPage + 1}-
          {Math.min((table.page + 1) * table.rowsPerPage, totalCount)} {t('common:table.of')}{' '}
          {totalCount}
        </Typography>
      </Box>

      {/* 右侧分页区域 */}
      <Box
        sx={{
          display: 'flex',
          justifyContent: { xs: 'center', md: 'flex-end' },
          width: { xs: '100%', md: 'auto' },
        }}
      >
        <Pagination
          count={totalPages}
          page={table.page + 1} // MUI Pagination 从 1 开始，useTable 从 0 开始
          onChange={(event, page) => table.onChangePage(event, page - 1)} // 转换回 0 索引
          color="primary"
          size="small"
          showFirstButton={totalPages > 7}
          showLastButton={totalPages > 7}
          siblingCount={0}
          boundaryCount={1}
          sx={{
            '& .MuiPagination-ul': {
              flexWrap: 'nowrap',
              justifyContent: 'center',
            },
          }}
        />
      </Box>
    </Box>
  );
}
