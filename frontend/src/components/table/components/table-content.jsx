import { useMemo, useCallback } from 'react';

import { Box, Table, TableRow, Checkbox, TableBody, TableCell } from '@mui/material';

import { Scrollbar } from 'src/components/scrollbar';

import { TableNoData } from '../table-no-data';
import { TableSkeleton } from '../table-skeleton';
import { TableHeadCustom } from '../table-head-custom';

/**
 * 表格内容区域组件
 * 负责渲染表格的主体内容
 */
export function TableContent({
  data,
  table,
  columns,
  onRowClick,
  enableSelection = true,
  enableStickyLastColumn = true,
  noDataText,
  emptyComponent,
  emptyProps = {},
  tableProps = {},
}) {
  // 计算表格总宽度
  const totalTableWidth = useMemo(() => {
    const containerWidth = window.innerWidth;
    let calculatedWidth = 0;

    // 添加选择列的宽度
    if (enableSelection) {
      calculatedWidth += 58;
    }

    // 添加每个可见列的宽度
    columns.visibleColumns.forEach((column) => {
      if (column.width) {
        calculatedWidth += column.width;
      } else if (column.minWidth) {
        calculatedWidth += column.minWidth;
      } else {
        calculatedWidth += 120;
      }
    });

    const availableWidth = containerWidth - 80;
    return calculatedWidth < availableWidth ? '100%' : calculatedWidth;
  }, [columns.visibleColumns, enableSelection]);

  // 处理行选择
  const handleSelectRow = useCallback(
    (id) => {
      table.onSelectRow(id);
    },
    [table]
  );

  // 渲染表格行
  const renderTableRows = useCallback(() => {
    if (data.loading || !data.data || data.data.length === 0) return null;

    return data.data.map((row, index) => {
      const rowKey = row.ID || row.id || `row-${index}`;
      const selected = table.selected.includes(rowKey);

      return (
        <TableRow
          hover
          key={rowKey}
          selected={selected}
          onClick={() => onRowClick && onRowClick(row)}
          sx={{
            cursor: onRowClick ? 'pointer' : 'default',
            '&:hover': onRowClick ? { backgroundColor: 'action.hover' } : {},
          }}
        >
          {enableSelection && (
            <TableCell
              padding="checkbox"
              sx={{
                position: 'sticky',
                left: 0,
                backgroundColor: 'background.paper',
                zIndex: 1,
              }}
            >
              <Checkbox
                checked={selected}
                onClick={(e) => {
                  e.stopPropagation();
                  handleSelectRow(rowKey);
                }}
                slotProps={{
                  input: {
                    'aria-labelledby': `row-${rowKey}-checkbox`,
                    'aria-label': `选择第${index + 1}行`,
                  },
                }}
              />
            </TableCell>
          )}
          {columns.visibleColumns.map((column, columnIndex) => {
            const isLastColumn = columnIndex === columns.visibleColumns.length - 1;
            const isActionColumn = column.id === 'action' || column.id === 'actions';
            const shouldStick = enableStickyLastColumn && (isLastColumn || isActionColumn);

            return (
              <TableCell
                key={`${rowKey}-${column.id}`}
                align={column.align || 'left'}
                sx={{
                  // 强制尺寸设置
                  ...(column.width && {
                    width: column.width,
                    maxWidth: column.width,
                    minWidth: column.width,
                  }),
                  ...(column.minWidth && !column.width && { minWidth: column.minWidth }),
                  ...(column.maxWidth && { maxWidth: column.maxWidth }),

                  // 强制文本截断样式
                  whiteSpace: 'nowrap',
                  overflow: 'hidden !important',
                  textOverflow: 'ellipsis',

                  // 确保内容不会溢出
                  '& *': {
                    maxWidth: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  },

                  // 固定最后一列的样式
                  ...(shouldStick && {
                    position: 'sticky',
                    right: 0,
                    backgroundColor: 'background.paper',
                    zIndex: 1,
                    boxShadow: 'inset 1px 0 0 0 rgba(145, 158, 171, 0.16)',
                  }),
                }}
              >
                {column.renderCell ? column.renderCell(row, index) : row[column.id]}
              </TableCell>
            );
          })}
        </TableRow>
      );
    });
  }, [
    data.data,
    data.loading,
    table.selected,
    onRowClick,
    enableSelection,
    handleSelectRow,
    columns.visibleColumns,
    enableStickyLastColumn,
  ]);

  // 计算总列数
  const totalColumns = columns.visibleColumns.length + (enableSelection ? 1 : 0);

  return (
    <Box sx={{ overflow: 'hidden' }}>
      <Scrollbar
        sx={{
          height: {
            xs: 'calc(100vh - 384px)',
            sm: 'calc(100vh - 364px)',
            md: 'calc(100vh - 344px)',
            lg: 'calc(100vh - 324px)',
            xl: 'calc(100vh - 304px)',
          },
          maxHeight: {
            xs: '400px',
            sm: '500px',
            md: '600px',
            lg: '800px',
            xl: '1000px',
          },
          minHeight: '300px',
          overflow: 'auto',
          width: '100%',
          '& .simplebar-content-wrapper': {
            overflow: 'auto !important',
          },
          '& .simplebar-content': {
            height: '100% !important',
            width: typeof totalTableWidth === 'number' ? 'max-content' : '100%',
          },
        }}
      >
        <Table
          stickyHeader
          size={table.dense ? 'small' : 'medium'}
          sx={{
            ...(typeof totalTableWidth === 'number'
              ? {
                  minWidth: totalTableWidth,
                  width: totalTableWidth,
                  tableLayout: 'fixed',
                }
              : {
                  width: '100%',
                  tableLayout: 'auto',
                }),
            '& .MuiTableRow-root': {
              height: table.dense ? 55 : 58,
            },
            '& .MuiTableCell-root': {
              padding: table.dense ? '12px 16px' : '14px 16px',
            },
          }}
          {...tableProps}
        >
          <TableHeadCustom
            order={table.order}
            orderBy={table.orderBy}
            headCells={columns.visibleColumns}
            rowCount={data.data?.length || 0}
            numSelected={table.selected.length}
            onSort={table.onSort}
            onSelectAllRows={
              enableSelection
                ? (checked) => {
                    const allIds = (data.data || []).map((row) => row.ID || row.id);
                    table.onSelectAllRows(checked, allIds);
                  }
                : null
            }
            enableStickyLastColumn={enableStickyLastColumn}
          />
          <TableBody>
            {data.loading ? (
              <TableSkeleton rowCount={5} cellCount={totalColumns} />
            ) : data.data && data.data.length > 0 ? (
              renderTableRows()
            ) : (
              (() => {
                const EmptyComponent = emptyComponent || TableNoData;
                return (
                  <EmptyComponent
                    notFound
                    title={noDataText}
                    colSpan={totalColumns}
                    sx={{ py: 10 }}
                    {...emptyProps}
                  />
                );
              })()
            )}
          </TableBody>
        </Table>
      </Scrollbar>
    </Box>
  );
}
