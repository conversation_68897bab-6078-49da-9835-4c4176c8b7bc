import {
  useSensor,
  DndContext,
  useSensors,
  closestCenter,
  PointerSensor,
  KeyboardSensor,
} from '@dnd-kit/core';
import {
  arrayMove,
  useSortable,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';

import {
  Box,
  List,
  Popover,
  Divider,
  ListItem,
  Typography,
  IconButton,
  ListItemText,
} from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

// 可拖拽的列表项组件
function SortableListItem({ column, visibility, onVisibilityChange, canHide }) {
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: column.id,
    disabled: !canHide,
  });

  // 限制拖拽为仅垂直方向
  const style = {
    transform: transform ? `translate3d(0, ${transform.y}px, 0)` : undefined,
    transition,
  };

  const isVisible = visibility[column.id] !== false;

  // 处理可见性切换
  const handleVisibilityToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    onVisibilityChange(column.id);
  };

  return (
    <ListItem
      ref={setNodeRef}
      style={style}
      sx={{
        opacity: !canHide ? 0.6 : isDragging ? 0.8 : 1,
        cursor: 'default', // 改为默认光标，拖拽手柄有自己的光标
        borderRadius: 1,
        mb: 0.5,
        '&:hover': {
          backgroundColor: 'action.hover',
        },
        ...(isDragging && {
          zIndex: 1000,
          backgroundColor: 'background.paper',
          boxShadow: 3,
          borderRadius: 1,
          border: '1px solid',
          borderColor: 'primary.main',
        }),
      }}
    >
      {/* 排序状态图标和拖拽手柄 */}
      {canHide ? (
        <Box
          className="drag-handle"
          sx={{
            mr: 1.5,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            color: 'text.disabled',
            cursor: 'grab',
            transition: 'color 0.2s ease-in-out',
            p: 0.5,
            borderRadius: 1,
            '&:hover': {
              color: 'primary.main',
              backgroundColor: 'action.hover',
            },
            '&:active': {
              cursor: 'grabbing',
            },
          }}
          {...attributes}
          {...listeners}
        >
          <Iconify icon="jam:align-justify" width={18} height={18} />
        </Box>
      ) : (
        <Box
          sx={{
            mr: 1.5,
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            color: 'text.disabled',
            p: 0.5,
          }}
        >
          <Iconify icon="solar:lock-keyhole-bold" width={14} height={14} />
        </Box>
      )}

      <ListItemText
        primary={column.label}
        sx={{
          flex: 1,
          '& .MuiListItemText-primary': {
            fontSize: '0.875rem',
            fontWeight: isVisible ? 600 : 400,
            color: isVisible ? 'text.primary' : 'text.secondary',
            transition: 'all 0.2s ease-in-out',
          },
        }}
      />

      {/* 可见性状态图标 - 可点击切换 */}
      <Box
        sx={{
          cursor: canHide ? 'pointer' : 'default',
          p: 0.5,
          borderRadius: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          '&:hover': canHide
            ? {
                backgroundColor: 'action.hover',
              }
            : {},
        }}
        onClick={canHide ? handleVisibilityToggle : undefined}
      >
        <Iconify
          icon={isVisible ? 'solar:eye-bold' : 'solar:eye-closed-bold'}
          sx={{
            color: isVisible ? 'primary.main' : 'text.disabled',
            width: 18,
            height: 18,
            transition: 'color 0.2s ease-in-out',
          }}
        />
      </Box>
    </ListItem>
  );
}

/**
 * 表格列管理组件
 * @param {Array} columns - 列定义数组
 * @param {Object} visibility - 列可见性状态对象
 * @param {function} onVisibilityChange - 列可见性变化回调
 * @param {function} onColumnReorder - 列重新排序回调
 * @param {Element} anchorEl - 弹出菜单锚点元素
 * @param {boolean} open - 弹出菜单打开状态
 * @param {function} onClose - 弹出菜单关闭回调
 * @param {function} onOpen - 弹出菜单打开回调（用于触发按钮）
 */
export function TableColumnManager({
  columns,
  visibility,
  onVisibilityChange,
  onColumnReorder,
  anchorEl,
  open,
  onClose,
  onOpen,
}) {
  const { t } = useTranslate('common');

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽8px才激活
        tolerance: 5, // 容忍度
        delay: 100, // 延迟100ms激活，避免与点击冲突
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event) => {
    // 拖拽开始时的处理
    console.log('Drag started for:', event.active.id);
  };

  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      const oldIndex = columns.findIndex((column) => column.id === active.id);
      const newIndex = columns.findIndex((column) => column.id === over.id);

      const newColumns = arrayMove(columns, oldIndex, newIndex);

      // 调用重新排序回调
      if (onColumnReorder) {
        onColumnReorder(newColumns);
      }
    }
  };

  // 获取可排序的列ID（排除固定列）
  const sortableColumnIds = columns
    .filter((column) => {
      // 判断是否为操作列（id 或 action 列固定不能隐藏）
      const isActionColumn =
        column.id === 'id' || column.id === 'action' || column.id === 'actions';
      // 判断是否允许隐藏（检查 allowHiding 属性，默认为 true）
      const allowHiding = column.allowHiding !== false;
      // 最终判断是否可以隐藏/排序
      return !isActionColumn && allowHiding;
    })
    .map((column) => column.id);

  return (
    <>
      {/* 列设置按钮 */}
      <IconButton onClick={onOpen} title={t('common:table.columnSettings')} sx={{ mr: 2 }}>
        <Iconify icon="solar:list-bold" />
      </IconButton>

      {/* 列设置弹出菜单 */}
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={onClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              width: 320,
              maxHeight: 480,
              overflow: 'hidden',
            },
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <Box
            sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}
          >
            <Typography variant="h6">{t('common:table.columnSettings')}</Typography>
          </Box>

          <Divider sx={{ my: 2 }} />

          {/* 列选择列表 */}
          <DndContext
            sensors={sensors}
            collisionDetection={closestCenter}
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
          >
            <SortableContext items={sortableColumnIds} strategy={verticalListSortingStrategy}>
              <List
                sx={{
                  py: 0,
                  maxHeight: 400,
                  overflow: 'auto',
                  overflowX: 'hidden', // 隐藏横向滚动条
                  '&::-webkit-scrollbar': {
                    display: 'none',
                  },
                  scrollbarWidth: 'none', // Firefox
                  msOverflowStyle: 'none', // IE
                }}
              >
                {columns.map((column) => {
                  // 判断是否为操作列（id 或 action 列固定不能隐藏）
                  const isActionColumn =
                    column.id === 'id' || column.id === 'action' || column.id === 'actions';

                  // 判断是否允许隐藏（检查 allowHiding 属性，默认为 true）
                  const allowHiding = column.allowHiding !== false;

                  // 最终判断是否可以隐藏
                  const canHide = !isActionColumn && allowHiding;

                  return (
                    <SortableListItem
                      key={column.id}
                      column={column}
                      visibility={visibility}
                      onVisibilityChange={onVisibilityChange}
                      canHide={canHide}
                    />
                  );
                })}
              </List>
            </SortableContext>
          </DndContext>
        </Box>
      </Popover>
    </>
  );
}
