# TableView 组件架构文档

> 基于分层架构的现代化表格组件系统

## 📖 概述

TableView 组件采用分层架构设计，实现了数据层、业务逻辑层和展示层的完全分离，提供了高性能、易维护、可扩展的表格解决方案。

## 🏗️ 架构设计

### 分层架构图
```
┌─────────────────────────────────────────┐
│            展示层 (Presentation)         │
│  ┌─────────────────────────────────────┐ │
│  │        TableView (主组件)           │ │
│  │  ┌─────────────────────────────────┐ │ │
│  │  │     子组件模块化               │ │ │
│  │  │  • TableToolbar               │ │ │
│  │  │  • TableContent               │ │ │
│  │  │  • TablePagination            │ │ │
│  │  └─────────────────────────────────┘ │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                     ↑
┌─────────────────────────────────────────┐
│          业务逻辑层 (Business Logic)      │
│  ┌─────────────────────────────────────┐ │
│  │       use-table-logic.js           │ │
│  │   • 整合所有表格相关逻辑            │ │
│  │   • 统一状态管理                   │ │
│  │   • 操作方法封装                   │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                     ↑
┌─────────────────────────────────────────┐
│             数据层 (Data Layer)          │
│  ┌─────────────────────────────────────┐ │
│  │        use-table-data.js           │ │
│  │   • 数据获取抽象                   │ │
│  │   • 请求去重机制                   │ │
│  │   • 状态管理                       │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 📁 文件结构

```
src/components/table/
├── README.md                           # 本文档
├── index.js                            # 统一导出入口
├── table-view.jsx                      # 主表格组件
├── utils.js                            # 工具函数
├── hooks/                              # 自定义 Hooks
│   ├── use-table-logic.js              # 业务逻辑集成 Hook
│   ├── use-table-data.js               # 数据获取抽象 Hook
│   ├── use-table.js                    # 基础表格状态 Hook
│   ├── use-table-search.js             # 搜索功能 Hook
│   └── use-column-visibility.js        # 列可见性管理 Hook
├── components/                         # UI 子组件
│   ├── table-toolbar.jsx               # 工具栏组件
│   ├── table-content.jsx               # 表格内容组件
│   └── table-pagination.jsx            # 分页组件
└── 辅助组件/                           # 基础UI组件
    ├── table-head-custom.jsx           # 自定义表头
    ├── table-search-bar.jsx            # 搜索栏
    ├── table-skeleton.jsx              # 骨架屏
    ├── table-no-data.jsx               # 空数据提示
    ├── table-column-manager.jsx        # 列管理器
    └── table-*.jsx                     # 其他辅助组件
```

## 🔧 核心组件详解

### 1. TableView (主组件)

**职责**: 纯UI渲染，统一的表格展示层

**特点**:
- 基于 forwardRef 实现，支持父组件引用
- 完全的 Props 驱动，无内部状态
- 自动处理加载状态、空数据状态
- 支持批量操作和自定义操作

**基本用法**:
```jsx
import { TableView } from 'src/components/table/table-view';

function MyTablePage() {
  const tableRef = useRef(null);
  
  // 数据获取函数
  const dataFetcher = useCallback(async (page, pageSize, orderBy, order, searchValue) => {
    const result = await MyService.getData({
      page: page + 1,  // 后端从1开始
      pageSize,
      sort: orderBy,
      order,
      search: searchValue
    });
    
    return [result.data, result.total]; // 返回 [数据数组, 总数]
  }, []);

  const columns = [
    { id: 'id', label: 'ID', width: 80, align: 'center' },
    { id: 'name', label: '姓名', sortable: true, width: 200 },
    { id: 'email', label: '邮箱', width: 250 },
    { id: 'created_at', label: '创建时间', sortable: true, width: 160 },
    { id: 'action', label: '操作', width: 120, align: 'center' },
  ];

  return (
    <TableView
      ref={tableRef}
      columns={columns}
      dataFetcher={dataFetcher}
      tableId="my-table"
      onDelete={handleBatchDelete}
      enableSelection={true}
    />
  );
}
```

### 2. use-table-logic.js (业务逻辑层)

**职责**: 整合所有表格相关的状态和逻辑，提供统一接口

**集成的功能模块**:
- 🔄 基础表格状态 (use-table)
- 🔍 搜索功能 (use-table-search)  
- 👁️ 列管理 (use-column-visibility)
- 📊 数据获取 (use-table-data)

**核心功能**:
- 自动数据获取和刷新
- 搜索防抖处理
- 页码自动重置
- 批量操作处理
- 选择状态管理

### 3. use-table-data.js (数据层)

**职责**: 专门负责数据获取、状态管理和请求优化

**核心功能**:
- ✅ 自动请求去重 (AbortController)
- ✅ 参数变化自动触发数据获取
- ✅ Loading/Error 状态管理
- ✅ 手动刷新支持
- ✅ 组件卸载时自动清理

### 4. 自定义 Hooks

#### use-table.js - 基础表格状态
- 分页状态管理
- 排序状态管理
- 行选择状态管理
- 密度切换支持

#### use-table-search.js - 搜索功能
- 搜索值状态管理
- 防抖处理 (300ms)
- 搜索清空功能

#### use-column-visibility.js - 列管理
- 列可见性控制
- 列拖拽排序
- 本地存储持久化
- 菜单状态管理

### 5. UI 子组件

#### TableToolbar - 工具栏组件
- 搜索栏集成
- 列管理器
- 批量操作按钮
- 选择状态显示

#### TableContent - 表格内容组件
- 表格主体渲染
- 行选择处理
- 列固定支持
- 骨架屏加载

#### TablePagination - 分页组件
- 响应式分页选项
- 密度切换
- 数据统计显示
- 分页导航

## 🚀 快速开始

### 步骤 1: 定义列配置

```javascript
const columns = [
  {
    id: 'id',
    label: 'ID', 
    width: 80,
    align: 'center',
    sortable: true,
  },
  {
    id: 'account',
    label: '账号',
    width: 200,
    sortable: true,
    renderCell: (row) => (
      <Box sx={{ fontWeight: 500 }}>{row.account}</Box>
    ),
  },
  {
    id: 'group',
    label: '分组',
    width: 120,
    sortable: true,
    renderCell: (row) => (
      <Box sx={{ fontWeight: 500 }}>{row.group || '-'}</Box>
    ),
  },
  {
    id: 'created_at',
    label: '创建时间',
    width: 160,
    sortable: true,
    renderCell: (row) => fDateTime(row.created_at, 'YYYY/MM/DD HH:mm'),
  },
  {
    id: 'action',
    label: '操作',
    width: 120,
    align: 'center',
    renderCell: (row) => (
      <Box sx={{ display: 'flex', gap: 0.5, justifyContent: 'center' }}>
        <IconButton size="small" onClick={(e) => handleEdit(row, e)}>
          <Iconify icon="solar:pen-bold" width={16} />
        </IconButton>
        <IconButton size="small" onClick={(e) => handleDelete(row, e)}>
          <Iconify icon="solar:trash-bin-trash-bold" width={16} />
        </IconButton>
      </Box>
    ),
  },
];
```

### 步骤 2: 实现数据获取函数

```javascript
const dataFetcher = useCallback(async (page, pageSize, orderBy, order, searchValue) => {
  try {
    let serviceCall;

    if (searchValue && searchValue.trim()) {
      // 有搜索词时使用搜索API
      const searchFields = ['account', 'group', 'remark'];
      serviceCall = YourService.SearchPageList(
        page + 1, // 后端从1开始
        pageSize,
        searchValue.trim(),
        searchFields
      );
    } else {
      // 无搜索词时使用普通分页API
      serviceCall = YourService.PageList(page + 1, pageSize);
    }

    const result = await serviceCall;
    return [result?.data || [], result?.total || 0];
    
  } catch (error) {
    console.error('数据获取失败:', error);
    return [[], 0];
  }
}, []);
```

### 步骤 3: 使用 TableView

```javascript
import { TableView } from 'src/components/table/table-view';

export function MyDataTable() {
  const { t } = useTranslate();
  const tableRef = useRef(null);

  // 批量删除处理
  const handleBatchDelete = useCallback(async (selectedIds) => {
    if (!selectedIds || selectedIds.length === 0) return;

    try {
      await YourService.BatchDelete(selectedIds);
      toast.success(t('common:message.deleteSuccess'));
      tableRef.current?.refetch();
    } catch (error) {
      console.error('批量删除失败:', error);
      toast.error(t('common:message.deleteFailed'));
    }
  }, [t]);

  return (
    <TableView
      ref={tableRef}
      columns={columns}
      dataFetcher={dataFetcher}
      onDelete={handleBatchDelete}
      tableId="my-data-table"
      enableSelection={true}
      enableStickyLastColumn={true}
      noDataText={t('common:tips.no_data')}
    />
  );
}
```

## ⚙️ 高级配置

### 表格配置选项

```javascript
<TableView
  // 基础配置
  columns={columns}                    // 列定义 (必需)
  dataFetcher={dataFetcher}           // 数据获取函数 (必需)
  tableId="unique-id"                 // 唯一标识 (必需)
  
  // 功能配置
  enableSelection={true}              // 启用行选择
  enableStickyLastColumn={true}       // 固定最后一列
  onRowClick={handleRowClick}         // 行点击回调
  onSelectionChange={handleSelection} // 选择变化回调
  onDelete={handleBatchDelete}        // 批量删除回调
  
  // 自定义配置
  selectedActions={customActions}     // 自定义选中操作
  noDataText="暂无数据"               // 空数据提示
  loading={externalLoading}           // 外部loading状态
  tableProps={customTableProps}       // 传递给Table的属性
/>
```

### 列配置选项

```javascript
const columnConfig = {
  id: 'unique-column-id',        // 唯一标识 (必需)
  label: '列标题',               // 显示标题 (必需)
  
  // 尺寸控制
  width: 200,                    // 固定宽度
  minWidth: 100,                 // 最小宽度  
  maxWidth: 300,                 // 最大宽度
  
  // 行为配置
  sortable: true,                // 是否可排序
  align: 'center',               // 对齐方式: 'left' | 'center' | 'right'
  noWrap: true,                  // 禁止换行
  isHidden: false,               // 是否默认隐藏
  allowHiding: true,             // 是否允许隐藏
  
  // 自定义渲染
  renderCell: (row, index) => {  // 自定义单元格渲染
    return <CustomComponent data={row} />;
  },
};
```

## 🔧 工具函数

### utils.js

```javascript
// 分页数据切片
export function rowInPage(data, page, rowsPerPage);

// 计算空行数
export function emptyRows(page, rowsPerPage, arrayLength);

// 获取排序比较器
export function getComparator(order, orderBy);

// 嵌套属性访问
function getNestedProperty(obj, key);
```

## 🎯 最佳实践

### 1. 性能优化

#### 使用 useMemo 优化列定义
```javascript
const columns = useMemo(() => [
  {
    id: 'account',
    label: t('account:account'),
    renderCell: (row) => highlightText(row.account || '-', searchTerm),
  },
], [t, searchTerm]);
```

#### 使用 useCallback 优化数据获取
```javascript
const dataFetcher = useCallback(async (page, pageSize, orderBy, order, searchValue) => {
  // 数据获取逻辑
}, []);
```

### 2. 错误处理

```javascript
const dataFetcher = useCallback(async (page, pageSize, orderBy, order, searchValue) => {
  try {
    const result = await api.getData(...);
    return [result.data, result.total];
  } catch (error) {
    console.error('数据获取失败:', error);
    toast.error('数据加载失败，请重试');
    return [[], 0];
  }
}, []);
```

### 3. 国际化支持

```javascript
const columns = useMemo(() => [
  {
    id: 'account',
    label: t('account:account'),
    renderCell: (row) => (
      <Box sx={{ fontWeight: 500 }}>
        {highlightText(row.account || '-', currentSearchTerm)}
      </Box>
    ),
  },
], [t, currentSearchTerm]);
```

## 🔧 核心功能特性

### 1. 请求去重机制

- 使用 AbortController 取消重复请求
- 请求ID机制确保只处理最新请求结果
- 自动处理组件卸载时的清理

### 2. 搜索功能

- 300ms 防抖处理
- 搜索时自动重置页码
- 支持多字段搜索
- 实时搜索结果显示

### 3. 列管理

- 拖拽排序功能
- 列可见性控制
- 本地存储持久化
- 固定列支持

### 4. 分页功能

- 响应式分页选项
- 自动计算总页数
- 页码跳转支持
- 数据统计显示

### 5. 选择功能

- 单选/多选支持
- 全选/反选
- 批量操作
- 选择状态持久化

## 🐛 故障排除

### 常见问题

#### 1. 数据不显示
**可能原因**: dataFetcher 返回格式不正确
```javascript
// ❌ 错误的返回格式
return response;

// ✅ 正确的返回格式
return [response.data || [], response.total || 0];
```

#### 2. 字段不匹配
**可能原因**: 前端列ID与后端字段名不一致
```javascript
// ❌ 前端期望大写字段
{ id: 'Account', label: '账号' }

// ✅ 匹配后端小写字段
{ id: 'account', label: '账号' }
```

#### 3. 搜索不工作
**可能原因**: 搜索字段与后端不匹配
```javascript
// ✅ 确保搜索字段与后端一致
const searchFields = ['account', 'group', 'remark']; // 使用后端字段名
```

#### 4. 操作按钮重复请求
**可能原因**: useListErrorHandler 的 onInitializationRequired 回调导致依赖不稳定
```javascript
// ❌ 会导致重复请求
const { handleListData } = useListErrorHandler({
  onInitializationRequired: () => {
    console.log('数据库需要初始化');
  },
});

// ✅ 移除回调以保持稳定性
const { handleListData } = useListErrorHandler();
```

**解决方案**:
- 移除 `onInitializationRequired` 回调函数
- 保持 `handleListData` 依赖项的稳定性
- 避免在 `useCallback` 中包含可能变化的回调函数

## 📈 性能监控

### 关键指标

1. **初始加载请求数**: 应该 ≤ 2 次
2. **参数变化触发**: 每次变化只应触发 1 次请求
3. **组件重渲染频率**: 避免不必要的重渲染
4. **内存泄漏**: 确保组件卸载时正确清理

### 性能优化检查清单

- [ ] dataFetcher 使用 useCallback 包装
- [ ] columns 使用 useMemo 优化
- [ ] 避免在 renderCell 中创建新对象
- [ ] 正确设置 tableId (用于缓存)
- [ ] 移除 useListErrorHandler 的 onInitializationRequired 回调
- [ ] 确保所有依赖项稳定，避免重复请求
- [ ] 大数据集考虑虚拟滚动

## 🔄 迁移指南

从旧版本迁移到新版本的主要变化：

1. **数据获取方式**: 从 `getData` prop 改为 `dataFetcher`
2. **字段名统一**: 确保前后端字段名一致
3. **搜索字段**: 使用后端实际字段名
4. **必需参数**: 新增 `tableId` 必需参数

## 🔮 未来规划

### 即将新增的功能

1. **虚拟滚动支持** - 处理大数据集
2. **导出功能** - CSV/Excel 导出
3. **高级筛选** - 多条件筛选器
4. **拖拽排序** - 行拖拽重排
5. **自定义主题** - 更多主题选项

---

**📝 文档版本**: v2.1.1  
**🔄 最后更新**: 2024-07-14  
**👥 维护者**: A8Tools 开发团队

---

如有问题或建议，请提交 Issue 或联系开发团队。