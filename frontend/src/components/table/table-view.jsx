import { useBoolean } from 'minimal-shared/hooks';
import { useEffect, forwardRef, useImperativeHandle } from 'react';

import { Box, Card, Button, Typography } from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';
import { ConfirmDialog } from 'src/components/custom-dialog';

import { TableNoData } from './table-no-data';
import { useTableLogic } from './hooks/use-table-logic';
import { TableToolbar } from './components/table-toolbar';
import { TableContent } from './components/table-content';
import { TablePagination } from './components/table-pagination';

/**
 * 重构后的表格组件 - 纯UI渲染层
 *
 * @param {Array} columns - 列定义
 * @param {function} dataFetcher - 数据获取函数 (page, pageSize, orderBy, order, searchValue) => Promise<[data, total]>
 * @param {function} onRowClick - 行点击回调
 * @param {boolean} enableSelection - 是否启用选择
 * @param {function} onSelectionChange - 选择变化回调
 * @param {function} onDelete - 批量删除回调
 * @param {Array} selectedActions - 选中项操作按钮
 * @param {boolean} loading - 外部loading状态
 * @param {string} noDataText - 无数据文本
 * @param {React.Component} emptyComponent - 自定义空组件
 * @param {object} emptyProps - 传递给空组件的额外属性
 * @param {object} tableProps - 传递给Table的属性
 * @param {boolean} enableStickyLastColumn - 是否固定最后一列
 * @param {string} tableId - 表格唯一标识
 */
export const TableView = forwardRef(function TableView(
  {
    columns,
    dataFetcher,
    onRowClick,
    enableSelection = true,
    onSelectionChange,
    onDelete,
    selectedActions = [],
    loading: externalLoading,
    noDataText,
    emptyComponent,
    emptyProps = {},
    tableProps = {},
    enableStickyLastColumn = true,
    tableId = 'default',
  },
  ref
) {
  const { t } = useTranslate('common');
  const confirmDialog = useBoolean();

  // 使用业务逻辑集成Hook
  const tableLogic = useTableLogic({
    dataFetcher,
    tableConfig: {
      defaultDense: true,
      defaultRowsPerPage: 20,
      defaultCurrentPage: 0,
      defaultOrderBy: 'id',
      defaultOrder: 'desc',
    },
    searchConfig: {},
    columnsConfig: { columns },
    tableId,
    onSelectionChange,
    onDelete,
  });

  // 暴露方法给父组件
  useImperativeHandle(ref, () => tableLogic.utils.getImperativeHandle(), [tableLogic.utils]);

  // 组件卸载时清理
  useEffect(
    () => () => {
      tableLogic.utils.cleanup();
    },
    [tableLogic.utils]
  );

  // 使用外部loading状态
  const isLoading = externalLoading !== undefined ? externalLoading : tableLogic.data.loading;

  // 如果无数据且不在加载，显示空状态
  if (!isLoading && tableLogic.data.data && tableLogic.data.data.length === 0) {
    const EmptyComponent = emptyComponent || TableNoData;

    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', pb: 2 }}>
        <Box
          sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: 400,
          }}
        >
          <EmptyComponent
            notFound
            title={noDataText}
            sx={{ py: 10, backgroundColor: 'transparent' }}
            {...emptyProps}
          />
        </Box>
      </Box>
    );
  }

  // 初始加载状态
  if (isLoading && (!tableLogic.data.data || tableLogic.data.data.length === 0)) {
    return (
      <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', pb: 2 }}>
        <Card
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 1,
            mb: 2,
            elevation: 0, // 移除阴影
            boxShadow: 'none', // 确保没有阴影
            border: 'none', // 移除边框
            backgroundColor: 'transparent', // 透明背景
          }}
        >
          <Box
            sx={{
              flex: 1,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              minHeight: 400,
            }}
          >
            <Box sx={{ textAlign: 'center' }}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  margin: '0 auto 16px',
                  animation: 'spin 1s linear infinite',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' },
                  },
                }}
              >
                <Iconify icon="eos-icons:bubble-loading" sx={{ width: '100%', height: '100%' }} />
              </Box>
              <Typography variant="body2" color="text.secondary">
                {t('common:loading')}
              </Typography>
            </Box>
          </Box>
        </Card>
      </Box>
    );
  }

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%', pb: 2 }}>
      <Card sx={{ display: 'flex', flexDirection: 'column', flex: 1, mb: 2 }}>
        {/* 工具栏 */}
        <TableToolbar
          search={{
            ...tableLogic.search,
            handleSearchWithReset: tableLogic.actions.handleSearchWithReset,
            handleClearSearchWithReset: tableLogic.actions.handleClearSearchWithReset,
            loading: isLoading,
            totalCount: tableLogic.data.totalCount,
          }}
          columns={tableLogic.columns}
          selectedActions={selectedActions}
          onDelete={onDelete ? () => confirmDialog.onTrue() : null}
          selected={tableLogic.table.selected}
          tableData={tableLogic.data.data || []}
          enableSelection={enableSelection}
          onSelectAllRows={tableLogic.table.onSelectAllRows}
          t={t}
        />

        {/* 表格内容 */}
        <TableContent
          data={tableLogic.data}
          table={tableLogic.table}
          columns={tableLogic.columns}
          onRowClick={onRowClick}
          enableSelection={enableSelection}
          enableStickyLastColumn={enableStickyLastColumn}
          noDataText={noDataText}
          emptyComponent={emptyComponent}
          emptyProps={emptyProps}
          tableProps={tableProps}
        />

        {/* 分页 */}
        <TablePagination table={tableLogic.table} totalCount={tableLogic.data.totalCount} t={t} />
      </Card>

      {/* 删除确认对话框 */}
      {onDelete && (
        <ConfirmDialog
          open={confirmDialog.value}
          onClose={confirmDialog.onFalse}
          title={t('common:action.delete')}
          content={<>{t('common:tips.delete', { count: tableLogic.table.selected.length })}</>}
          onConfirm={tableLogic.actions.handleBatchDelete}
          action={
            <Button
              variant="contained"
              color="primary"
              onClick={tableLogic.actions.handleBatchDelete}
            >
              {t('common:action.delete')}
            </Button>
          }
        />
      )}
    </Box>
  );
});
