import {
  Box,
  Button,
  Dialog,
  Typography,
  DialogTitle,
  DialogActions,
  DialogContent,
} from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

/**
 * 初始化提示对话框组件
 * 在用户执行需要数据库的操作时，提示用户需要先进行应用初始化
 */
export function InitializationPromptDialog({ open, onClose, onConfirm }) {
  const { t } = useTranslate();

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth sx={{ borderRadius: 2 }}>
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              width: 48,
              height: 48,
              borderRadius: '50%',
              bgcolor: 'primary.main',
              color: 'common.white',
            }}
          >
            <Iconify icon="mingcute:rocket-fill" width={24} />
          </Box>
          <Box>
            <Typography variant="h6" component="div">
              {t('common:initialization.required')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('common:initialization.subtitle')}
            </Typography>
          </Box>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Typography variant="body1" color="text.primary">
            {t('common:initialization.description')}
          </Typography>

          <Box
            sx={{
              p: 2,
              borderRadius: 1,
              border: '1px solid',
              borderColor: 'grey.500',
            }}
          >
            <Typography variant="body2" color="text.secondary">
              <Iconify icon="solar:info-circle-bold" sx={{ mr: 1, verticalAlign: 'middle' }} />
              {t('common:initialization.benefits')}
            </Typography>
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose}>{t('common:action.cancel')}</Button>
        <Button
          color="primary"
          variant="contained"
          onClick={onConfirm}
          startIcon={<Iconify icon="mingcute:rocket-fill" />}
          sx={{ minWidth: 120 }}
        >
          {t('common:initialization.startNow')}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
