# 统一初始化处理方案 - 使用说明文档

> 为A8Tools项目提供统一的数据库初始化流程管理

## 概述

本方案通过 `useInitializationManager` Hook 统一管理所有页面的初始化逻辑，并在应用层面（`app.jsx`）提供全局初始化检查，确保用户进入应用时就能完成初始化流程。

### 🔄 双层初始化机制
1. **应用层初始化** (`app.jsx`) - 用户进入应用时自动检查并显示初始化对话框
2. **页面层初始化** (`useInitializationManager`) - 用户在具体操作时的初始化保护

## 核心组件

### 1. InitializationPromptDialog (本目录)
- **功能**: 初始化提示对话框，询问用户是否开始初始化
- **特点**: 统一的提示文案，无需传入动态参数
- **使用**: 由 `useInitializationManager` 自动管理

### 2. InitializationDialog
- **功能**: 完整的初始化流程对话框
- **位置**: `src/components/initialization/dialog.jsx`
- **使用**: 由 `useInitializationManager` 自动管理

### 3. useInitializationManager Hook
- **功能**: 统一的初始化管理Hook
- **位置**: `src/hooks/use-initialization-manager.js`
- **作用**: 替代原有的 `usePasswordGuard`，提供完整的初始化流程管理

## 快速开始

### 基础用法

```javascript
import { useInitializationManager } from 'src/hooks/use-initialization-manager';

export default function YourPage() {
  const { wrapAction, renderInitialization } = useInitializationManager();
  
  // 包装需要初始化的操作
  const handleSomeAction = wrapAction(() => {
    console.log('这个操作需要先完成初始化');
  });

  return (
    <>
      {/* 页面内容 */}
      <Button onClick={handleSomeAction}>执行操作</Button>
      
      {/* 统一的初始化UI管理 - 一行搞定 */}
      {renderInitialization()}
    </>
  );
}
```

### 完整示例

```javascript
import { useRef, useState } from 'react';
import { useBoolean } from 'minimal-shared/hooks';
import { Box, Button, Typography } from '@mui/material';

import { useInitializationManager } from 'src/hooks/use-initialization-manager';
import { useTranslate } from 'src/locales';
import { Iconify } from 'src/components/iconify';

export default function ExamplePage() {
  const { t } = useTranslate('example');
  const { wrapAction, renderInitialization, hasPassword } = useInitializationManager();
  
  // 业务状态
  const createDialog = useBoolean(false);
  const importDialog = useBoolean(false);
  
  // 业务操作函数
  const handleCreate = () => {
    createDialog.onTrue();
    console.log('创建操作');
  };
  
  const handleImport = () => {
    importDialog.onTrue();
    console.log('导入操作');
  };
  
  const handleExport = () => {
    console.log('导出操作');
  };

  // 使用 wrapAction 包装需要初始化的操作
  const handleCreateWithInit = wrapAction(handleCreate);
  const handleImportWithInit = wrapAction(handleImport);
  const handleExportWithInit = wrapAction(handleExport);

  return (
    <>
      {/* 页面头部 */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h5">{t('example:title')}</Typography>
        
        {/* 显示初始化状态 */}
        {hasPassword ? (
          <Typography variant="body2" color="success.main">
            ✅ 应用已初始化
          </Typography>
        ) : (
          <Typography variant="body2" color="warning.main">
            ⚠️ 需要初始化
          </Typography>
        )}
      </Box>

      {/* 操作按钮 */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Button
          variant="contained"
          startIcon={<Iconify icon="mingcute:add-circle-fill" />}
          onClick={handleCreateWithInit}
        >
          {t('example:create')}
        </Button>
        
        <Button
          variant="outlined"
          startIcon={<Iconify icon="mingcute:arrow-down-circle-fill" />}
          onClick={handleImportWithInit}
        >
          {t('example:import')}
        </Button>
        
        <Button
          variant="text"
          startIcon={<Iconify icon="mingcute:arrow-up-circle-fill" />}
          onClick={handleExportWithInit}
        >
          {t('example:export')}
        </Button>
      </Box>

      {/* 页面主要内容 */}
      <Box>
        {/* 你的页面内容 */}
      </Box>

      {/* 业务对话框 */}
      {createDialog.value && (
        <CreateDialog 
          open={createDialog.value} 
          onClose={createDialog.onFalse} 
        />
      )}
      
      {importDialog.value && (
        <ImportDialog 
          open={importDialog.value} 
          onClose={importDialog.onFalse} 
        />
      )}

      {/* 🎯 关键：统一的初始化UI管理 - 一行代码搞定所有初始化流程 */}
      {renderInitialization()}
    </>
  );
}
```

## API 参考

### useInitializationManager()

统一的初始化管理Hook，提供完整的初始化流程管理。

#### 返回值

```typescript
{
  hasPassword: boolean;           // 全局初始化状态
  wrapAction: (callback) => Function;  // 操作包装函数
  checkInitialization: () => boolean;  // 状态检查函数
  renderInitialization: () => JSX.Element; // UI渲染函数
}
```

#### 详细说明

##### `hasPassword: boolean`
- **功能**: 获取全局初始化状态
- **返回**: `true` 表示已初始化，`false` 表示需要初始化
- **用途**: 可用于条件渲染或状态显示

```javascript
const { hasPassword } = useInitializationManager();

// 条件渲染
{hasPassword ? <MainContent /> : <WelcomeMessage />}
```

##### `wrapAction: (callback) => Function`
- **功能**: 包装需要初始化的操作
- **参数**: `callback` - 需要执行的回调函数
- **返回**: 包装后的函数
- **行为**: 
  - 如果已初始化，直接执行原操作
  - 如果未初始化，显示初始化提示对话框

```javascript
const { wrapAction } = useInitializationManager();

// 包装操作
const handleCreate = wrapAction(() => {
  console.log('创建操作');
});

// 也可以包装带参数的操作
const handleEdit = wrapAction((id, data) => {
  console.log('编辑操作', id, data);
});
```

##### `checkInitialization: () => boolean`
- **功能**: 直接检查初始化状态
- **返回**: `true` 表示已初始化，`false` 表示需要初始化
- **行为**: 如果未初始化，会自动显示初始化提示
- **用途**: 适用于需要手动检查的场景

```javascript
const { checkInitialization } = useInitializationManager();

const handleComplexOperation = () => {
  if (!checkInitialization()) {
    return; // 会自动显示初始化提示
  }
  
  // 执行复杂操作
  console.log('复杂操作');
};
```

##### `renderInitialization: () => JSX.Element`
- **功能**: 渲染所有初始化相关的UI组件
- **返回**: 包含InitializationPromptDialog和InitializationDialog的JSX元素
- **用途**: 在页面底部调用，提供完整的初始化UI

```javascript
const { renderInitialization } = useInitializationManager();

return (
  <>
    {/* 页面内容 */}
    
    {/* 必须：渲染初始化UI */}
    {renderInitialization()}
  </>
);
```

## 迁移指南

### 从旧的usePasswordGuard迁移

#### 旧代码示例
```javascript
// ❌ 旧的复杂方式
import { useState } from 'react';
import { useBoolean } from 'minimal-shared/hooks';
import { usePasswordGuard } from 'src/hooks/use-password-guard';
import InitializationDialog from 'src/components/initialization/dialog';
import { InitializationPromptDialog } from 'src/components/initialization-prompt-dialog';

export default function OldPage() {
  const { withPasswordCheck } = usePasswordGuard();
  const initPromptDialog = useBoolean(false);
  const initDialog = useBoolean(false);
  const [currentAction, setCurrentAction] = useState('');

  // 需要多个重复的处理函数
  const handleInitializationRequired = (actionName) => {
    setCurrentAction(actionName);
    initPromptDialog.onTrue();
  };

  const handleInitializationConfirm = () => {
    initPromptDialog.onFalse();
    initDialog.onTrue();
  };

  // ... 其他处理函数

  // 复杂的操作包装
  const handleCreate = withPasswordCheck(createAction, {
    action: t('common:action.create'),
    onInitializationRequired: handleInitializationRequired,
  });

  return (
    <>
      {/* 页面内容 */}
      
      {/* 手动渲染两个对话框 */}
      <InitializationPromptDialog
        open={initPromptDialog.value}
        onClose={handleClose}
        onConfirm={handleInitializationConfirm}
        actionName={currentAction}
      />
      <InitializationDialog
        open={initDialog.value}
        onClose={handleClose}
        onComplete={handleComplete}
      />
    </>
  );
}
```

#### 新代码示例
```javascript
// ✅ 新的简洁方式
import { useInitializationManager } from 'src/hooks/use-initialization-manager';

export default function NewPage() {
  const { wrapAction, renderInitialization } = useInitializationManager();

  // 简单的操作包装
  const handleCreate = wrapAction(createAction);

  return (
    <>
      {/* 页面内容 */}
      
      {/* 一行代码搞定所有初始化UI */}
      {renderInitialization()}
    </>
  );
}
```

### 迁移检查清单

- [ ] 删除 `usePasswordGuard` 导入
- [ ] 删除 `InitializationPromptDialog` 和 `InitializationDialog` 导入
- [ ] 删除 `initPromptDialog` 和 `initDialog` 状态
- [ ] 删除 `currentAction` 状态
- [ ] 删除所有初始化相关的事件处理函数
- [ ] 用 `useInitializationManager` 替代
- [ ] 用 `wrapAction` 替代 `withPasswordCheck`
- [ ] 用 `renderInitialization()` 替代手动渲染对话框

## 最佳实践

### 1. 命名约定
```javascript
// ✅ 推荐：清晰的函数命名
const handleCreateWallet = wrapAction(createWallet);
const handleImportWallet = wrapAction(importWallet);

// ❌ 避免：混淆的命名
const handleCreate = wrapAction(create);
```

### 2. 操作分离
```javascript
// ✅ 推荐：分离业务逻辑和初始化逻辑
const createWallet = () => {
  // 纯业务逻辑
  console.log('创建钱包');
};

const handleCreateWallet = wrapAction(createWallet);

// ❌ 避免：在wrapAction内部处理业务逻辑
const handleCreateWallet = wrapAction(() => {
  // 复杂的业务逻辑...
});
```

### 3. 条件渲染
```javascript
// ✅ 推荐：使用hasPassword进行条件渲染
const { hasPassword, wrapAction, renderInitialization } = useInitializationManager();

return (
  <>
    {hasPassword ? (
      <AdvancedFeatures />
    ) : (
      <WelcomeMessage />
    )}
    
    {renderInitialization()}
  </>
);
```

### 4. 错误处理
```javascript
// ✅ 推荐：在业务逻辑中处理错误
const createWallet = async () => {
  try {
    await walletService.create();
    showSuccessMessage();
  } catch (error) {
    showErrorMessage(error);
  }
};

const handleCreateWallet = wrapAction(createWallet);
```

## 常见问题

### Q: 为什么不需要传递actionName了？
A: 新的设计使用统一的提示文案，减少了复杂性。用户体验更加一致，代码也更简洁。

### Q: 如何自定义初始化提示内容？
A: 通过国际化文件 `locales` 修改 `common:initialization.description` 等键值来自定义提示内容。

### Q: 可以在同一个页面使用多次wrapAction吗？
A: 完全可以。每个需要初始化的操作都可以单独使用wrapAction包装。

### Q: renderInitialization()必须放在页面底部吗？
A: 不是必须的，但建议放在页面底部，确保所有业务组件都渲染完成后再渲染初始化对话框。

### Q: 如何处理异步操作？
A: wrapAction完全支持异步操作，直接传入async函数即可。

```javascript
const handleAsyncOperation = wrapAction(async () => {
  const result = await someAsyncOperation();
  console.log(result);
});
```

## 技术原理

### 状态管理
- **全局状态**: 通过 `useAppSettings` 中的 `hasPassword` 管理全局初始化状态
- **应用层检查**: `app.jsx` 在应用启动时检查 `hasPassword`，未初始化时直接显示初始化对话框
- **页面层保护**: 各页面通过 `useInitializationManager` 包装操作，提供二次保护
- **状态同步**: 初始化完成后，全局状态自动更新，所有层级立即生效

### 执行流程

#### 应用启动流程
```
应用启动 → app.jsx检查hasPassword → 两种情况：

1. hasPassword = true (已初始化)
   └── 正常进入应用 ✅

2. hasPassword = false (未初始化)
   └── 显示应用层InitializationDialog
       └── 用户完成初始化步骤
           └── hasPassword自动变为true
               └── 正常进入应用 ✅
```

#### 页面操作流程
```
用户操作 → wrapAction检查 → 三种情况：

1. hasPassword = true (已初始化)
   └── 直接执行操作 ✅

2. hasPassword = false (未初始化，理论上不应该出现)
   └── 显示页面层InitializationPromptDialog
       └── 用户点击"开始初始化"
           └── 显示InitializationDialog
               └── 用户完成初始化步骤
                   └── hasPassword自动变为true
                       └── 用户手动重新点击操作按钮 ✅

3. 用户取消初始化
   └── 关闭所有对话框，回到原状态
```

### 性能优化
- 使用 `useCallback` 优化函数引用稳定性
- UI状态本地化，避免全局重渲染
- 延迟加载对话框组件

## 更新日志

### v1.0.0 (当前版本)
- ✅ 创建统一的 `useInitializationManager` Hook
- ✅ 简化 `InitializationPromptDialog`，移除actionName参数
- ✅ 替代 `usePasswordGuard`，减少代码重复
- ✅ 支持全局状态管理，一次初始化全应用生效
- ✅ 提供完整的迁移指南和使用文档

---

## 支持

如有问题或建议，请联系开发团队或在项目中创建Issue。

**Happy Coding! 🚀**