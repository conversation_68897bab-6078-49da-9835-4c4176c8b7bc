import { useState } from 'react';

import { Box, Button } from '@mui/material';

import { useAppPassword } from 'src/hooks/use-app-password';

import { useTranslate } from 'src/locales';

import { PasswordSetupDialog } from './password-setup-dialog';
import { PasswordChangeDialog } from './password-change-dialog';

/**
 * 密码设置/修改按钮组件
 * 根据是否已设置密码显示不同的按钮文本和功能
 * 分别调用密码设置对话框和密码修改对话框
 * 组件专注于UI控制，业务逻辑由各自的Dialog处理
 */
export function PasswordSetButton({ onSuccess, onError }) {
  const { t } = useTranslate();
  const { hasPassword } = useAppPassword();
  const [setupDialogOpen, setSetupDialogOpen] = useState(false);
  const [changeDialogOpen, setChangeDialogOpen] = useState(false);

  // 处理打开密码设置对话框
  const handleOpenSetupDialog = () => {
    setSetupDialogOpen(true);
  };

  // 处理关闭密码设置对话框
  const handleCloseSetupDialog = () => {
    setSetupDialogOpen(false);
  };

  // 处理打开密码修改对话框
  const handleOpenChangeDialog = () => {
    setChangeDialogOpen(true);
  };

  // 处理关闭密码修改对话框
  const handleCloseChangeDialog = () => {
    setChangeDialogOpen(false);
  };

  // 处理密码设置成功
  const handleSetupSuccess = () => {
    setSetupDialogOpen(false);
    onSuccess?.();
  };

  // 处理密码修改成功
  const handleChangeSuccess = () => {
    setChangeDialogOpen(false);
    onSuccess?.();
  };

  // 处理密码设置错误（可选）
  const handleSetupError = (error) => {
    console.error('密码设置失败:', error);
    onError?.(error);
  };

  // 处理密码修改错误（可选）
  const handleChangeError = (error) => {
    console.error('密码修改失败:', error);
    onError?.(error);
  };

  return (
    <Box>
      {!hasPassword ? (
        <Button variant="outlined" sx={{ mb: 0.5 }} onClick={handleOpenSetupDialog}>
          {t('settings:security.setPasscode')}
        </Button>
      ) : (
        <Button variant="outlined" sx={{ mb: 2 }} onClick={handleOpenChangeDialog}>
          {t('settings:security.changePasscode')}
        </Button>
      )}

      {/* 密码设置对话框 */}
      <PasswordSetupDialog
        open={setupDialogOpen}
        onClose={handleCloseSetupDialog}
        onSuccess={handleSetupSuccess}
        onError={handleSetupError}
      />

      {/* 密码修改对话框 */}
      <PasswordChangeDialog
        open={changeDialogOpen}
        onClose={handleCloseChangeDialog}
        onSuccess={handleChangeSuccess}
        onError={handleChangeError}
      />
    </Box>
  );
}
