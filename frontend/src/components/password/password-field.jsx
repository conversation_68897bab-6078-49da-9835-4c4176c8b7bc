import { useBoolean } from 'minimal-shared/hooks';

import { Box, Tooltip, IconButton } from '@mui/material';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

export function PasswordField({ password }) {
  const { t } = useTranslate('common');
  const showPassword = useBoolean(false);

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      {showPassword.value ? password : '********'}
      <Tooltip
        title={showPassword.value ? t('common:tips.hide-password') : t('common:tips.show-password')}
        placement="top"
        arrow
      >
        <IconButton size="small" onClick={showPassword.onToggle} sx={{ ml: 1 }}>
          <Iconify icon={showPassword.value ? 'solar:eye-closed-linear' : 'solar:eye-bold'} />
        </IconButton>
      </Tooltip>
    </Box>
  );
}
