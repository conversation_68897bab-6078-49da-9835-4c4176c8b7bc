import { toast } from 'sonner';
import { useRef, useState, useCallback } from 'react';

import {
  Box,
  Alert,
  Button,
  Dialog,
  Checkbox,
  TextField,
  IconButton,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  FormControlLabel,
} from '@mui/material';

import { useAppPassword } from 'src/hooks/use-app-password';

import { useTranslate } from 'src/locales/use-locales';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const DEBOUNCE_DELAY = 800;
const MIN_PASSCODE_LENGTH = 6;

/**
 * 密码修改对话框组件
 * 专门用于修改已存在的密码，需要验证旧密码
 * 内置完整的业务逻辑处理，包括错误处理和用户反馈
 */
export function PasswordChangeDialog({
  open,
  onClose,
  onSuccess, // 修改成功回调
  onError, // 修改失败回调（可选）
}) {
  const { t } = useTranslate();
  const { verifyPassword, changePassword } = useAppPassword();

  // 表单状态
  const [oldPasscode, setOldPasscode] = useState('');
  const [newPasscode, setNewPasscode] = useState('');
  const [confirmPasscode, setConfirmPasscode] = useState('');
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [oldPasswordVerified, setOldPasswordVerified] = useState(false);
  const [isVerifyingOldPassword, setIsVerifyingOldPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  // 新增：错误和加载状态
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // 防抖定时器引用
  const verifyTimerRef = useRef(null);

  const handleClose = () => {
    // 清除防抖定时器
    if (verifyTimerRef.current) {
      clearTimeout(verifyTimerRef.current);
      verifyTimerRef.current = null;
    }

    // 重置所有状态
    setOldPasscode('');
    setNewPasscode('');
    setConfirmPasscode('');
    setShowOldPassword(false);
    setShowNewPassword(false);
    setShowConfirmPassword(false);
    setOldPasswordVerified(false);
    setIsVerifyingOldPassword(false);
    setAgreedToTerms(false);
    setErrorMessage('');
    setIsSubmitting(false);
    onClose();
  };

  // 防抖验证旧密码函数
  const debouncedVerifyPassword = useCallback(
    async (password) => {
      if (!password) {
        setOldPasswordVerified(false);
        setIsVerifyingOldPassword(false);
        return;
      }

      setIsVerifyingOldPassword(true);
      setErrorMessage(''); // 清除之前的错误

      try {
        const isValid = await verifyPassword(password);
        setOldPasswordVerified(isValid);
        if (!isValid) {
          setErrorMessage(t('settings:security.oldPasswordIncorrect'));
        }
      } catch (err) {
        console.error('验证旧密码失败:', err);
        setOldPasswordVerified(false);
        setErrorMessage(t('settings:security.verifyPasswordFailed'));
      } finally {
        setIsVerifyingOldPassword(false);
      }
    },
    [verifyPassword, t]
  );

  // 验证旧密码 - 添加防抖机制
  const handleOldPasswordChange = useCallback(
    (event) => {
      const { value } = event.target;
      setOldPasscode(value);
      setErrorMessage(''); // 清除错误信息

      // 清除之前的定时器
      if (verifyTimerRef.current) {
        clearTimeout(verifyTimerRef.current);
      }

      if (!value) {
        // 如果输入为空，立即重置状态
        setOldPasswordVerified(false);
        setIsVerifyingOldPassword(false);
        return;
      }

      // 设置新的防抖定时器，DEBOUNCE_DELAY 后执行验证
      verifyTimerRef.current = setTimeout(() => {
        debouncedVerifyPassword(value);
      }, DEBOUNCE_DELAY);
    },
    [debouncedVerifyPassword]
  );

  const handleConfirm = async () => {
    if (!oldPasswordVerified || !newPasscode || newPasscode !== confirmPasscode) {
      return;
    }

    setIsSubmitting(true);
    setErrorMessage('');

    try {
      const success = await changePassword(oldPasscode, newPasscode, (progress) => {
        console.log('修改密码进度:', progress);
      });

      if (success) {
        // 显示成功提示
        toast.success(t('settings:security.passwordChangeSuccess'));

        // 调用成功回调
        if (onSuccess) {
          onSuccess();
        }

        handleClose();
      } else {
        const errorMsg = t('settings:security.passwordChangeFailed');
        setErrorMessage(errorMsg);
        // 调用错误回调
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (err) {
      console.error('密码修改异常:', err);
      const errorMsg = err.message || t('settings:security.passwordChangeError');
      setErrorMessage(errorMsg);

      // 调用错误回调
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const isValid =
    oldPasswordVerified &&
    newPasscode &&
    newPasscode === confirmPasscode &&
    newPasscode.length >= MIN_PASSCODE_LENGTH &&
    agreedToTerms;

  // 获取按钮文案和状态
  const getButtonState = () => {
    if (isSubmitting) {
      return { text: t('settings:security.changingPassword'), disabled: true };
    }

    if (oldPasscode && !oldPasswordVerified && !isVerifyingOldPassword) {
      return { text: t('settings:security.oldPasswordIncorrect'), disabled: true };
    }

    if (isVerifyingOldPassword) {
      return { text: t('settings:security.verifyingPassword'), disabled: true };
    }

    if (!newPasscode && !confirmPasscode) {
      return { text: t('settings:security.confirm'), disabled: true };
    }

    if (newPasscode && newPasscode.length < 6) {
      return { text: t('settings:security.passwordTooShort'), disabled: true };
    }

    if (confirmPasscode && newPasscode !== confirmPasscode) {
      return { text: t('settings:security.passwordMismatch'), disabled: true };
    }

    if (
      newPasscode &&
      confirmPasscode &&
      newPasscode === confirmPasscode &&
      newPasscode.length >= MIN_PASSCODE_LENGTH &&
      !agreedToTerms
    ) {
      return { text: t('settings:security.pleaseAgreeToTerms'), disabled: true };
    }

    if (isValid) {
      return { text: t('settings:security.confirm'), disabled: false };
    }

    return { text: t('settings:security.confirm'), disabled: true };
  };

  const buttonState = getButtonState();

  return (
    <Dialog
      open={open}
      onClose={isSubmitting ? undefined : handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            p: 1,
          },
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {t('settings:security.changePasscode')}
        <IconButton onClick={handleClose} size="small" disabled={isSubmitting}>
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3, pb: 2 }}>
        <Box sx={{ mt: 2 }}>
          {/* 错误提示 */}
          {errorMessage && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errorMessage}
            </Alert>
          )}

          {/* 旧密码输入框 */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.oldPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showOldPassword ? 'text' : 'password'}
              value={oldPasscode}
              onChange={handleOldPasswordChange}
              placeholder={t('settings:security.enterOldPasscode')}
              variant="outlined"
              size="medium"
              disabled={isSubmitting}
              error={oldPasscode && !oldPasswordVerified && !isVerifyingOldPassword}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowOldPassword(!showOldPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                        disabled={isSubmitting}
                      >
                        <Iconify
                          icon={showOldPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>

          {/* 新密码输入框 */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.newPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showNewPassword ? 'text' : 'password'}
              value={newPasscode}
              onChange={(e) => setNewPasscode(e.target.value)}
              placeholder={t('settings:security.createStrongPasscode')}
              variant="outlined"
              size="medium"
              disabled={isSubmitting}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                        disabled={isSubmitting}
                      >
                        <Iconify
                          icon={showNewPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>

          {/* 确认密码输入框 */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.confirmPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPasscode}
              onChange={(e) => setConfirmPasscode(e.target.value)}
              placeholder={t('settings:security.reenterPasscode')}
              variant="outlined"
              size="medium"
              disabled={isSubmitting}
              error={confirmPasscode && newPasscode !== confirmPasscode}
              helperText={
                confirmPasscode && newPasscode !== confirmPasscode
                  ? t('settings:security.passwordMismatch')
                  : ''
              }
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                        disabled={isSubmitting}
                      >
                        <Iconify
                          icon={showConfirmPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>

          {/* 密码忘记提示确认 */}
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  color="primary"
                  disabled={isSubmitting}
                />
              }
              label={
                <span
                  dangerouslySetInnerHTML={{ __html: t('settings:security.passwordForgetTip') }}
                />
              }
            />
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          fullWidth
          variant="contained"
          onClick={handleConfirm}
          disabled={buttonState.disabled}
          sx={{
            borderRadius: 2,
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          {buttonState.text}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
