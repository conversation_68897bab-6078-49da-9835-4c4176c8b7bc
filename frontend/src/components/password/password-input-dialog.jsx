import { useState } from 'react';

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Dialog,
  TextField,
  IconButton,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
} from '@mui/material';

import { useAppPassword } from 'src/hooks/use-app-password';
// import { useTranslate } from 'src/locales/use-locales';

import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

/**
 * 密码输入对话框组件
 * 专门用于异常情况的密码输入，当密码失效或需要重新输入时显示
 */
export function PasswordInputDialog({ open, onClose, eventData, onSuccess }) {
  // const { t } = useTranslate();
  const { unlockApplication } = useAppPassword();

  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleClose = () => {
    // 重置状态
    setPassword('');
    setShowPassword(false);
    setError('');
    setIsLoading(false);
    onClose();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!password) {
      setError('请输入密码');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // 使用相同的unlockApplication方法验证密码
      const success = await unlockApplication(password);

      if (success) {
        // 成功后调用成功回调
        onSuccess?.();
        handleClose();
      } else {
        setError('密码验证失败，请重试');
      }
    } catch (err) {
      console.error('密码验证异常:', err);
      setError(err.message || '密码验证异常，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  // 根据事件类型获取对话框内容
  const getDialogContent = () => {
    const eventType = eventData?.type || 'password_invalid';

    switch (eventType) {
      case 'password_invalid':
        return {
          title: '密码失效',
          message: '当前密码已失效，请重新输入密码以继续操作',
          severity: 'warning',
        };
      case 'password_required':
        return {
          title: '需要密码',
          message: '系统需要验证您的密码以继续操作',
          severity: 'info',
        };
      default:
        return {
          title: '身份验证',
          message: '请输入密码以继续',
          severity: 'info',
        };
    }
  };

  const dialogContent = getDialogContent();

  return (
    <Dialog
      open={open}
      onClose={isLoading ? undefined : handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            p: 1,
          },
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {dialogContent.title}
        {!isLoading && (
          <IconButton onClick={handleClose} size="small">
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        )}
      </DialogTitle>

      <DialogContent sx={{ px: 3, pb: 2 }}>
        <Alert severity={dialogContent.severity} sx={{ mb: 3 }}>
          {dialogContent.message}
        </Alert>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <form onSubmit={handleSubmit}>
          <TextField
            autoFocus
            margin="normal"
            required
            fullWidth
            name="password"
            label="输入密码"
            type={showPassword ? 'text' : 'password'}
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={isLoading}
            error={!!error}
            slotProps={{
              input: {
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleTogglePasswordVisibility}
                      edge="end"
                      disabled={isLoading}
                    >
                      <Iconify icon={showPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'} />
                    </IconButton>
                  </InputAdornment>
                ),
              },
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 2,
              },
            }}
          />
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} color="inherit" disabled={isLoading}>
          取消
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={!password || isLoading}
          sx={{
            borderRadius: 2,
            px: 3,
          }}
        >
          {isLoading ? '验证中...' : '确认'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

/**
 * 密码输入对话框容器组件
 * 自动监听密码事件并显示对话框
 */
export function PasswordInputDialogContainer() {
  const { showPasswordDialog, passwordEvent, hidePasswordDialog } = useAppPassword();

  const handleSuccess = () => {
    // 密码验证成功后的处理
    console.log('密码验证成功，继续之前的操作');
  };

  return (
    <PasswordInputDialog
      open={showPasswordDialog}
      onClose={hidePasswordDialog}
      eventData={passwordEvent}
      onSuccess={handleSuccess}
    />
  );
}
