import { useState } from 'react';

import {
  Box,
  Alert,
  Button,
  Dialog,
  Checkbox,
  TextField,
  IconButton,
  DialogTitle,
  DialogContent,
  DialogActions,
  InputAdornment,
  FormControlLabel,
} from '@mui/material';

import { useAppPassword } from 'src/hooks/use-app-password';

import { useTranslate } from 'src/locales/use-locales';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

// ----------------------------------------------------------------------

const MIN_PASSCODE_LENGTH = 6;

/**
 * 密码设置对话框组件
 * 专门用于初次设置密码，不需要验证旧密码
 * 内置完整的业务逻辑处理，包括错误处理和用户反馈
 */
export function PasswordSetupDialog({
  open,
  onClose,
  onSuc<PERSON>, // 设置成功回调
  onError, // 设置失败回调（可选）
}) {
  const { t } = useTranslate();
  const { setPassword } = useAppPassword();

  // 表单状态
  const [newPasscode, setNewPasscode] = useState('');
  const [confirmPasscode, setConfirmPasscode] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [agreedToTerms, setAgreedToTerms] = useState(false);

  // 新增：错误和加载状态
  const [errorMessage, setErrorMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleClose = () => {
    // 重置所有状态
    setNewPasscode('');
    setConfirmPasscode('');
    setShowNewPassword(false);
    setShowConfirmPassword(false);
    setAgreedToTerms(false);
    setErrorMessage('');
    setIsSubmitting(false);
    onClose();
  };

  const handleConfirm = async () => {
    if (!newPasscode || newPasscode !== confirmPasscode) {
      return;
    }

    setIsSubmitting(true);
    setErrorMessage('');

    try {
      const success = await setPassword(newPasscode);

      if (success) {
        // 显示成功提示
        toast.success(t('settings:security.passwordSetSuccess'));

        // 调用成功回调
        if (onSuccess) {
          onSuccess();
        }

        handleClose();
      } else {
        const errorMsg = t('settings:security.passwordSetFailed');
        setErrorMessage(errorMsg);

        // 调用错误回调
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (err) {
      console.error('密码设置异常:', err);
      const errorMsg = err.message || t('settings:security.passwordSetError');
      setErrorMessage(errorMsg);

      // 调用错误回调
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const isValid =
    newPasscode &&
    newPasscode === confirmPasscode &&
    newPasscode.length >= MIN_PASSCODE_LENGTH &&
    agreedToTerms;

  // 获取按钮文案和状态
  const getButtonState = () => {
    if (isSubmitting) {
      return { text: t('settings:security.settingPassword'), disabled: true };
    }

    if (!newPasscode && !confirmPasscode) {
      return { text: t('settings:security.confirm'), disabled: true };
    }

    if (newPasscode && newPasscode.length < 6) {
      return { text: t('settings:security.passwordTooShort'), disabled: true };
    }

    if (confirmPasscode && newPasscode !== confirmPasscode) {
      return { text: t('settings:security.passwordMismatch'), disabled: true };
    }

    if (
      newPasscode &&
      confirmPasscode &&
      newPasscode === confirmPasscode &&
      newPasscode.length >= MIN_PASSCODE_LENGTH &&
      !agreedToTerms
    ) {
      return { text: t('settings:security.pleaseAgreeToTerms'), disabled: true };
    }

    if (isValid) {
      return { text: t('settings:security.confirm'), disabled: false };
    }

    return { text: t('settings:security.confirm'), disabled: true };
  };

  const buttonState = getButtonState();

  return (
    <Dialog
      open={open}
      onClose={isSubmitting ? undefined : handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 3,
            p: 1,
          },
        },
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        {t('settings:security.setPasscode')}
        <IconButton onClick={handleClose} size="small" disabled={isSubmitting}>
          <Iconify icon="mingcute:close-line" />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: 3, pb: 2 }}>
        <Box sx={{ mt: 2 }}>
          {/* 错误提示 */}
          {errorMessage && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {errorMessage}
            </Alert>
          )}

          {/* 新密码输入框 */}
          <Box sx={{ mb: 3 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.newPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showNewPassword ? 'text' : 'password'}
              value={newPasscode}
              onChange={(e) => setNewPasscode(e.target.value)}
              placeholder={t('settings:security.createStrongPasscode')}
              variant="outlined"
              size="medium"
              disabled={isSubmitting}
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowNewPassword(!showNewPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                        disabled={isSubmitting}
                      >
                        <Iconify
                          icon={showNewPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>

          {/* 确认密码输入框 */}
          <Box sx={{ mb: 2 }}>
            <Box sx={{ mb: 1, fontSize: '0.875rem', fontWeight: 500 }}>
              {t('settings:security.confirmPasscode')}
            </Box>
            <TextField
              fullWidth
              type={showConfirmPassword ? 'text' : 'password'}
              value={confirmPasscode}
              onChange={(e) => setConfirmPasscode(e.target.value)}
              placeholder={t('settings:security.reenterPasscode')}
              variant="outlined"
              size="medium"
              disabled={isSubmitting}
              error={confirmPasscode && newPasscode !== confirmPasscode}
              helperText={
                confirmPasscode && newPasscode !== confirmPasscode
                  ? t('settings:security.passwordMismatch')
                  : ''
              }
              slotProps={{
                input: {
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        edge="end"
                        size="small"
                        tabIndex={-1}
                        disabled={isSubmitting}
                      >
                        <Iconify
                          icon={showConfirmPassword ? 'solar:eye-closed-linear' : 'solar:eye-bold'}
                        />
                      </IconButton>
                    </InputAdornment>
                  ),
                },
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                },
              }}
            />
          </Box>

          {/* 密码忘记提示确认 */}
          <Box sx={{ mb: 2 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={agreedToTerms}
                  onChange={(e) => setAgreedToTerms(e.target.checked)}
                  color="primary"
                  disabled={isSubmitting}
                />
              }
              label={
                <span
                  dangerouslySetInnerHTML={{ __html: t('settings:security.passwordForgetTip') }}
                />
              }
            />
          </Box>
        </Box>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button
          fullWidth
          variant="contained"
          onClick={handleConfirm}
          disabled={buttonState.disabled}
          sx={{
            borderRadius: 2,
            py: 1.5,
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          {buttonState.text}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
