/**
 * 字符串安全显示函数
 * 将字符串中间部分用星号替换，只显示前后指定长度的明文
 * @param {string} str - 需要处理的字符串
 * @param {number} prefixLength - 前面明文显示的长度
 * @param {number} suffixLength - 后面明文显示的长度
 * @returns {string} 处理后的字符串
 */
export function maskString(str, prefixLength = 4, suffixLength = 4) {
  // 参数验证
  if (typeof str !== 'string') {
    return '';
  }

  if (str.length === 0) {
    return str;
  }

  // 确保参数为非负整数
  const prefix = Math.max(0, Math.floor(prefixLength));
  const suffix = Math.max(0, Math.floor(suffixLength));

  // 如果字符串长度小于等于前后明文长度之和，直接返回原字符串
  if (str.length <= prefix + suffix) {
    return str;
  }

  // 提取前缀和后缀
  const prefixStr = str.substring(0, prefix);
  const suffixStr = str.substring(str.length - suffix);

  // 计算中间需要替换的长度
  const middleLength = str.length - prefix - suffix;

  // 生成星号字符串，最多显示5个星号
  const maskStr = '*'.repeat(Math.min(middleLength, 5));

  return prefixStr + maskStr + suffixStr;
}

/**
 * 预设的常用格式化函数
 */
export const formatters = {
  // 手机号格式化 (显示前3位和后4位)
  phone: (str) => maskString(str, 3, 4),

  // 身份证号格式化 (显示前6位和后4位)
  idCard: (str) => maskString(str, 6, 4),

  // 银行卡号格式化 (显示前4位和后4位)
  bankCard: (str) => maskString(str, 4, 4),

  // 邮箱格式化 (保留@前1位和@后完整域名)
  email: (str) => {
    if (typeof str !== 'string' || !str.includes('@')) {
      return str;
    }
    const [localPart, domain] = str.split('@');
    if (localPart.length <= 1) {
      return str;
    }
    return maskString(localPart, 1, 0) + '@' + domain;
  },

  // 钱包地址格式化 (显示前6位和后6位)
  walletAddress: (str) => maskString(str, 6, 6),

  // 私钥格式化 (显示前4位和后4位)
  privateKey: (str) => maskString(str, 4, 4),

  // 助记词格式化 (完全隐藏，只显示单词数量)
  mnemonic: (str) => maskMnemonic(str, true),
};

/**
 * 通用文本格式化函数
 * @param {string} str - 需要格式化的字符串
 * @param {string} type - 格式化类型
 * @param {object} options - 自定义选项
 * @returns {string} 格式化后的字符串
 */
export function formatText(str, type = 'default', options = {}) {
  if (typeof str !== 'string') {
    return '';
  }

  // 如果有预设的格式化函数，使用预设函数
  if (formatters[type]) {
    return formatters[type](str);
  }

  // 否则使用自定义参数
  const { prefixLength = 4, suffixLength = 4 } = options;
  return maskString(str, prefixLength, suffixLength);
}

/**
 * 安全的助记词显示函数
 * 助记词是高度敏感的信息，不应显示任何明文内容
 * @param {string} str - 助记词字符串
 * @param {boolean} showWordCount - 是否显示单词数量
 * @returns {string} 安全格式化后的字符串
 */
export function maskMnemonic(str, showWordCount = true) {
  if (typeof str !== 'string' || str.length === 0) {
    return '';
  }

  // 计算单词数量
  const wordCount = str.trim().split(/\s+/).length;

  if (showWordCount) {
    return `****** (${wordCount} words)`;
  }

  return '******';
}

export default {
  maskString,
  maskMnemonic,
  formatters,
  formatText,
};
