/**
 * 统一错误处理工具
 * 处理前端与后端交互中的各种错误情况
 */

import { toast } from 'src/components/snackbar';

// 错误类型枚举
export const ERROR_TYPES = {
  NOT_INITIALIZED: 'NotInitialized',
  VALIDATION_ERROR: 'ValidationError',
  NETWORK_ERROR: 'NetworkError',
  PERMISSION_ERROR: 'PermissionError',
  UNKNOWN_ERROR: 'UnknownError',
};

// 静默错误类型 - 这些错误不需要显示toast提示
const SILENT_ERRORS = [ERROR_TYPES.NOT_INITIALIZED];

/**
 * 从错误对象中提取错误消息
 * @param {Error|string|object} error - 错误对象
 * @returns {string} 错误消息
 */
export function getErrorMessage(error) {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.error) {
    return getErrorMessage(error.error);
  }

  return String(error || 'Unknown error');
}

/**
 * 错误分类器 - 根据错误内容确定错误类型
 * @param {Error|string|object} error - 错误对象
 * @returns {string} 错误类型
 */
export function classifyError(error) {
  const errorMessage = getErrorMessage(error);

  // 数据库未初始化错误
  if (errorMessage === 'NotInitialized' || errorMessage.includes('NotInitialized')) {
    return ERROR_TYPES.NOT_INITIALIZED;
  }

  // 验证错误
  if (errorMessage.includes('validation') || errorMessage.includes('invalid')) {
    return ERROR_TYPES.VALIDATION_ERROR;
  }

  // 网络错误
  if (errorMessage.includes('network') || errorMessage.includes('connection')) {
    return ERROR_TYPES.NETWORK_ERROR;
  }

  // 权限错误
  if (errorMessage.includes('permission') || errorMessage.includes('unauthorized')) {
    return ERROR_TYPES.PERMISSION_ERROR;
  }

  return ERROR_TYPES.UNKNOWN_ERROR;
}

/**
 * 检查错误是否应该静默处理
 * @param {string} errorType - 错误类型
 * @returns {boolean} 是否应该静默处理
 */
export function isSilentError(errorType) {
  return SILENT_ERRORS.includes(errorType);
}

/**
 * 统一错误处理器
 * @param {Error|string|object} error - 错误对象
 * @param {object} options - 处理选项
 * @param {function} options.onInitializationRequired - 需要初始化时的回调
 * @param {boolean} options.showToast - 是否显示toast提示，默认true
 * @param {function} options.onError - 自定义错误处理回调
 * @param {boolean} options.silent - 是否静默处理所有错误
 * @returns {object} 处理结果
 */
export function handleServiceError(error, options = {}) {
  const { onInitializationRequired, showToast = true, onError, silent = false } = options;

  const errorType = classifyError(error);
  const errorMessage = getErrorMessage(error);

  // 如果有自定义错误处理回调，先执行
  if (onError) {
    const customResult = onError(error, errorType, errorMessage);
    if (customResult?.handled) {
      return customResult;
    }
  }

  // 根据错误类型进行特定处理
  switch (errorType) {
    case ERROR_TYPES.NOT_INITIALIZED:
      // 数据库未初始化 - 静默处理，不显示错误提示
      console.log('数据库未初始化，等待初始化完成');
      if (onInitializationRequired) {
        onInitializationRequired();
      }
      return {
        handled: true,
        requiresInitialization: true,
        errorType,
        message: errorMessage,
      };

    case ERROR_TYPES.VALIDATION_ERROR:
      // 验证错误 - 通常需要用户修正输入
      if (showToast && !silent) {
        toast.error(`验证失败: ${errorMessage}`);
      }
      return {
        handled: true,
        requiresUserAction: true,
        errorType,
        message: errorMessage,
      };

    case ERROR_TYPES.NETWORK_ERROR:
      // 网络错误 - 可能需要重试
      if (showToast && !silent) {
        toast.error('网络连接失败，请检查网络状态');
      }
      return {
        handled: true,
        canRetry: true,
        errorType,
        message: errorMessage,
      };

    case ERROR_TYPES.PERMISSION_ERROR:
      // 权限错误 - 需要用户授权
      if (showToast && !silent) {
        toast.error('权限不足，请检查访问权限');
      }
      return {
        handled: true,
        requiresPermission: true,
        errorType,
        message: errorMessage,
      };

    default:
      // 未知错误 - 显示通用错误消息
      if (showToast && !silent && !isSilentError(errorType)) {
        toast.error(errorMessage || '操作失败');
      }
      return {
        handled: false,
        errorType,
        message: errorMessage,
      };
  }
}

/**
 * 包装服务调用，自动处理错误
 * @param {Promise} serviceCall - 服务调用Promise
 * @param {object} options - 处理选项
 * @param {any} options.defaultValue - 错误时返回的默认值
 * @param {function} options.onInitializationRequired - 需要初始化时的回调
 * @param {boolean} options.showToast - 是否显示toast提示
 * @param {function} options.onError - 自定义错误处理回调
 * @param {boolean} options.silent - 是否静默处理所有错误
 * @param {boolean} options.rethrowUnhandled - 是否重新抛出未处理的错误
 * @returns {Promise} 包装后的Promise
 */
export function wrapServiceCall(serviceCall, options = {}) {
  const { defaultValue, rethrowUnhandled = false, ...errorOptions } = options;

  return serviceCall.catch((error) => {
    const result = handleServiceError(error, errorOptions);

    if (result.handled || result.requiresInitialization) {
      // 错误已处理或需要初始化，返回默认值
      return defaultValue;
    }

    // 未处理的错误
    if (rethrowUnhandled) {
      throw error;
    }

    return defaultValue;
  });
}

/**
 * 批量包装服务对象的所有方法
 * @param {object} service - 服务对象
 * @param {object} defaultOptions - 默认处理选项
 * @returns {object} 包装后的服务对象
 */
export function wrapService(service, defaultOptions = {}) {
  const wrappedService = {};

  Object.keys(service).forEach((key) => {
    if (typeof service[key] === 'function') {
      wrappedService[key] = (...args) => wrapServiceCall(service[key](...args), defaultOptions);
    } else {
      wrappedService[key] = service[key];
    }
  });

  return wrappedService;
}

/**
 * 为列表组件创建专用的错误处理函数
 * @param {object} options - 选项
 * @param {function} options.onInitializationRequired - 需要初始化时的回调
 * @returns {function} 错误处理函数
 */
export function createListErrorHandler(options = {}) {
  return (error) =>
    handleServiceError(error, {
      ...options,
      showToast: false, // 列表组件通常不需要显示错误toast
      silent: true, // 静默处理，避免干扰用户体验
    });
}
