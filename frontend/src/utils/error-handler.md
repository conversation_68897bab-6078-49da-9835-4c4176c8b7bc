# 统一错误处理使用指南

## 概述

本项目提供了统一的错误处理机制，特别针对 `NotInitialized` 错误进行了优化处理。该机制可以：

- 自动识别和静默处理 `NotInitialized` 错误
- 提供类型化的错误分类
- 简化组件中的错误处理逻辑
- 避免重复的 try-catch 代码

## 核心组件

### 1. 错误处理工具 (`src/utils/error-handler.js`)

提供底层的错误分类和处理功能：

```javascript
import { handleServiceError, wrapServiceCall } from 'src/utils/error-handler';

// 基础错误处理
const result = handleServiceError(error, {
  onInitializationRequired: () => console.log('需要初始化'),
  showToast: false,
});

// 包装服务调用
const data = await wrapServiceCall(
  SomeService.getData(),
  { defaultValue: [], silent: true }
);
```

### 2. 错误处理 Hooks (`src/hooks/use-service-error-handler.js`)

为组件提供便捷的错误处理能力：

- `useServiceErrorHandler` - 通用错误处理
- `useListErrorHandler` - 专用于列表组件
- `useFormErrorHandler` - 专用于表单组件

## 使用方法

### 列表组件中的使用

最常见的使用场景，适用于所有数据列表组件：

```javascript
import { useListErrorHandler } from 'src/hooks/use-service-error-handler';

export function SomeList() {
  const { handleListData } = useListErrorHandler({
    onInitializationRequired: () => {
      // 可选：初始化提示逻辑
      console.log('数据库需要初始化');
    },
  });

  const getData = useCallback(async (page, pageSize, orderBy, order, searchValue) => {
    // 构建请求参数
    const params = { page: page + 1, pageSize, orderBy, order };
    
    // 使用统一错误处理
    const result = await handleListData(
      SomeService.Page(params, searchValue)
    );
    
    return [result?.data || [], result?.total || 0];
  }, [handleListData]);

  // 其他组件逻辑...
}
```

### 表单组件中的使用

适用于表单提交、数据编辑等操作：

```javascript
import { useFormErrorHandler } from 'src/hooks/use-service-error-handler';

export function SomeForm() {
  const { handleFormSubmit } = useFormErrorHandler({
    onInitializationRequired: () => {
      // 表单提交时如果数据库未初始化的处理
    },
  });

  const onSubmit = async (formData) => {
    try {
      await handleFormSubmit(SomeService.Create(formData));
      toast.success('创建成功');
    } catch (error) {
      // 这里只处理特定的业务错误
      // NotInitialized 等错误已被自动处理
    }
  };
}
```

### 通用服务调用

对于不属于列表或表单的其他服务调用：

```javascript
import { useServiceErrorHandler } from 'src/hooks/use-service-error-handler';

export function SomeComponent() {
  const { withErrorHandling } = useServiceErrorHandler({
    showToast: true,
    onInitializationRequired: () => {
      // 自定义初始化处理
    },
  });

  const handleAction = async () => {
    const result = await withErrorHandling(
      SomeService.performAction(),
      { defaultValue: null }
    );
    
    if (result) {
      // 处理成功的结果
    }
  };
}
```

## 错误类型

系统自动识别以下错误类型：

- `NotInitialized` - 数据库未初始化（静默处理）
- `ValidationError` - 验证错误
- `NetworkError` - 网络错误
- `PermissionError` - 权限错误
- `UnknownError` - 未知错误

## 迁移现有代码

### 迁移前 (旧方式)

```javascript
const getData = useCallback(async (page, pageSize) => {
  try {
    const result = await SomeService.Page(page, pageSize);
    return [result?.data || [], result?.total || 0];
  } catch (error) {
    console.error('获取数据失败:', error);
    toast.error('加载失败');
    return [[], 0];
  }
}, []);
```

### 迁移后 (新方式)

```javascript
const { handleListData } = useListErrorHandler();

const getData = useCallback(async (page, pageSize) => {
  const result = await handleListData(
    SomeService.Page(page, pageSize)
  );
  return [result?.data || [], result?.total || 0];
}, [handleListData]);
```

## 优势

1. **代码简化** - 减少重复的 try-catch 代码
2. **统一体验** - 所有组件的错误处理行为一致
3. **智能处理** - 自动识别 NotInitialized 等预期错误
4. **易于维护** - 错误处理逻辑集中管理
5. **类型安全** - 提供完整的 TypeScript 支持

## 最佳实践

1. **列表组件** - 始终使用 `useListErrorHandler`
2. **表单组件** - 使用 `useFormErrorHandler` 处理提交
3. **自定义初始化** - 在 `onInitializationRequired` 中添加初始化提示
4. **保持简洁** - 让错误处理器处理通用错误，组件只关注业务逻辑
5. **测试覆盖** - 确保错误处理逻辑的测试覆盖率