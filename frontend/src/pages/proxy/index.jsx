import { useRef, useState } from 'react';

import { Box, Typography } from '@mui/material';

import { useInitializationManager } from 'src/hooks/use-initialization-manager';

import { useTranslate } from 'src/locales';

import { ImportExportAction } from 'src/components/action';

import { ListView } from 'src/sections/proxy/list';
import { ProxyNewDialog } from 'src/sections/proxy/proxy-new-dialog';

export default function Page() {
  const { t } = useTranslate(['proxy', 'common']);
  const { wrapAction, renderInitialization } = useInitializationManager();
  const listViewRef = useRef(null);
  const [dialogOpen, setDialogOpen] = useState(false);

  const handleImport = () => {
    setDialogOpen(true);
  };

  const handleDialogClose = () => {
    setDialogOpen(false);
  };

  const handleExport = () => {
    console.log('export');
  };

  // 使用统一的初始化管理包装操作
  const handleImportWithPasswordCheck = wrapAction(handleImport);
  const handleExportWithPasswordCheck = wrapAction(handleExport);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
        }}
      >
        <Typography variant="h5" sx={{ mb: 3 }}>
          {t('proxy:title')}
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <ImportExportAction
            onImport={handleImportWithPasswordCheck}
            onExport={handleExportWithPasswordCheck}
            sx={{ xs: 1, md: 3 }}
          />
        </Box>
      </Box>

      <ListView ref={listViewRef} />

      {dialogOpen && <ProxyNewDialog open={dialogOpen} onClose={handleDialogClose} />}

      {/* 统一的初始化UI管理 */}
      {renderInitialization()}
    </>
  );
}
