import { useRef, useCallback } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import { Box, Button } from '@mui/material';
import Typography from '@mui/material/Typography';

import { useInitializationManager } from 'src/hooks/use-initialization-manager';

import { useTranslate } from 'src/locales';

import { Iconify } from 'src/components/iconify';

import { WalletList } from 'src/sections/wallet/wallet-list';
import { WalletNewDialog } from 'src/sections/wallet/wallet-new-dialog';
import { WalletImportDialog } from 'src/sections/wallet/wallet-import-dialog';

export default function WalletPage() {
  const { t } = useTranslate('wallet');
  const { wrapAction, renderInitialization } = useInitializationManager();
  const newDialog = useBoolean(false);
  const importDialog = useBoolean(false);
  const walletListRef = useRef(null);

  // 使用引用来避免重复 refetch 调用
  const isRefetchingRef = useRef(false);

  // 防重复的 refetch 函数
  const performRefetch = useCallback(() => {
    if (isRefetchingRef.current) {
      return; // 如果正在执行 refetch，跳过这次调用
    }

    isRefetchingRef.current = true;
    walletListRef.current?.refetch();

    // 500ms 后重置标记，防止正常的后续操作被阻止
    setTimeout(() => {
      isRefetchingRef.current = false;
    }, 500);
  }, []);

  // 处理新建钱包成功
  const handleNewWalletSuccess = useCallback(() => {
    performRefetch();
  }, [performRefetch]);

  // 处理导入钱包成功
  const handleImportSuccess = useCallback(() => {
    performRefetch();
  }, [performRefetch]);

  // 使用统一的初始化管理包装操作
  const handleCreateWallet = wrapAction(newDialog.onTrue);
  const handleImportWallet = wrapAction(importDialog.onTrue);

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          mb: 3,
        }}
      >
        <Typography variant="h5">{t('wallet:title')}</Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="text"
            startIcon={<Iconify icon="mingcute:add-circle-fill" />}
            onClick={handleCreateWallet}
          >
            {t('wallet:create')}
          </Button>

          <Button
            variant="text"
            startIcon={<Iconify icon="mingcute:arrow-down-circle-fill" />}
            onClick={handleImportWallet}
          >
            {t('wallet:import')}
          </Button>
        </Box>
      </Box>

      {/* 钱包列表 */}
      <WalletList
        ref={walletListRef}
        onCreateClick={handleCreateWallet}
        onImportClick={handleImportWallet}
      />

      {/* 对话框 */}
      {newDialog.value && (
        <WalletNewDialog
          open={newDialog.value}
          onClose={newDialog.onFalse}
          onSuccess={handleNewWalletSuccess}
        />
      )}
      {importDialog.value && (
        <WalletImportDialog
          open={importDialog.value}
          onClose={importDialog.onFalse}
          onSuccess={handleImportSuccess}
        />
      )}

      {/* 统一的初始化UI管理 */}
      {renderInitialization()}
    </>
  );
}
