import { Outlet } from 'react-router';
import { lazy, Suspense } from 'react';

import { CONFIG } from 'src/global-config';
import { DashboardLayout, DashboardContent } from 'src/layouts/dashboard';

import { LoadingScreen } from 'src/components/loading-screen';

import { AccountLayout } from 'src/sections/account/account-layout';

import { AuthGuard } from 'src/auth/guard';

import { paths } from '../paths';
import { usePathname } from '../hooks';

// ----------------------------------------------------------------------

// Dashboard
const OverviewAppPage = lazy(() => import('src/pages/dashboard'));

//Wallet
const WalletIndex = lazy(() => import('src/pages/wallet/index'));

//Proxy
const ProxyIndex = lazy(() => import('src/pages/proxy/index'));

const MailPage = lazy(() => import('src/pages/account/mail'));
const XPage = lazy(() => import('src/pages/account/x'));
const TelegramPage = lazy(() => import('src/pages/account/telegram'));
const DiscordPage = lazy(() => import('src/pages/account/discord'));

//Profile
const ProfilePage = lazy(() => import('src/pages/profile'));

//Settings
const SettingsPage = lazy(() => import('src/pages/settings'));

// ----------------------------------------------------------------------

function SuspenseOutlet() {
  const pathname = usePathname();
  return (
    <Suspense key={pathname} fallback={<LoadingScreen />}>
      <Outlet />
    </Suspense>
  );
}

const dashboardLayout = () => (
  <DashboardLayout>
    <DashboardContent maxWidth="xl">
      <SuspenseOutlet />
    </DashboardContent>
  </DashboardLayout>
);

const accountLayout = () => {
  // 处理添加单个账户
  const handleAddIndividual = () => {
    console.log('添加单个账户');
  };

  // 处理批量添加账户
  const handleBatchAdd = () => {
    console.log('批量添加账户');
  };

  // 处理导出账户
  const handleExport = () => {
    console.log('导出账户');
  };

  return (
    <AccountLayout
      onAddIndividual={handleAddIndividual}
      onBatchAdd={handleBatchAdd}
      onExport={handleExport}
    >
      <SuspenseOutlet />
    </AccountLayout>
  );
};

export const dashboardRoutes = [
  {
    element: CONFIG.auth.skip ? dashboardLayout() : <AuthGuard>{dashboardLayout()}</AuthGuard>,
    children: [
      { path: paths.dashboard.root, element: <OverviewAppPage /> },
      {
        path: paths.wallet.root,
        children: [{ index: true, element: <WalletIndex /> }],
      },
      {
        path: paths.proxy.root,
        children: [{ index: true, element: <ProxyIndex /> }],
      },
      {
        path: paths.account.root,
        element: accountLayout(),
        children: [
          { index: true, element: <MailPage /> },
          { path: paths.account.x.root, element: <XPage /> },
          { path: paths.account.telegram.root, element: <TelegramPage /> },
          { path: paths.account.discord.root, element: <DiscordPage /> },
        ],
      },
      {
        path: paths.profile.root,
        children: [{ index: true, element: <ProfilePage /> }],
      },
      {
        path: paths.settings.root,
        children: [{ index: true, element: <SettingsPage /> }],
      },
    ],
  },
];
