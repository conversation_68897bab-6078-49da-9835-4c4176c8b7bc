import { paths } from './routes/paths.js';
import packageJson from '../package.json' with { type: 'json' };

// ----------------------------------------------------------------------

export const CONFIG = {
  appName: 'A8Tools',
  appVersion: packageJson.version,
  serverUrl: import.meta.env.VITE_SERVER_URL ?? '',
  assetsDir: import.meta.env.VITE_ASSETS_DIR ?? '',
  /**
   * Auth
   * @method jwt
   */
  auth: {
    method: 'jwt',
    skip: true,
    redirectPath: paths.dashboard.root,
  },
};

export const SUPPORTED_COINTYPES = [
  { value: 'aptos', label: 'Aptos', id: 'aptos', network: 'Aptos' },
  { value: 'bsc', label: 'BSC', id: 'binance-smart-chain', network: 'BNB Smart Chain' },
  { value: 'btc', label: 'BTC', id: 'bitcoin', network: 'Bitcoin' },
  { value: 'cardano', label: 'Cardano', id: 'cardano', network: 'Cardano' },
  { value: 'cosmos', label: 'Cosmos', id: 'cosmos', network: 'Cosmos' },
  { value: 'eos', label: 'EOS', id: 'eos', network: 'EOS' },
  { value: 'eth', label: 'EVM', id: 'ethereum', network: 'Ethereum' },
  { value: 'near', label: 'Near', id: 'near-protocol', network: 'Near' },
  { value: 'sol', label: 'Sol', id: 'solana', network: 'Solana' },
  { value: 'stacks', label: 'Stacks', id: 'stacks', network: 'Stacks' },
  { value: 'starknet', label: 'Starknet', id: 'starknet', network: 'Starknet' },
  { value: 'sui', label: 'Sui', id: 'sui', network: 'Sui' },
  { value: 'ton', label: 'TON', id: 'ton', network: 'TON' },
  { value: 'trx', label: 'TRX', id: 'tron', network: 'Tron' },
];
