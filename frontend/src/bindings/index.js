// 模型导出
export { Wallet } from '../../bindings/a8.tools/backend/database/models/models.js';

// 服务导出
export * as XService from '../../bindings/a8.tools/backend/services/account/xservice.js';

export * as MailService from '../../bindings/a8.tools/backend/services/account/mailservice.js';

export * as ProxyService from '../../bindings/a8.tools/backend/services/account/proxyservice.js';
export * as WalletService from '../../bindings/a8.tools/backend/services/wallet/walletservice.js';
export * as DiscordService from '../../bindings/a8.tools/backend/services/account/discordservice.js';
export * as SettingService from '../../bindings/a8.tools/backend/services/setting/settingservice.js';
export * as TelegramService from '../../bindings/a8.tools/backend/services/account/telegramservice.js';
export * as AppPasswordService from '../../bindings/a8.tools/backend/services/password/passwordmanager.js';
// 分页和查询相关模型
export {
  Pageable,
  PageResult,
  QueryBuilder,
  QueryCondition,
} from '../../bindings/a8.tools/backend/services/models.js';
export {
  X,
  Proxy,
  Email,
  Discord,
  Telegram,
} from '../../bindings/a8.tools/backend/database/models/account/models.js';
