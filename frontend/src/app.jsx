import 'src/global.css';

import { useEffect } from 'react';

import { paths } from 'src/routes/paths';
import { usePathname } from 'src/routes/hooks';

import { LocalizationProvider } from 'src/locales';
import { themeConfig, ThemeProvider } from 'src/theme';
import { I18nProvider } from 'src/locales/i18n-provider';

import AutoLock from 'src/components/auto-lock';
import { Snackbar } from 'src/components/snackbar';
import { ProgressBar } from 'src/components/progress-bar';
import { MotionLazy } from 'src/components/animate/motion-lazy';
import { AuthFlowManager } from 'src/components/auth-flow/auth-flow-manager';
import { clearInitialLockTriggered } from 'src/components/auto-lock/lock-guard';
import { SettingsDrawer, defaultSettings, SettingsProvider } from 'src/components/settings';
import { PasswordInputDialogContainer } from 'src/components/password/password-input-dialog';

import { AuthProvider as JwtAuth<PERSON>rovider } from 'src/auth/context/jwt';

// ----------------------------------------------------------------------

const AuthProvider = JwtAuthProvider;

// ----------------------------------------------------------------------

export default function App({ children }) {
  useScrollToTop();
  // 禁用右键
  useDisableRightClick();
  const pathname = usePathname();

  // 应用启动时清除初始锁定标记，确保每次重启应用都会触发初始锁定
  useEffect(() => {
    clearInitialLockTriggered();
  }, []);

  return (
    <I18nProvider>
      <AuthProvider>
        <SettingsProvider defaultSettings={defaultSettings}>
          <LocalizationProvider>
            <ThemeProvider
              modeStorageKey={themeConfig.modeStorageKey}
              defaultMode={themeConfig.enableSystemMode ? 'system' : themeConfig.defaultMode}
            >
              <MotionLazy>
                <Snackbar />
                <ProgressBar />
                <SettingsDrawer defaultSettings={defaultSettings} />
                {pathname !== paths.lock && <AutoLock />}
                {/* 新的认证流程管理器 - 统一管理认证、密码检查和锁屏流程 */}
                <AuthFlowManager>{children}</AuthFlowManager>

                {/* 密码输入对话框 - 用于处理密码失效等异常情况 */}
                <PasswordInputDialogContainer />
              </MotionLazy>
            </ThemeProvider>
          </LocalizationProvider>
        </SettingsProvider>
      </AuthProvider>
    </I18nProvider>
  );
}

// ----------------------------------------------------------------------

function useScrollToTop() {
  const pathname = usePathname();

  useEffect(() => {
    window.scrollTo(0, 0);
  }, [pathname]);

  return null;
}

// ----------------------------------------------------------------------

function useDisableRightClick() {
  useEffect(() => {
    // 只在生产环境禁用右键
    if (import.meta.env.PROD) {
      const handleContextMenu = (e) => {
        e.preventDefault();
        return false;
      };

      document.addEventListener('contextmenu', handleContextMenu);

      return () => {
        document.removeEventListener('contextmenu', handleContextMenu);
      };
    }

    // 开发环境下返回undefined（不执行任何操作）
    return undefined;
  }, []);

  return null;
}
