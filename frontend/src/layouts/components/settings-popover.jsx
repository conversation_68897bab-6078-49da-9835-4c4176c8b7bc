import { m } from 'framer-motion';
import { useCallback } from 'react';
import { usePopover } from 'minimal-shared/hooks';
import { useLocation, useNavigate } from 'react-router';

import Box from '@mui/material/Box';
import Switch from '@mui/material/Switch';
import Avatar from '@mui/material/Avatar';
import SvgIcon from '@mui/material/SvgIcon';
import Divider from '@mui/material/Divider';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import IconButton from '@mui/material/IconButton';
import Typography from '@mui/material/Typography';
import { useColorScheme } from '@mui/material/styles';

import { useRouter } from 'src/routes/hooks';

import { useAppPassword } from 'src/hooks/use-app-password';

import { useTranslate } from 'src/locales';

import { CustomPopover } from 'src/components/custom-popover';
import { lockScreen } from 'src/components/auto-lock/lock-guard';
import { varTap, varHover, transitionTap } from 'src/components/animate';

// ----------------------------------------------------------------------

export function SettingsPopover({ data = [], sx, ...other }) {
  const router = useRouter();
  const navigate = useNavigate();
  const location = useLocation();
  const { open, anchorEl, onClose, onOpen } = usePopover();
  const {
    open: langOpen,
    anchorEl: langAnchorEl,
    onClose: onLangClose,
    onOpen: onLangOpen,
  } = usePopover();

  const { mode, setMode } = useColorScheme();
  const { onChangeLang, currentLang } = useTranslate();
  const { hasPassword, lockApplication } = useAppPassword();
  const { t } = useTranslate();
  const handleToggleDarkMode = () => {
    setMode(mode === 'light' ? 'dark' : 'light');
  };

  const handleLockApp = async () => {
    onClose();

    // 只有设置了密码才允许手动锁定
    if (!hasPassword) {
      // 可以显示提示信息，告诉用户需要先设置密码
      console.warn('请先设置应用密码才能锁定应用');
      return;
    }

    try {
      // 调用后端锁定应用，清除内存密码
      const lockSuccess = await lockApplication();

      if (lockSuccess) {
        // 后端锁定成功后，导航到锁屏页面
        lockScreen(navigate, location.pathname);
      } else {
        console.error('锁定应用失败：后端锁定操作未成功');
        // 即使后端锁定失败，为了安全起见仍然导航到锁屏页面
        lockScreen(navigate, location.pathname);
      }
    } catch (error) {
      console.error('锁定应用时发生错误:', error);
      // 发生错误时仍然导航到锁屏页面，确保用户界面被锁定
      lockScreen(navigate, location.pathname);
    }
  };

  const handleSettingsClick = () => {
    onClose();
    if (router.pathname !== '/settings') {
      router.push('/settings');
    }
  };

  const handleLanguageClick = (event) => {
    onLangOpen(event);
  };

  const handleChangeLang = useCallback(
    (newLang) => {
      onChangeLang(newLang);
      onLangClose();
      onClose();
    },
    [onChangeLang, onLangClose, onClose]
  );

  const languageOptions = [
    { label: '简体中文', value: 'cn' },
    { label: 'English', value: 'en' },
  ];

  const renderLanguageList = () => (
    <CustomPopover
      open={langOpen}
      anchorEl={langAnchorEl}
      onClose={onLangClose}
      slotProps={{
        arrow: { placement: 'right-center' },
        paper: {
          sx: {
            marginLeft: '-18px !important', // 强制左边距
          },
        },
      }}
    >
      <MenuList sx={{ width: 160, minHeight: 72 }}>
        {languageOptions.map((option) => (
          <MenuItem
            key={option.value}
            selected={option.value === currentLang.value}
            onClick={() => handleChangeLang(option.value)}
          >
            {option.label}
          </MenuItem>
        ))}
      </MenuList>
    </CustomPopover>
  );

  const renderSettingList = () => (
    <CustomPopover open={open} anchorEl={anchorEl} onClose={onClose}>
      <MenuList sx={{ width: 245, minHeight: 72, p: 1 }}>
        {/* 用户信息 */}
        <Box sx={{ px: 2, py: 1.5, mb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
            <Avatar sx={{ width: 40, height: 40, bgcolor: 'primary.main' }}>L</Avatar>
            <Box>
              <Typography variant="subtitle2" noWrap>
                liuzc89 team
              </Typography>
              <Typography variant="body2" color="text.secondary" noWrap>
                <EMAIL>
              </Typography>
            </Box>
          </Box>
        </Box>
        <Divider sx={{ borderStyle: 'solid', borderColor: 'divider' }} />
        {/* 设置中心 */}
        <MenuItem onClick={handleSettingsClick} sx={{ px: 2, py: 1.5, gap: 1.5 }}>
          <SvgIcon color="inherit">
            <path
              fill="currentColor"
              d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-.61-.23-1.18-.54-1.71-.91l.95-.95c.39.39.89.63 1.76.63s1.37-.24 1.76-.63l.95.95c-.53.37-1.1.68-1.71.91zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"
            />
          </SvgIcon>
          <Typography variant="body2">{t('settings:settings')}</Typography>
        </MenuItem>

        {/* 语言选择 */}
        <MenuItem onClick={handleLanguageClick} sx={{ px: 2, py: 1.5, gap: 1.5 }}>
          <SvgIcon color="inherit">
            <path
              fill="currentColor"
              d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2m6.93 6h-2.95a15.7 15.7 0 0 0-1.38-3.56A8.03 8.03 0 0 1 18.92 8M12 4.04c.83 1.2 1.48 2.53 1.91 3.96h-3.82c.43-1.43 1.08-2.76 1.91-3.96M4.26 14C4.1 13.36 4 12.69 4 12s.1-1.36.26-2h3.38c-.08.66-.14 1.32-.14 2s.06 1.34.14 2zm.82 2h2.95c.32 1.25.78 2.45 1.38 3.56A8 8 0 0 1 5.08 16m2.95-8H5.08a8 8 0 0 1 4.33-3.56A15.7 15.7 0 0 0 8.03 8M12 19.96c-.83-1.2-1.48-2.53-1.91-3.96h3.82c-.43 1.43-1.08 2.76-1.91 3.96M14.34 14H9.66c-.09-.66-.16-1.32-.16-2s.07-1.35.16-2h4.68c.09.65.16 1.32.16 2s-.07 1.34-.16 2m.25 5.56c.6-1.11 1.06-2.31 1.38-3.56h2.95a8.03 8.03 0 0 1-4.33 3.56M16.36 14c.08-.66.14-1.32.14-2s-.06-1.34-.14-2h3.38c.16.64.26 1.31.26 2s-.1 1.36-.26 2z"
            />
          </SvgIcon>
          <Typography variant="body2">
            {languageOptions.find((option) => option.value === currentLang.value)?.label ||
              '简体中文'}
          </Typography>
          <SvgIcon sx={{ ml: 'auto' }} color="inherit">
            <path fill="currentColor" d="m7 10l5 5 5-5z" />
          </SvgIcon>
        </MenuItem>

        {/* 深色模式 */}
        <MenuItem sx={{ px: 2, py: 1.5, gap: 1.5 }}>
          <SvgIcon color="inherit">
            <path
              fill="currentColor"
              d="M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9c0-.46-.04-.92-.1-1.36c-.98 1.37-2.58 2.26-4.4 2.26c-2.98 0-5.4-2.42-5.4-5.4c0-1.81.89-3.42 2.26-4.4c-.44-.06-.9-.1-1.36-.1z"
            />
          </SvgIcon>
          <Typography variant="body2">{t('settings:darkMode')}</Typography>
          <Switch
            checked={mode === 'dark'}
            onChange={handleToggleDarkMode}
            size="small"
            sx={{ ml: 'auto' }}
          />
        </MenuItem>

        <Divider sx={{ borderStyle: 'solid', borderColor: 'divider' }} />

        {/* 锁定应用 */}
        <MenuItem
          onClick={handleLockApp}
          disabled={!hasPassword}
          sx={{
            px: 2,
            py: 1.5,
            gap: 1.5,
            opacity: hasPassword ? 1 : 0.5,
          }}
        >
          <SvgIcon color="inherit">
            <path
              fill="currentColor"
              d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"
            />
          </SvgIcon>
          <Typography variant="body2">{t('settings:lockScreen')}</Typography>
        </MenuItem>
      </MenuList>
    </CustomPopover>
  );

  return (
    <>
      <IconButton
        component={m.button}
        whileTap={varTap(0.96)}
        whileHover={varHover(1.04)}
        transition={transitionTap()}
        aria-label="Settings button"
        onClick={onOpen}
        sx={[
          (theme) => ({
            p: 0,
            width: 40,
            height: 40,
            ...(open && { bgcolor: theme.vars.palette.action.selected }),
          }),
          ...(Array.isArray(sx) ? sx : [sx]),
        ]}
        {...other}
      >
        <SvgIcon
          component={m.svg}
          animate={{ rotate: 360 }}
          transition={{ duration: 8, ease: 'linear', repeat: Infinity }}
        >
          {/* https://icon-sets.iconify.design/solar/settings-bold-duotone/ */}
          <path
            fill="currentColor"
            fillRule="evenodd"
            d="M14.279 2.152C13.909 2 13.439 2 12.5 2s-1.408 0-1.779.152a2.008 2.008 0 0 0-1.09 1.083c-.094.223-.13.484-.145.863a1.615 1.615 0 0 1-.796 1.353a1.64 1.64 0 0 1-1.579.008c-.338-.178-.583-.276-.825-.308a2.026 2.026 0 0 0-1.49.396c-.318.242-.553.646-1.022 1.453c-.47.807-.704 1.21-.757 1.605c-.07.526.074 1.058.4 1.479c.148.192.357.353.68.555c.477.297.783.803.783 1.361c0 .558-.306 1.064-.782 1.36c-.324.203-.533.364-.682.556a1.99 1.99 0 0 0-.399 1.479c.053.394.287.798.757 1.605c.47.807.704 1.21 1.022 1.453c.424.323.96.465 1.49.396c.242-.032.487-.13.825-.308a1.64 1.64 0 0 1 1.58.008c.486.28.774.795.795 1.353c.015.38.051.64.145.863c.204.49.596.88 1.09 1.083c.37.152.84.152 1.779.152s1.409 0 1.779-.152a2.008 2.008 0 0 0 1.09-1.083c.094-.223.13-.483.145-.863c.02-.558.309-1.074.796-1.353a1.64 1.64 0 0 1 1.579-.008c.338.178.583.276.825.308c.53.07 1.066-.073 1.49-.396c.318-.242.553-.646 1.022-1.453c.47-.807.704-1.21.757-1.605a1.99 1.99 0 0 0-.4-1.479c-.148-.192-.357-.353-.68-.555c-.477-.297-.783-.803-.783-1.361c0-.558.306-1.064.782-1.36c.324-.203.533-.364.682-.556a1.99 1.99 0 0 0 .399-1.479c-.053-.394-.287-.798-.757-1.605c-.47-.807-.704-1.21-1.022-1.453a2.026 2.026 0 0 0-1.49-.396c-.242.032-.487.13-.825.308a1.64 1.64 0 0 1-1.58-.008a1.615 1.615 0 0 1-.795-1.353c-.015-.38-.051-.64-.145-.863a2.007 2.007 0 0 0-1.09-1.083"
            clipRule="evenodd"
            opacity="0.5"
          />
          <path
            fill="currentColor"
            d="M15.523 12c0 1.657-1.354 3-3.023 3c-1.67 0-3.023-1.343-3.023-3S10.83 9 12.5 9c1.67 0 3.023 1.343 3.023 3"
          />
        </SvgIcon>
      </IconButton>

      {renderSettingList()}
      {renderLanguageList()}
    </>
  );
}
