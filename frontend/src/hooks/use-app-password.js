import { useRef, useState, useEffect, useCallback } from 'react';

import { AppPasswordService } from '../bindings';
import { EventsOn, EventsOff } from '../bindings/runtime';

// 全局状态缓存
let globalPasswordState = null;
let globalStateSubscribers = new Set();
let isInitializing = false;

/**
 * 应用密码管理hook
 * 专门处理密码相关的操作，包括设置、验证、解锁等功能
 * 使用全局状态避免重复调用后端服务
 */
export function useAppPassword() {
  const [hasPassword, setHasPassword] = useState(globalPasswordState ?? false);
  const subscriberRef = useRef(null);

  // 新增生命周期管理相关状态
  const [passwordStatus, setPasswordStatus] = useState('unknown');
  const [showPasswordDialog, setShowPasswordDialog] = useState(false);
  const [passwordEvent, setPasswordEvent] = useState(null);

  // 订阅全局状态变化
  useEffect(() => {
    const updateState = (newState) => {
      setHasPassword(newState);
    };

    subscriberRef.current = updateState;
    globalStateSubscribers.add(updateState);

    return () => {
      globalStateSubscribers.delete(updateState);
      subscriberRef.current = null;
    };
  }, []);

  // 更新全局状态并通知所有订阅者
  const updateGlobalState = useCallback((newState) => {
    globalPasswordState = newState;
    globalStateSubscribers.forEach((subscriber) => subscriber(newState));
  }, []);

  // 刷新密码状态（带缓存和防重复调用）
  const refreshPasswordStatus = useCallback(async () => {
    // 如果正在初始化，避免重复调用
    if (isInitializing) {
      return globalPasswordState ?? false;
    }

    isInitializing = true;

    try {
      const result = await AppPasswordService.HasAppPassword();
      updateGlobalState(result);
      return result;
    } catch (error) {
      console.error('检查密码状态失败:', error);
      updateGlobalState(false);
      return false;
    } finally {
      isInitializing = false;
    }
  }, [updateGlobalState]);

  // 初始化密码状态（仅在全局状态为空时执行）
  useEffect(() => {
    if (globalPasswordState === null && !isInitializing) {
      refreshPasswordStatus();
    }
  }, [refreshPasswordStatus]);

  // 密码生命周期事件监听
  useEffect(() => {
    const handlePasswordRequired = (eventData) => {
      setPasswordEvent({ type: 'password_required', data: eventData });
      setShowPasswordDialog(true);
    };

    const handlePasswordInvalid = (eventData) => {
      setPasswordEvent({ type: 'password_invalid', data: eventData });
      setShowPasswordDialog(true);
    };

    const handlePasswordUpdated = (eventData) => {
      setShowPasswordDialog(false);
      setPasswordEvent(null);
      setPasswordStatus('unlocked');
      // 可能需要刷新其他状态
      refreshPasswordStatus();
    };

    const handlePasswordCleared = (eventData) => {
      setPasswordStatus('locked');
      setShowPasswordDialog(false);
      setPasswordEvent(null);
      // 可能需要刷新其他状态
      refreshPasswordStatus();
    };

    // 监听Wails3事件
    if (typeof EventsOn === 'function') {
      EventsOn('password.required', handlePasswordRequired);
      EventsOn('password.invalid', handlePasswordInvalid);
      EventsOn('password.updated', handlePasswordUpdated);
      EventsOn('password.cleared', handlePasswordCleared);
    }

    return () => {
      if (typeof EventsOff === 'function') {
        EventsOff('password.required', handlePasswordRequired);
        EventsOff('password.invalid', handlePasswordInvalid);
        EventsOff('password.updated', handlePasswordUpdated);
        EventsOff('password.cleared', handlePasswordCleared);
      }
    };
  }, [refreshPasswordStatus]);

  /**
   * 设置密码
   * @param {string} password - 新密码
   */
  const setPassword = useCallback(
    async (password) => {
      // 参数验证
      if (!password || typeof password !== 'string' || password.trim() === '') {
        console.error('密码参数无效');
        return false;
      }

      try {
        await AppPasswordService.SetAppPassword(password);
        // 只有成功后才更新全局状态
        updateGlobalState(true);
        return true;
      } catch (error) {
        console.error('设置密码失败:', error);
        // 确保状态正确，重新检查密码状态
        await refreshPasswordStatus();
        return false;
      }
    },
    [updateGlobalState, refreshPasswordStatus]
  );

  /**
   * 验证密码
   * @param {string} password - 要验证的密码
   */
  const verifyPassword = useCallback(async (password) => {
    // 参数验证
    if (!password || typeof password !== 'string') {
      console.error('密码参数无效');
      return false;
    }

    try {
      const result = await AppPasswordService.VerifyAppPassword(password);
      return result;
    } catch (error) {
      console.error('验证密码失败:', error);
      return false;
    }
  }, []);

  /**
   * 初始化应用（使用密码解锁应用）
   * @param {string} password - 用于解锁的密码
   */
  const unlockApplication = useCallback(async (password) => {
    // 参数验证
    if (!password || typeof password !== 'string' || password.trim() === '') {
      console.error('密码参数无效');
      return false;
    }

    try {
      // 使用推荐的解锁方法
      if (AppPasswordService.UnlockApplication) {
        await AppPasswordService.UnlockApplication(password);
      } else {
        // 回退到旧方法（如果新方法不可用）
        await AppPasswordService.VerifyAppPassword(password);
      }
      return true;
    } catch (error) {
      console.error('解锁应用失败:', error);
      return false;
    }
  }, []);

  const changePassword = useCallback(async (oldPassword, newPassword, onProgress) => {
    // 1.参数验证
    if (
      !oldPassword ||
      !newPassword ||
      typeof oldPassword !== 'string' ||
      typeof newPassword !== 'string'
    ) {
      console.error('密码参数无效');
      return false;
    }

    // 2.如果提供了进度回调，使用带进度的方法
    if (onProgress && typeof onProgress === 'function') {
      try {
        // 使用现有的带进度回调的方法
        await AppPasswordService.ChangePasswordWithEvents(oldPassword, newPassword);
        return true;
      } catch (error) {
        console.error('修改密码失败:', error);
        return false;
      }
    } else {
      // 3.不需要进度回调，使用简单的密码修改方法
      try {
        await AppPasswordService.ChangePassword(oldPassword, newPassword);
        return true;
      } catch (error) {
        console.error('修改密码失败:', error);
        return false;
      }
    }
  }, []);

  /**
   * 锁定应用
   */
  const lockApplication = useCallback(async () => {
    try {
      await AppPasswordService.LockApplication();
      return true;
    } catch (error) {
      console.error('锁定应用失败:', error);
      return false;
    }
  }, []);

  /**
   * 获取密码状态
   */
  const getPasswordStatus = useCallback(async () => {
    try {
      const status = await AppPasswordService.GetPasswordStatus();
      setPasswordStatus(status.state);
      return status;
    } catch (error) {
      console.error('获取密码状态失败:', error);
      return { state: 'unknown' };
    }
  }, []);

  return {
    // 现有API
    hasPassword,
    setPassword,
    changePassword,
    verifyPassword,
    unlockApplication,
    refreshPasswordStatus,

    // 新增生命周期管理API
    lockApplication,
    getPasswordStatus,

    // 新增状态
    passwordStatus,
    showPasswordDialog,
    passwordEvent,

    // 事件控制
    hidePasswordDialog: () => setShowPasswordDialog(false),
  };
}
