import { useState, useEffect, useCallback } from 'react';

// ----------------------------------------------------------------------

export function useLocalStorage(key, initialState) {
  const [state, setState] = useState(initialState);

  useEffect(() => {
    try {
      const restored = localStorage.getItem(key);
      if (restored) {
        setState(JSON.parse(restored));
      }
    } catch (error) {
      console.error(error);
    }
  }, [key]);

  const setLocalStorage = useCallback(
    (value) => {
      try {
        localStorage.setItem(key, JSON.stringify(value));
        setState(value);
      } catch (error) {
        console.error(error);
      }
    },
    [key]
  );

  return [state, setLocalStorage];
}
