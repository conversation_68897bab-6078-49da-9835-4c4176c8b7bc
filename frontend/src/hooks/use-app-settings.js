import { useCallback } from 'react';
import { useLocalStorage } from 'minimal-shared/hooks';

// 导入AutoLock重置函数
import { resetAutoLock } from 'src/components/auto-lock/index.jsx';

// 应用设置的存储key
const APP_SETTINGS_STORAGE_KEY = 'app-settings-data';

// 默认应用设置（移除passcode字段，改用AppPasswordService管理）
const defaultAppSettings = {
  app: {
    isInited: false,
  },
  security: {
    autoLock: -1,
  },
  backup: {
    dataPath: '/default/path',
  },
  about: {
    version: '1.0.0',
  },
  // 版本号，用于数据迁移
  version: '1.0.0',
};

/**
 * 应用设置管理hook
 * 使用localStorage进行本地持久化
 * 密码相关功能已抽离到 use-app-password.js
 */
export function useAppSettings() {
  const {
    state: settings,
    setState,
    setField,
  } = useLocalStorage(APP_SETTINGS_STORAGE_KEY, defaultAppSettings);

  /**
   * 更新设置值
   * @param {string} key - 设置键，支持嵌套路径如 'security.autoLock'
   * @param {any} value - 设置值
   */
  const updateSetting = useCallback(
    (key, value) => {
      console.debug('[useAppSettings] Updating setting:', key, value);
      // 支持嵌套路径更新
      if (key.includes('.')) {
        const keys = key.split('.');
        const newSettings = { ...settings };

        // 深度更新嵌套对象
        let current = newSettings;
        for (let i = 0; i < keys.length - 1; i += 1) {
          const currentKey = keys[i];
          if (!current[currentKey] || typeof current[currentKey] !== 'object') {
            current[currentKey] = {};
          }
          current[currentKey] = { ...current[currentKey] };
          current = current[currentKey];
        }

        // 设置最终值
        current[keys[keys.length - 1]] = value;
        setState(newSettings);
      } else {
        // 直接更新顶级属性
        setField(key, value);
      }

      // 如果更新了自动锁屏设置，重置AutoLock组件
      if (key === 'security.autoLock') {
        console.debug('[useAppSettings] AutoLock setting changed, calling resetAutoLock with new value:', value);
        // 直接传递新值给resetAutoLock
        resetAutoLock(value);
      }
    },
    [settings, setState, setField]
  );

  /**
   * 批量更新设置
   * @param {object} updates - 要更新的设置对象
   */
  const updateSettings = useCallback(
    (updates) => {
      setState((prev) => ({
        ...prev,
        ...updates,
      }));
    },
    [setState]
  );

  /**
   * 重置设置到默认值
   */
  const resetSettings = useCallback(() => {
    setState(defaultAppSettings);
  }, [setState]);

  /**
   * 获取特定设置值
   * @param {string} key - 设置键，支持嵌套路径
   * @param {any} defaultValue - 默认值
   */
  const getSetting = useCallback(
    (key, defaultValue = null) => {
      if (key.includes('.')) {
        const keys = key.split('.');
        let current = settings;

        for (const currentKey of keys) {
          if (current && typeof current === 'object' && currentKey in current) {
            current = current[currentKey];
          } else {
            return defaultValue;
          }
        }

        return current;
      }

      return settings[key] ?? defaultValue;
    },
    [settings]
  );

  return {
    settings,
    updateSetting,
    updateSettings,
    resetSettings,
    getSetting,
  };
}
