/**
 * 服务错误处理 Hook
 * 为组件提供统一的服务调用错误处理能力
 */

import { useMemo, useCallback } from 'react';

import { wrapServiceCall, createListErrorHandler } from 'src/utils/error-handler';

/**
 * 服务错误处理 Hook
 * @param {object} options - Hook选项
 * @param {function} options.onInitializationRequired - 需要初始化时的回调
 * @param {boolean} options.showToast - 是否显示toast提示，默认true
 * @param {boolean} options.silent - 是否静默处理，默认false
 * @returns {object} 错误处理工具
 */
export function useServiceErrorHandler(options = {}) {
  const { onInitializationRequired, showToast = true, silent = false } = options;

  /**
   * 包装单个服务调用
   * @param {Promise} serviceCall - 服务调用Promise
   * @param {object} callOptions - 调用特定选项
   * @returns {Promise} 包装后的Promise
   */
  const withErrorHandling = useCallback(
    (serviceCall, callOptions = {}) =>
      wrapServiceCall(serviceCall, {
        onInitializationRequired,
        showToast,
        silent,
        ...callOptions,
      }),
    [onInitializationRequired, showToast, silent]
  );

  /**
   * 为列表数据获取创建错误处理函数
   * 专门处理列表组件的getData调用，静默处理NotInitialized错误
   * @param {any} defaultValue - 错误时返回的默认值，默认[[], 0]
   * @returns {function} 列表错误处理函数
   */
  const createListDataHandler = useCallback(
    (defaultValue = [[], 0]) => {
      const errorHandler = createListErrorHandler({
        onInitializationRequired,
      });

      return async (serviceCall) => {
        try {
          return await serviceCall;
        } catch (error) {
          const result = errorHandler(error);

          // 如果是NotInitialized错误，静默返回默认值
          if (result.requiresInitialization) {
            return defaultValue;
          }

          // 其他错误重新抛出（可能需要组件层面的特殊处理）
          throw error;
        }
      };
    },
    [onInitializationRequired]
  );

  /**
   * 为表单提交创建错误处理函数
   * 处理表单提交的错误，显示相应的提示信息
   * @param {object} formOptions - 表单特定选项
   * @returns {function} 表单错误处理函数
   */
  const createFormHandler = useCallback(
    (formOptions = {}) =>
      (serviceCall) =>
        withErrorHandling(serviceCall, {
          showToast: true,
          rethrowUnhandled: true, // 表单错误通常需要组件处理
          ...formOptions,
        }),
    [withErrorHandling]
  );

  return {
    withErrorHandling,
    createListDataHandler,
    createFormHandler,
  };
}

/**
 * 专用于列表组件的错误处理 Hook
 * 简化列表组件中的错误处理逻辑
 * @param {object} options - Hook选项
 * @returns {object} 列表错误处理工具
 */
export function useListErrorHandler(options = {}) {
  const { createListDataHandler } = useServiceErrorHandler({
    silent: true,
    showToast: false,
    ...options,
  });

  // 使用 useMemo 缓存 handleListData，确保稳定性
  const handleListData = useMemo(() => createListDataHandler(), [createListDataHandler]);

  return {
    handleListData,
  };
}

/**
 * 专用于表单组件的错误处理 Hook
 * 简化表单组件中的错误处理逻辑
 * @param {object} options - Hook选项
 * @returns {object} 表单错误处理工具
 */
export function useFormErrorHandler(options = {}) {
  const { createFormHandler } = useServiceErrorHandler({
    showToast: true,
    ...options,
  });

  return {
    handleFormSubmit: createFormHandler(),
  };
}
