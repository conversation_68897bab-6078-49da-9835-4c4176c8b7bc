import { useCallback } from 'react';
import { useBoolean } from 'minimal-shared/hooks';

import InitializationDialog from 'src/components/initialization/dialog';
import { InitializationPromptDialog } from 'src/components/initialization-prompt-dialog';

import { useAppPassword } from './use-app-password';

/**
 * 统一的初始化管理Hook
 * 替代原有的usePasswordGuard，提供完整的初始化流程管理
 */
export function useInitializationManager() {
  const { hasPassword } = useAppPassword();

  // UI状态管理
  const initPromptDialog = useBoolean(false);
  const initDialog = useBoolean(false);

  // 事件处理函数
  const handleInitializationRequired = useCallback(() => {
    initPromptDialog.onTrue();
  }, [initPromptDialog]);

  const handleInitializationConfirm = useCallback(() => {
    initPromptDialog.onFalse();
    initDialog.onTrue();
  }, [initPromptDialog, initDialog]);

  const handleInitializationComplete = useCallback(() => {
    initDialog.onFalse();
    // hasPassword会在InitializationDialog内部更新，这里不需要手动处理
  }, [initDialog]);

  const handleClose = useCallback(() => {
    initPromptDialog.onFalse();
    initDialog.onFalse();
  }, [initPromptDialog, initDialog]);

  /**
   * 包装需要初始化的操作（替代usePasswordGuard的withPasswordCheck）
   * @param {Function} callback - 需要执行的回调函数
   * @returns {Function} 包装后的函数
   */
  const wrapAction = useCallback(
    (callback) =>
      (...args) => {
        if (!hasPassword) {
          handleInitializationRequired();
          return undefined;
        }

        if (callback) {
          return callback(...args);
        }

        return undefined;
      },
    [hasPassword, handleInitializationRequired]
  );

  /**
   * 直接检查初始化状态（替代usePasswordGuard的checkPassword）
   * @returns {boolean} 是否已初始化
   */
  const checkInitialization = useCallback(() => {
    if (!hasPassword) {
      handleInitializationRequired();
      return false;
    }
    return true;
  }, [hasPassword, handleInitializationRequired]);

  /**
   * 统一的初始化UI渲染
   * @returns {JSX.Element} 初始化相关的对话框组件
   */
  const renderInitialization = useCallback(
    () => (
      <>
        {/* 初始化提示对话框 */}
        <InitializationPromptDialog
          open={initPromptDialog.value}
          onClose={handleClose}
          onConfirm={handleInitializationConfirm}
        />

        {/* 完整初始化对话框 */}
        <InitializationDialog
          open={initDialog.value}
          onClose={handleClose}
          onComplete={handleInitializationComplete}
        />
      </>
    ),
    [
      initPromptDialog.value,
      initDialog.value,
      handleClose,
      handleInitializationConfirm,
      handleInitializationComplete,
    ]
  );

  return {
    hasPassword, // 全局初始化状态
    wrapAction, // 操作包装函数，替代withPasswordCheck
    checkInitialization, // 状态检查函数，替代checkPassword
    renderInitialization, // 统一的UI渲染函数
  };
}
