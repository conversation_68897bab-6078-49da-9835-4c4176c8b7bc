import dayjs from 'dayjs';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';

import { toast } from 'src/components/snackbar';

import {
  fallbackLng,
  detectSystemLanguage,
  changeLangMessages as messages,
} from './locales-config';
import { allLangs } from './all-langs';

// ----------------------------------------------------------------------

export function useTranslate(ns = 'common') {
  const { t: originalT, i18n } = useTranslation(ns);

  // 增强t函数，支持自动解析命名空间
  const t = useCallback(
    (key, options = {}) => {
      // 检查key是否包含命名空间（通过冒号分隔）
      if (key.includes(':')) {
        const [namespace, actualKey] = key.split(':', 2);
        // 使用指定的命名空间，确保正确加载
        try {
          return i18n.t(actualKey, { ...options, ns: namespace });
        } catch (error) {
          console.warn(`Translation failed for ${namespace}:${actualKey}`, error);
          return key; // 返回原始key作为fallback
        }
      }
      // 没有指定命名空间，使用默认的
      return originalT(key, options);
    },
    [originalT, i18n]
  );

  const fallback = allLangs.filter((lang) => lang.value === fallbackLng)[0];

  const currentLang = allLangs.find((lang) => lang.value === i18n.resolvedLanguage);

  const onChangeLang = useCallback(
    async (newLang) => {
      try {
        const langChangePromise = i18n.changeLanguage(newLang);

        const currentMessages = messages[newLang] || messages.en;

        toast.promise(langChangePromise, {
          loading: currentMessages.loading,
          success: () => currentMessages.success,
          error: currentMessages.error,
          duration: 1500,
        });

        if (currentLang) {
          dayjs.locale(currentLang.adapterLocale);
        }
      } catch (error) {
        console.error(error);
      }
    },
    [currentLang, i18n]
  );

  // 重置语言到系统默认值
  const onResetToSystemLang = useCallback(async () => {
    try {
      console.log('🔄 Resetting language to system default...');

      // 清除 localStorage 中的语言设置
      try {
        localStorage.removeItem('i18nextLng');
        console.log('🗑️ Cleared stored language preference');
      } catch (error) {
        console.warn('⚠️ Failed to clear stored language preference:', error);
      }

      // 重新检测系统语言
      const systemLang = await detectSystemLanguage();
      console.log('🔍 Detected system language:', systemLang);

      // 切换到检测到的系统语言
      const langChangePromise = i18n.changeLanguage(systemLang);

      const currentMessages = messages[systemLang] || messages.en;

      toast.promise(langChangePromise, {
        loading: currentMessages.loading,
        success: () => `${currentMessages.success} (${systemLang})`,
        error: currentMessages.error,
        duration: 2000,
      });

      // 更新 dayjs 语言设置
      const newLang = allLangs.find((lang) => lang.value === systemLang);
      if (newLang) {
        dayjs.locale(newLang.adapterLocale);
      }

      return systemLang;
    } catch (error) {
      console.error('❌ Failed to reset to system language:', error);
      toast.error('Failed to reset language');
      throw error;
    }
  }, [i18n]);

  return {
    t,
    i18n,
    onChangeLang,
    onResetToSystemLang,
    currentLang: currentLang ?? fallback,
  };
}
