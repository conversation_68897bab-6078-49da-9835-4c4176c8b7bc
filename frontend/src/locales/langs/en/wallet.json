{"title": "Wallet Management", "empty": "No Wallet", "create": "Create", "import": "Import", "please": "Please", "or": "or", "wallet": "wallet", "import_mnemonic": "Import mnemonic", "import_mnemonic_tip": "Please enter the mnemonic (one per line)", "manual_input": "Manual input", "upload_file": "Upload file", "upload_file_tip": "Please upload a file containing the mnemonic, supported file types: .csv, .xls, .xlsx, .txt (size limit: 2MB, max 100 lines)", "generate_title": "Batch generate wallet addresses", "generate_tip": "The entire wallet generation process is completed locally, and your data is encrypted and saved with your key. No one can decrypt and view your data.", "choice_network": "Network", "choice_mnemonic_length": "Mnemonic length", "generate_wallet_count": "Generate wallet count", "maxWalletCountError": "Cannot exceed {{max}} wallets", "walletCountHelper": "Maximum {{max}} wallets can be generated each time", "generateFailed": "Generate wallet failed", "generatingProgress": "Generation progress", "generating_progress": "Generating wallet {{current}}/{{total}}", "generating": "Generating...", "immediate_generation": "Generation", "label_group": "Group", "label_name": "Name", "label_remark": "Remark", "lable_network": "Network", "lable_address": "Address", "lable_privatekey": "Private<PERSON><PERSON>", "lable_publicKey": "PublicKey", "words": "words", "createSuccess": "Successfully created {{count}} wallets"}