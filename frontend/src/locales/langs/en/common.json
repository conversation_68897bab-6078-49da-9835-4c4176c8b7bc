{"demo": {"lang": "English"}, "action": {"action": "Action", "add": "Add", "create": "Create", "delete": "Delete", "import": "Import", "importing": "Importing...", "export": "Export", "cancel": "Cancel", "save": "Save", "edit": "Edit", "confirm": "Confirm", "setting": "Setting...", "addIndividual": "Add Individual", "batchAdd": "<PERSON><PERSON> Add", "selectAll": "Select All", "unselectAll": "Unselect All"}, "table": {"rowsPerPage": "Rows per page", "showing": "Showing", "of": "of", "columnSettings": "<PERSON>umn <PERSON>"}, "tips": {"delete": "Are you sure want to delete?", "deleteConfirm": "Are you sure you want to delete ", "copied": "Copied!", "copy": "Copy", "hide-password": "Hide Password", "show-password": "Show Password", "copy-password": "Copy Password", "search": "Search...", "rows_per_page": "Rows per page", "quick_edit": "Quick Edit", "delete-success": "Delete Success!", "delete-error": "Delete Error", "add-success": "Add Success!", "add-error": "Add Error", "update-success": "Update Success!", "update-error": "Update Error", "fetch-error": "<PERSON><PERSON>r", "no_data": "No Data"}, "promise": {"updating": "Updating...", "update_success": "Update Success!", "update_error": "Update Error"}, "message": {"loadFailed": "Failed to load data", "deleteSuccess": "Delete successful", "deleteFailed": "Delete failed", "updateSuccess": "Update successful", "updateFailed": "Update failed", "passwordRequired": "Please set up app password first to {{action}}", "passwordMismatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters", "passwordSetSuccess": "Password set successfully", "passwordSetFailed": "Failed to set password"}, "security": {"password": "Password", "confirmPassword": "Confirm Password", "setupPassword": "Set Up App Password", "setupPasswordDesc": "Please set up an app password to protect your data", "passwordSetupTips": "The password is used to encrypt your sensitive data. It cannot be recovered once set, please keep it safe."}, "validation": {"required": "This field is required", "minLength": "Minimum {{min}} characters required", "passwordMismatch": "Passwords do not match"}, "initialization": {"required": "App Initialization Required", "subtitle": "First-time setup needed", "description": "To {{action}}, you need to complete the app initialization first. This process only takes a few minutes.", "benefits": "Initialization will set up app password and create a secure data storage environment to protect your data.", "stepsTitle": "Initialization includes the following steps:", "step1": "Set up app password to protect your data", "step2": "Create or import your first wallet", "step3": "Configure basic app settings", "startNow": "Start Initialization"}, "loading": "Loading...", "createdAt": "Created At"}