version: '3'

vars:
  APP_NAME: "A8Tools"

tasks:
  go:mod:tidy:
    summary: Runs `go mod tidy`
    internal: true
    cmds:
      - go mod tidy

  install:frontend:deps:
    summary: Install frontend dependencies
    dir: frontend
    sources:
      - package.json
      - package-lock.json
    generates:
      - node_modules/*
    preconditions:
      - sh: yarn version
        msg: "Looks like yarn isn't installed. Yarn is part of the Node installer: https://nodejs.org/en/download/"
    cmds:
      - yarn install --ignore-scripts

  build:frontend:
    summary: Build the frontend project
    dir: frontend
    sources:
      - "**/*"
    generates:
      - dist/*
    deps:
      - task: install:frontend:deps
      - task: generate:bindings
        vars:
          PRODUCTION: '{{.PRODUCTION}}'
    cmds:
      - yarn run {{.BUILD_COMMAND}}
    env:
      PRODUCTION: '{{.PRODUCTION | default "false"}}'
    vars:
      BUILD_COMMAND: '{{if eq .PRODUCTION "true"}}build{{else}}build{{end}}'


  generate:bindings:
    label: generate:bindings (BUILD_FLAGS={{.BUILD_FLAGS}})
    summary: Generates bindings for the frontend
    deps:
      - task: go:mod:tidy
    sources:
      - "**/*.[jt]s"
      - exclude: frontend/**/*
      - frontend/bindings/**/*  # Rerun when switching between dev/production mode causes changes in output
      - "**/*.go"
      - go.mod
      - go.sum
    generates:
      - frontend/bindings/**/*
    cmds:
      - wails3 generate bindings -f '{{.BUILD_FLAGS}}' -clean=true 
    vars:
      BUILD_FLAGS: '{{if eq .PRODUCTION "true"}}-tags production{{else}}-tags debug{{end}}'
    env:
      PRODUCTION: '{{.PRODUCTION | default "false"}}'

  generate:icons:
    summary: Generates Windows `.ico` and Mac `.icns` files from an image
    dir: build
    sources:
      - "appicon.png"
    generates:
      - "icons.icns"
      - "icons.ico"
    cmds:
      - wails3 generate icons -input appicon.png -macfilename darwin/icons.icns -windowsfilename windows/icons.ico

  dev:frontend:
    summary: Runs the frontend in development mode
    dir: frontend
    deps:
      - task: install:frontend:deps
    cmds:
      - npm run dev -- --port {{.VITE_PORT}} --strictPort

  update:build-assets:
    summary: Updates the build assets
    dir: build
    cmds:
      - wails3 update build-assets -name "{{.APP_NAME}}" -binaryname "{{.APP_NAME}}" -config config.yml -dir .