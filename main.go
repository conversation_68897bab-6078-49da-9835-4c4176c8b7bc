package main

import (
	"context"
	"crypto/rand"
	"embed"
	_ "embed"
	"fmt"
	"log"
	"log/slog"
	"os"

	"a8.tools/backend"
	"a8.tools/backend/config"
	"a8.tools/backend/pkg/password"
	"a8.tools/backend/services/account"
	"a8.tools/backend/services/setting"
	"a8.tools/backend/services/wallet"
	"github.com/wailsapp/wails/v3/pkg/application"
)

// Wails uses Go's `embed` package to embed the frontend files into the binary.
// Any files in the frontend/dist folder will be embedded into the binary and
// made available to the frontend.
// See https://pkg.go.dev/embed for more information.

//go:embed all:frontend/dist
var assets embed.FS

// WailsLoggerHandler is a custom slog.Handler that filters logs to show only frontend service calls.
// It shows frontend service method calls and optionally their parameters.
type WailsLoggerHandler struct {
	slog.Handler
	showParameters bool // 控制是否显示调用参数
}

// NewWailsLoggerHandler 创建一个新的 Wails 日志处理器
func NewWailsLoggerHandler(handler slog.Handler, showParameters bool) *WailsLoggerHandler {
	return &WailsLoggerHandler{
		Handler:        handler,
		showParameters: showParameters,
	}
}

// Handle overrides the default Handle method to filter logs.
func (h *WailsLoggerHandler) Handle(ctx context.Context, r slog.Record) error {
	// 过滤掉资源服务器请求
	if r.Message == "Asset Request:" {
		return nil
	}

	// 检查是否是服务调用
	isServiceCall := false
	serviceName := ""
	methodName := ""
	var params []slog.Attr

	r.Attrs(func(a slog.Attr) bool {
		switch a.Key {
		case "service":
			serviceName = a.Value.String()
			isServiceCall = true
		case "method":
			methodName = a.Value.String()
		case "params":
			if h.showParameters {
				params = append(params, a)
			}
		case "wails-frontend":
			// 保留前端相关的日志，但我们会进一步过滤
			return true
		default:
			// 保留其他可能有用的属性
			if h.showParameters {
				params = append(params, a)
			}
		}
		return true
	})

	// 只显示服务调用
	if isServiceCall && serviceName != "" && methodName != "" {
		// 构建自定义日志消息
		message := fmt.Sprintf("🔧 Service Call: %s.%s", serviceName, methodName)

		if h.showParameters && len(params) > 0 {
			// 创建新的日志记录，包含参数
			newRecord := slog.NewRecord(r.Time, r.Level, message, r.PC)
			for _, param := range params {
				newRecord.AddAttrs(param)
			}
			return h.Handler.Handle(ctx, newRecord)
		} else {
			// 创建简单的日志记录，不包含参数
			newRecord := slog.NewRecord(r.Time, r.Level, message, r.PC)
			return h.Handler.Handle(ctx, newRecord)
		}
	}

	// 过滤掉所有其他日志
	return nil
}

// generateRandomEncryptionKey 生成一个32字节的随机加密密钥
func generateRandomEncryptionKey() [32]byte {
	var key [32]byte
	_, err := rand.Read(key[:])
	if err != nil {
		log.Fatal("Failed to generate random encryption key:", err)
	}
	return key
}

// 生成随机加密密钥
var encryptionKey = generateRandomEncryptionKey()

var mainWindow *application.WebviewWindow

// main function serves as the application's entry point. It initializes the application, creates a window,
// and starts a goroutine that emits a time-based event every second. It subsequently runs the application and
// logs any error that might occur.
func startApp() {

	// Create a new Wails application by providing the necessary options.
	// Variables 'Name' and 'Description' are for application metadata.
	// 'Assets' configures the asset server with the 'FS' variable pointing to the frontend files.
	// 'Bind' is a list of Go struct instances. The frontend has access to the methods of these instances.
	// 'Mac' options tailor the application when running an macOS.

	// 根据构建标签确定安全的日志级别和参数显示
	var logLevel slog.Level
	var showParameters bool

	if config.IsDebugBuild {
		logLevel = slog.LevelInfo // 调试模式：启用详细日志
		showParameters = true     // 调试模式：显示调用参数
		log.Println("Debug build - Service call logging enabled with parameters")
	} else {
		logLevel = slog.LevelInfo // 生产模式：只显示服务调用，不显示错误
		showParameters = false    // 生产模式：不显示调用参数
		log.Println("Release build - Service call logging enabled without parameters")
	}

	// 创建一个自定义的 logger，用于只显示前端服务调用
	baseHandler := slog.NewTextHandler(os.Stderr, &slog.HandlerOptions{
		Level: logLevel,
	})
	handler := NewWailsLoggerHandler(baseHandler, showParameters)
	customLogger := slog.New(handler)

	app := application.New(application.Options{
		Name:        "A8 Tools",
		Description: "A8 Tools",

		Assets: application.AssetOptions{
			Handler: application.BundledAssetFileServer(assets),
		},
		Services: []application.Service{
			application.NewService(account.NewMailService()),
			application.NewService(account.NewDiscordService()),
			application.NewService(account.NewTelegramService()),
			application.NewService(account.NewProxyService()),
			application.NewService(account.NewXService()),
			application.NewService(password.NewPasswordApplicationServiceForWails()),
			application.NewService(wallet.NewWalletService()),
			application.NewService(setting.NewSettingService()),
		},

		Mac: application.MacOptions{
			ApplicationShouldTerminateAfterLastWindowClosed: true,
		},
		Windows: application.WindowsOptions{},
		Linux:   application.LinuxOptions{},
		OnShutdown: func() {

		},
		PanicHandler: func(details *application.PanicDetails) {
			log.Printf("Panic: %v", details.Error)
		},
		SingleInstance: &application.SingleInstanceOptions{
			UniqueID: "tools.a8.app",
			OnSecondInstanceLaunch: func(data application.SecondInstanceData) {
				if mainWindow != nil {
					mainWindow.Restore()
					mainWindow.Focus()
				}
			},
			EncryptionKey: encryptionKey,
		},
		Logger:   customLogger, // 使用自定义的 logger
		LogLevel: logLevel,     // LogLevel 仍然需要设置，以控制 Wails 内部组件的日志级别
	})

	// 同步初始化后端（确保依赖注入在服务使用前完成）
	backend.Init(app)

	// Create a new window with the necessary options.
	// 'Title' is the title of the window.
	// 'Mac' options tailor the window when running on macOS.
	// 'BackgroundColour' is the background colour of the window.
	// 'URL' is the URL that will be loaded into the webview.
	mainWindow = app.Window.NewWithOptions(application.WebviewWindowOptions{
		Title:  "A8 Tools",
		Width:  1600,
		Height: 1000,
		Mac: application.MacWindow{
			InvisibleTitleBarHeight: 50,
			Backdrop:                application.MacBackdropTranslucent,
			TitleBar:                application.MacTitleBarHiddenInset,
		},
		BackgroundColour: application.NewRGB(27, 38, 54),
		URL:              "/",
		DevToolsEnabled:  config.IsDebugBuild, // 使用统一的构建配置
	})

	// Run the application. This blocks until the application has been exited.
	err := app.Run()

	// If an error occurred while running the application, log it and exit.
	if err != nil {
		log.Fatal(err)
	}
}

func main() {

	// 启动应用
	startApp()
}
